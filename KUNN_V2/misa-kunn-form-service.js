const { PARTNER_CODE, CONTRACT_TYPE, ERROR_CODE, TYPE_COLLECTION, SERVICE_NAME } = require("../const/definition");
const loanContractService = require("../services/loan-contract-service");
const { KUNN_STATUS, KUNN_STEP, caseStatusCode, WORKFLOW_STAGE } = require("../const/caseStatus");
const kunnRepo = require("../repositories/kunn-repo");
const { STATUS } = require("../const/caseStatus");
const moment = require("moment-timezone");
moment().tz("Asia/Ho_Chi_Minh").format();
const { getLoanContract } = require("../repositories/loan-contract-repo");
const { getLoanCustomer } = require("../repositories/loan-customer-repo");
const { checkAvailableAmountApi, cancelKunnApi, checkRemainPrinAmountApi } = require("../apis/lms-api");
const helper = require("../utils/helper");
const { getProductDetailByCodeApi } = require("../apis/product-api");
const { RATE_TYPE } = require("../const/product");
const antiFraudService = require("../services/anti-fraud");
const disbursementInfoRepo = require("../repositories/kunn-disbursement-info-repo");
const uuid = require("uuid");

const { CIC_CONTRACT_TYPE } = require("../const/cusType");
const { downloadAndPushDocS3, callBackKunnCic } = require("../services/sme-misa-v2");
const loanContractDocumentRepo = require("../repositories/document");
const offerRepo = require("../repositories/offer");
const { convertEvfLov, getValueCode_v3, getValueCode_v2, parseValueCode } = require("../utils/masterdataService");
const { saveStepLog } = require("../repositories/logging-repo");
const { STEP, ADDRESS_CODE_TYPE, DOC_GROUP, LOAN_CUSTOMER_SUBJECT } = require("../const/variables-const");
const loanRepo = require("../repositories/loan-contract-repo");
const { findOne } = require("../utils/sqlHelper");
const loanBusinessOwnerRepo = require("../repositories/loan-business-owner-repo");
const loanCustomerShareholderRepo = require("../repositories/loan-customer-shareholders-repo");
const { checkBankAccountInfoApi } = require("../apis/disbursement-api");
const { MISA_ERROR_CODE } = require("../const/response-const");
const loggingRepo = require("../repositories/logging-repo");
const { callbackKunnCancelApi } = require("../apis/misa-api");
const { genMisaKunnTTRV, genMisaKunnPaymentSchedule } = require("../utils/misa-file-handler");
const { getFullyAddress } = require("../utils/common");

const USER = {
  MISA: "misa.api",
  SYSTEM: "system",
};

const ANTI_FRAUD_PERSON_TYPE = {
  PER: "PER",
  REP: "REP",
  PARTNER: "PARTNER",
};

const saveKunn = async (kunnData, context = {}) => {
  const convertedKunn = convertBody(kunnData);

  const saveRs = await kunnRepo.insertKunn(helper.convertCamelToSnake(convertedKunn));
  if (!saveRs) {
    helper.throwServerError(`Can not save kunnId`);
  }
  convertedKunn.disbursementInfos = kunnData.disbursementInfos;
  const { disbursementInfos, loanApplicationFile } = kunnData;
  await createOffer(convertedKunn);
  //save loanApplicationFile
  const s3Result = await downloadAndPushDocS3(loanApplicationFile, kunnData.debtContractNumber, context);
  const fileKey = s3Result?.Key;
  const fileLocation = s3Result?.Location;
  await loanContractDocumentRepo.insert({
    contractNumber: kunnData.contractNumber,
    kunnContractNumber: kunnData.kunnId,
    docType: loanApplicationFile?.docType,
    docId: uuid.v4(),
    fileKey,
    fileName: loanApplicationFile.fileName,
    url: fileLocation,
    typeCollection: TYPE_COLLECTION.DOC_EXTERNAL,
    docGroup: DOC_GROUP.MISA_HM_DISBURSAL,
  });
  //save disbursement info
  const tasks = [];
  for (const info of disbursementInfos) {
    tasks.push(saveDisbursementInfo(kunnData, info, context));
  }
  await Promise.all(tasks);
  return convertedKunn;
};

const saveDisbursementInfo = async (kunnData, info, context = {}) => {
  try {
    const kunnId = kunnData.kunnId;
    const infoRs = await disbursementInfoRepo.save({ ...info, kunnId });
    if (!infoRs) helper.throwServerError(`cannot save disbursement info ${info}`);
    //save doc
    const docTasks = [];
    for (const doc of info.collectingDocs) {
      const s3Result = await downloadAndPushDocS3(doc.file, kunnData.debtContractNumber, context);
      const fileLocation = s3Result.Location;
      const fileKey = s3Result.Key;
      docTasks.push(
        loanContractDocumentRepo.insert({
          contractNumber: kunnData.contractNumber,
          kunnContractNumber: kunnId,
          docType: doc.file.docTYoe || "VAT",
          docId: uuid.v4(),
          fileKey,
          fileName: doc.file.fileName,
          url: fileLocation,
          typeCollection: TYPE_COLLECTION.DOC_EXTERNAL,
          disbursementInfoId: infoRs.id,
          docGroup: DOC_GROUP.MISA_HM_DISBURSAL,
        })
      );
    }
    for (const doc of info.scanDocs) {
      const s3Result = await downloadAndPushDocS3(doc, kunnData.debtContractNumber, context);
      const fileLocation = s3Result.Location;
      const fileKey = s3Result.Key;
      docTasks.push(
        loanContractDocumentRepo.insert({
          contractNumber: kunnData.contractNumber,
          kunnContractNumber: kunnId,
          docType: doc.docType || "SCAN",
          docId: uuid.v4(),
          fileKey,
          fileName: doc.fileName,
          url: fileLocation,
          typeCollection: TYPE_COLLECTION.DOC_EXTERNAL,
          disbursementInfoId: infoRs.id,
          docGroup: DOC_GROUP.MISA_HM_DISBURSAL,
        })
      );
    }

    await Promise.all(docTasks);
  } catch (error) {
    console.log(`[MISA][KUNN][V2]saveDisbursementInfo ${JSON.stringify(info)}, error ${error}`);
    throw error;
  }
};

const updateKunn = async (kunnId, values) => {
  values = helper.convertCamelToSnake(values);
  return kunnRepo.update(kunnId, values);
};
const convertBody = (kunnData) => {
  const convertedBody = {
    status: KUNN_STATUS.RECIEVE,
    kunnId: kunnData.debtContractNumber || kunnData.kunnId,
    contractNumber: kunnData.contractNumber,
    esignContract: kunnData.esignContract,
    esignedContract: null,
    availableAmount: kunnData.availableAmount,
    requestId: kunnData.requestId,
    partnerCode: PARTNER_CODE.MISA,
    customerName: kunnData.companyName,
    identityCardId: kunnData.representations?.[0]?.idNumber,
    method: null,
    withDrawAmount: kunnData.totalWithdrawAmt,
    withDrawTenor: null,
    tenor: kunnData.tenor,
    ir: kunnData.ir,
    dateApproval: null,
    billDay: kunnData.monthlyIntPaymentDate,
    kunnCode: kunnData.kunnCode,
    capitalNeed: null,
    selfFinancing: null,
    otherCapital: null,
    fundingFromEc: null,
    repaymentSource: null,
    fixedAsset: null,
    planAsset: null,
    numberBussinessAsset: null,
    startDate: kunnData.startDate,
    endDate: kunnData.endDate,
    lmsType: kunnData.lmsType,
    acronymSmeName: kunnData.companyName,
    step: KUNN_STEP.RECIEVE,
    apiVersion: "v2",
  };

  return convertedBody;
};

const validateAmount = async (loanContract, kunnData, kunnCodeInfo) => {
  let { disbursementInfos, totalWithdrawAmt } = kunnData;
  const { minAmount } = kunnCodeInfo;
  totalWithdrawAmt = Number(totalWithdrawAmt);
  const approvedAmt = Number(loanContract.approvalAmt);
  // const avalibleAmountRs = await checkAvailableAmountApi(kunnData.contractNumber);
  const remainAmountData = await checkRemainPrinAmountApi(loanContract.custId, PARTNER_CODE.MISA);
  if (helper.isNullOrEmptyV3(remainAmountData?.totalPrinAmount)) {
    helper.throwServerError(`Can not get remainAmountData`);
  }
  const totalPrinAmount = Number(remainAmountData.totalPrinAmount);
  const availableAmount = approvedAmt - totalPrinAmount;
  const totalAmtInList = disbursementInfos.reduce((sum, info) => sum + Number(info.amount), 0);
  if (totalWithdrawAmt != totalAmtInList || totalAmtInList > availableAmount || totalWithdrawAmt > availableAmount) {
    helper.throwBadReqError("totalWithdrawAmt", `Tổng số tiền giải ngân vượt quá hạn mức hoặc khác tổng số tiền trong danh sách giải ngân`, MISA_ERROR_CODE.E406);
  }
  if (Number(minAmount) > totalWithdrawAmt) {
    helper.throwBadReqError("totalWithdrawAmt", `Tổng số tiền giải ngân tối thiểu theo sản phẩm là ${minAmount}`, MISA_ERROR_CODE.E406);
  }
  kunnData.availableAmount = availableAmount;
  return true;
};

const validateBankInfo = async (kunnId, info) => {
  const bankName = await getValueCode_v3(info.bankCode, "BANK");
  if (!bankName) {
    helper.throwBadReqError("disbursementInfos.bankCode", `Mã ngân hàng ${info.bankCode} chưa được hỗ trợ`, MISA_ERROR_CODE.E408);
  }
  info.bankName = bankName;
  //check account info
  const checkAcountInfo = await checkBankAccountInfoApi({
    bankCode: info.bankCode,
    accountNumber: info.bankAccount,
    customerName: info.accountName,
    kunnId,
  });
  if (checkAcountInfo?.actualName) {
    //handle here
    info.verifyAccountName = checkAcountInfo.actualName;
    info.verifyAccountInfo = JSON.stringify(checkAcountInfo);
    if (helper.upperAndRemoveTrim(helper.removeVietnameseTones(info.accountName)) !== helper.upperAndRemoveTrim(helper.removeVietnameseTones(checkAcountInfo.actualName))) {
      console.log(`[validateBankInfo]kunnId: ${kunnId} not match, accountName: ${info.accountName}, actualName: ${checkAcountInfo.actualName}`);
      helper.throwBadReqError(`disbursementInfos.bankAccount`, `bankAccount ${info.bankAccount} invalid: ${info.accountName} not match ${info.verifyAccountName}`, MISA_ERROR_CODE.E409);
    }
  }
  return info;
};

const validateDisbursementInfo = async (kunnId, infos) => {
  if (infos?.length <= 0) {
    helper.throwBadReqError("disbursementInfos", `Danh sách giải ngân trống`, MISA_ERROR_CODE.E407);
  }
  if (helper.hasDuplicateByFields(infos, ["bankAccount", "bankCode"])) {
    helper.throwBadReqError("disbursementInfos", `Thông tin giải ngân trùng lặp`, MISA_ERROR_CODE.E400);
  }
  const bankInfoTask = [];
  infos.forEach((e) => {
    bankInfoTask.push(validateBankInfo(kunnId, e));
  });
  return await Promise.all(bankInfoTask);
};

const handleCreateKunn = async (kunnId, kunnData, context = {}) => {
  let kunn;
  const contractNumber = kunnData.contractNumber;
  kunnData.debtContractNumber = kunnId;
  kunnData.kunnId = kunnId;
  try {
    console.log(`[MISA][KUNN][V2][createKunn] with payload ${JSON.stringify(kunnData)}`);

    kunnData = await convertEvfLov({
      partnerCode: PARTNER_CODE.MISA,
      convertObject: kunnData,
    });
    if (!kunnData) {
      helper.throwServerError(`Cannot mapping code`);
    }

    // Check if loanContract is satisfied
    const loanContract = helper.snakeToCamel(await getLoanContract(contractNumber));
    if (loanContract?.status != STATUS.ACTIVATED || loanContract?.partnerCode != PARTNER_CODE.MISA) {
      helper.throwBadReqError("contractNumber", `contractNumber ${contractNumber} not found or invalid`, MISA_ERROR_CODE.E401);
    }
    if (!loanContractService.isContractLockActive(loanContract)) {
      helper.throwBadReqError("contractNumber", `contractNumber ${contractNumber} was locked`, MISA_ERROR_CODE.E415); // TODO: Cần confirm lại
    }
    loanContract.loanCustomer = helper.snakeToCamel(await getLoanCustomer(contractNumber));
    const contractType = loanContract.contractType === CONTRACT_TYPE.CREDIT_LINE ? "HM" : "VM";
    let kunnCode = `KU_SME_MISA_${contractType}_STANDARD`;
    const productCode = ["SME_MISA_HM_VIP", "SME_MISA_HM_PLATINUM", "SME_MISA_VM_PLATINUM", "SME_MISA_HM_PREMIUM", "SME_MISA_VM_VIP", "SME_MISA_HM_STANDARD", "SME_MISA_VM_STANDARD"];
    if (!productCode.includes(loanContract.productCode)) {
      kunnCode = `KU_` + loanContract.productCode;
    }
    // req.body.tenor = loanContract?.approval_tenor_kunn;
    // if(contractType==='VM') req.body.tenor = loanContract?.approval_tenor;
    // const misaKU = new misaKUNN(req,res,kunnCode);
    // misaKU.createDiburRequest2();
    const productInfo = await getProductDetailByCodeApi(loanContract.productCode);
    if (!productInfo || !productInfo?.kunn) helper.throwServerError("can not get productInfo or kunnCode info");
    const kunnCodeInfo = productInfo.kunn;

    kunnData.productInfo = productInfo;
    kunnData.kunnCode = kunnCode;
    kunnData.lmsType = loanContract.contractType;
    kunnData.tenor = Number(kunnCodeInfo.tenor);
    kunnData.monthlyIntPaymentDate = kunnData.monthlyIntPaymentDate.length == 1 ? `0${kunnData.monthlyIntPaymentDate}` : kunnData.monthlyIntPaymentDate;
    //check can create kunn
    kunnData.endDate = moment().add(kunnData.tenor, "months").format("yyyy-MM-DD");
    kunnData.startDate = moment().format("yyyy-MM-DD");

    const canCreateKunnRs = await canCreateKunn(contractNumber, true);
    if (!canCreateKunnRs) {
      helper.throwBadReqError(`contractNumber`, `Has Kunn processing`, MISA_ERROR_CODE.E411);
    }
    if (productInfo) {
      const ir = productInfo?.rates?.find((val) => val?.rateType === RATE_TYPE.NORMAL)?.intRateVal;
      if (!ir) {
        helper.throwServerError(`can not get rate type`);
      }
      kunnData.ir = Number(ir) / 100;
    }
    await validateAmount(loanContract, kunnData, kunnCodeInfo);
    //return received
    kunnData.disbursementInfos = await validateDisbursementInfo(kunnId, kunnData.disbursementInfos);
    kunn = await saveKunn(kunnData, context);

    const [loanBusinessOwners, loanCustomerShareholders] = await Promise.all([loanBusinessOwnerRepo.findByContractNumber(contractNumber), loanCustomerShareholderRepo.findByContractNumber(contractNumber)]);
    loanContract.loanBusinessOwners = loanBusinessOwners;
    loanContract.loanCustomerShareholders = loanCustomerShareholders;
    await handleAntifraud(kunnData, loanContract);
    await loggingRepo.saveWorkflow(WORKFLOW_STAGE.REQUEST_KUNN, kunn.status, contractNumber, USER.MISA, kunnId);
  } catch (error) {
    console.log(`[MISA][KUNN][V2][createKunn] Error body ${JSON.stringify(kunnData)}, error: ${error.message} `);

    rs = error?.errors?.[0]?.message || error.data || error.message;

    if (!kunn) {
      try {
        kunn = await saveKunn(kunnData, context);
      } catch (error) {
        console.log(`save kunn ${kunnId} error`, error);
      }
    }
    if (kunn?.kunnId) {
      await Promise.all([
        kunnRepo.update(kunnId, { status: KUNN_STATUS.CANCELLED }),
        loggingRepo.saveWorkflow(WORKFLOW_STAGE.REQUEST_KUNN, KUNN_STATUS.CANCELLED, contractNumber, USER.SYSTEM, kunnId, `System error: ${rs}`)
      ]);
      //callback
      setTimeout(() => {
        callbackKunnCancelApi(kunnId, {
          contractNumber: contractNumber,
          debtContractNumber: kunnId,
          debtContractStatus: 10,
        });
      }, 60000);
    }
  }
};

const createKunn = async (kunnData, context = {}) => {
  //gen kunnId
  let kunnId;
  let rs;
  try {
    console.log(`[MISA][KUNN][V2][createKunn] with payload ${JSON.stringify(kunnData)}`);
    kunnId = await kunnRepo.genKunnNumber();
    handleCreateKunn(kunnId, kunnData, context);
    //check flow
    rs = {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
      data: {
        status: "RECEIVED",
        debtContractNumber: kunnId,
      },
    };
    return rs;
  } catch (error) {
    console.log(`[MISA][KUNN][V2][createKunn] Error body ${JSON.stringify(kunnData)}, error: ${error} `);
    rs = error.data || error.message;
    throw error;
  } finally {
    if (kunnId) {
      await saveStepLog(kunnId, SERVICE_NAME.MC_LOS, STEP.REQUEST_KUNN, kunnData, rs);
    }
  }
};

const canCreateKunn = async (contractNumber, isSme = false) => {
  const contractStatus = await helper.getKuStatus(poolRead, contractNumber);
  let count = 0;
  if (contractStatus?.length != 0) {
    for (let i = 0; i < contractStatus.length; i++) {
      if (!isSme) {
        if (![STATUS.CANCELLED, STATUS.TERMINATED, STATUS.ACTIVATED, STATUS.NOT_ELIGIBLE, caseStatusCode.KKH13, caseStatusCode.KCP07, caseStatusCode.KKH14, caseStatusCode.KKH15, STATUS.SIGNED_TO_BE_DISBURED].includes(contractStatus[i].status)) {
          // let message = `kunn contract status: ${contractStatus[i].status}`;
          count++;
        }
      } else {
        if (![KUNN_STATUS.ACTIVATED_WITH_DIS_DOCS, KUNN_STATUS.CANCELLED, KUNN_STATUS.TERMINATED, KUNN_STATUS.ACTIVATED, KUNN_STATUS.NOT_ELIGIBLE, KUNN_STATUS.REFUSED, caseStatusCode.KKH13, caseStatusCode.KCP07, caseStatusCode.KKH14, caseStatusCode.KKH15].includes(contractStatus[i].status)) {
          // let message = `kunn contract status: ${contractStatus[i].status}`;
          count++;
        }
      }
    }
  }
  return !count ? true : false;
};

const createOffer = async ({ contractNumber, kunnId, withDrawAmount, ir, tenor, kunnCode }) => {
  const data = {};
  data.contractNumber = contractNumber;
  data.kunnNumber = kunnId;
  data.offerAmt = withDrawAmount;
  data.offerRate = ir;
  data.offerTenor = tenor;
  data.offerType = "STANDARD";
  data.productCode = kunnCode;
  data.requestAmt = withDrawAmount;
  data.requestTenor = tenor;
  data.isSelected = 1;
  return await offerRepo.saveKUOfferV2(data);
};

const handleAntifraud = async (kunnData, loanContract) => {
  try {
    let persons = kunnData.representations?.map((e) => ({
      customerName: e.fullName,
      fullName: e.fullName,
      idNumber: e.idNumber,
      otherIdNumber: e.otherIdNumber,
      issueDate: e.issueDate,
      issuePlace: e.issuePlace,
      dateOfBirth: e.dob,
      gender: null,
      phoneNumber: e.phoneNumber,
      address: e.address || e.curDetailAddress || "",
    }));
    let enterprises = [
      {
        registrationNumber: loanContract.loanCustomer.registrationNumber,
        taxCode: loanContract.smeTaxId,
        companyName: loanContract.loanCustomer.companyName,
        address: loanContract.loanCustomer.addressOnLicense || "",
      },
    ];
    for (const obj of [...(loanContract.loanBusinessOwners || []), ...(loanContract.loanCustomerShareholders || [])]) {
      if (obj.subject === LOAN_CUSTOMER_SUBJECT.INDIVIDUAL) {
        if (!persons.find((e) => e.idNumber == obj.idNumber)) {
          persons.push({
            customerName: obj.fullName,
            fullName: obj.fullName,
            idNumber: obj.idNumber,
            otherIdNumber: obj.otherIdNumber || "",
            issueDate: obj.issueDate,
            issuePlace: obj.issuePlace,
            dateOfBirth: obj.dob,
            gender: null,
            phoneNumber: obj.phoneNumber,
            type: ANTI_FRAUD_PERSON_TYPE.PER,
          });
        }

        if (obj.marriedStatus === "M" && obj.partnerIdNumber && !persons.find((e) => e.idNumber == obj.partnerIdNumber)) {
          persons.push({
            customerName: obj.partnerFullName,
            fullName: obj.partnerFullName,
            idNumber: obj.partnerIdNumber,
            otherIdNumber: "",
            issueDate: null,
            issuePlace: null,
            dateOfBirth: null,
            gender: null,
            phoneNumber: null,
            type: ANTI_FRAUD_PERSON_TYPE.PARTNER,
          });
        }
      } else if (obj.subject === LOAN_CUSTOMER_SUBJECT.ORGANIZATION && obj.taxId && !enterprises.find((e) => e.taxCode == obj.taxId)) {
        enterprises.push({
          registrationNumber: obj.registrationNumber || obj.taxId || "",
          taxCode: obj.taxId || "",
          companyName: obj.companyName || "",
          address: obj.addressOnLicense || "",
          customerName: obj.fullName || "",
          idNumber: obj.idNumber || "",
          otherIdNumber: obj.otherIdNumber || "",
          issueDate: obj.issueDate || "",
          issuePlace: obj.issuePlace || "",
          dateOfBirth: obj.dateOfBirth || "",
          gender: obj.gender || "",
          phoneNumber: obj.phoneNumber || "",
          registrationDate: obj.registrationDate || "",
          companyType: obj.companyType || "03",
        });
      }
    }
    loanContract.persons = persons;
    loanContract.enterprises = enterprises;
    const flows = [
      checkEligible,
      //check b11t
      checkCicB11t,
      //call cic
      checkCicDetails,
    ];
    let isEligible = true;
    for (let i = 0; i < flows.length; i++) {
      const rs = await flows[i](kunnData, loanContract);
      isEligible = rs;
      if (!rs) {
       let isEnd = true;
        break;
      }
    }
    if (!isEligible) {
      //handle callback cic
      await callBackKunnCic(kunnData.kunnId, isEligible);
    }
  } catch (error) {
    console.log(`[MISA][KUNN][V2][handleAntifraud] Error kunnData ${JSON.stringify(kunnData)}, error: ${error} `);
    throw error;
  }
};

const checkEligible = async (kunnData, loan) => {
  const requestId = uuid.v4();
  const step = KUNN_STEP.CHECK_ELIGIBLE;
  let status = KUNN_STATUS.ELIGIBLE;
  let statsDesc = KUNN_STATUS.ELIGIBLE;
  const eligiblePayload = {
    requestId: requestId,
    legalRepresentative: {
      customerName: kunnData.representations[0].fullName,
      idNumber: kunnData.representations[0].idNumber,
      otherIdNumber: kunnData.representations[0].otherIdNumber,
      issueDate: kunnData.representations[0].issueDate,
      issuePlace: kunnData.representations[0].issuePlace,
      dateOfBirth: kunnData.representations[0].dob,
      gender: null,
      phoneNumber: kunnData.representations[0].phoneNumber || loan.loanCustomer.phoneNumber,
    },
    otherPersons: loan.persons.filter((e) => e.type != ANTI_FRAUD_PERSON_TYPE.REP && e.idNumber != kunnData.representations[0].idNumber),
    otherEnterprises: loan.enterprises.filter(
      // loai bo doanh nghiep chinh dang vay, chi lay cac co dong
      (e) => e.taxCode != loan.loanCustomer.taxId
    ),
    productType: "",
    registrationNumber: loan.loanCustomer.registrationNumber,
    taxCode: loan.smeTaxId,
    companyName: loan.loanCustomer.companyName,
    registrationDate: loan.loanCustomer.registrationDate ?? loan?.registrationDate,
    productCode: "",
    caseCreationTime: new Date(),
    partnerCode: loan.partnerCode,
    channel: loan.channel,
    contractNumber: kunnData.debtContractNumber,
    companyType: loan.loanCustomer.businessType,
    contractType: CIC_CONTRACT_TYPE.KUNN,
  };
  try {
    const eligibleResult = await antiFraudService.checkEligibleSmeV2(eligiblePayload);
    if (!eligibleResult?.decision) {
      helper.throwServerError(`${kunnData.debtContractNumber} | KUNN | check eligible error`);
    }
    let result = true;
    if (eligibleResult?.decision && eligibleResult.decision != KUNN_STATUS.ELIGIBLE) {
      status = KUNN_STATUS.NOT_ELIGIBLE;
      result = false;
    }
    await updateKunn(kunnData.debtContractNumber, {
      status,
      step,
    });
    return result;
  } catch (error) {
    console.log(`[MISA][KUNN][V2][checkEligible] Error body ${JSON.stringify(eligiblePayload)}, error: ${error} `);
    statsDesc = `System error: ${error.message}`;
    throw error;
  } finally {
    await loggingRepo.saveWorkflow(WORKFLOW_STAGE.CHECK_ELIGIBLE, status, loan.contractNumber, USER.SYSTEM, kunnData.debtContractNumber, statsDesc);
  }
};

const checkCicS37 = async (kunnData, loan) => {
  const step = KUNN_STEP.CHECK_CIC_S37;
  let status = KUNN_STATUS.ELIGIBLE;
  let statsDesc = KUNN_STATUS.ELIGIBLE;

  const cicBody = {
    contractNumber: kunnData.debtContractNumber,
    persons: loan.persons,
    enterprises: loan.enterprises,
  };
  try {
    const cicResult = await antiFraudService.checkS37Sme(cicBody);
    if (!cicResult?.decision) {
      helper.throwServerError(`${kunnData.debtContractNumber} | KUNN | check checkS37Sme error`);
    }
    let result = true;
    if (cicResult?.decision && cicResult.decision != KUNN_STATUS.ELIGIBLE) {
      status = KUNN_STATUS.REFUSED;
      result = false;
    }
    await updateKunn(kunnData.debtContractNumber, {
      status,
      step,
    });
    return result;
  } catch (error) {
    console.log(`[MISA][KUNN][V2][checkCicS37] Error body ${JSON.stringify(cicBody)}, error: ${error} `);
    statsDesc = `System error: ${error.message}`;
    throw error;
  } finally {
    await loggingRepo.saveWorkflow(WORKFLOW_STAGE.CHECK_CIC_S37, status, loan.contractNumber, USER.SYSTEM, kunnData.debtContractNumber, statsDesc);
  }
};

const checkCicDetails = async (kunnData, loan) => {
  const step = KUNN_STEP.CHECK_CIC;
  let status = KUNN_STATUS.WAITING_CIC_RESULT;
  let statsDesc = KUNN_STATUS.ELIGIBLE;

  const cicBody = {
    contractNumber: kunnData.debtContractNumber,
    custId: loan.custId,
    contractType: CIC_CONTRACT_TYPE.KUNN,
    taxCode: loan.loanCustomer.taxId,
    persons: loan.persons,
    enterprises: loan.enterprises,
  };
  try {
    const cicResult = await antiFraudService.checkCicDetailsSme(cicBody);
    if (!cicResult?.status) {
      helper.throwServerError(`${kunnData.debtContractNumber} | KUNN | check checkCicDetails error`);
    }
    status = KUNN_STATUS.WAITING_CIC_RESULT;
    await updateKunn(kunnData.debtContractNumber, {
      status,
      step,
    });
    return true;
  } catch (error) {
    console.log(`[MISA][KUNN][V2][checkCicS37] Error body ${JSON.stringify(cicBody)}, error: ${error} `);
    statsDesc = `System error: ${error.message}`;
    throw error;
  } finally {
    await loggingRepo.saveWorkflow(WORKFLOW_STAGE.CHECK_CIC, status, loan.contractNumber, USER.SYSTEM, kunnData.debtContractNumber, statsDesc);
  }
};

const buildResApplicationForm = (personalInfo, address, smeInfo) => {
  return {
    appDetail: {},
    personalInfo,
    otherInfo: {},
    address,
    disbursement: {},
    offer: {},
    additionalInfor: {},
    voucherInfo: {},
    smeInfo,
  };
};

const getApplicationForm = async (kunnId) => {
  try {
    const kunn = helper.snakeToCamel(await kunnRepo.getKunnData(kunnId));
    if (!kunn) {
      helper.throwBadReqError(`contractNumber`, `Kunn ${kunnId} not found`);
    }
    const contract = helper.snakeToCamel(await loanRepo.getLoanContract(kunn.contractNumber));
    const representation = helper.snakeToCamel(
      await findOne({
        table: `loan_customer_representations`,
        whereCondition: {
          contract_number: contract.contractNumber,
        },
        orderBy: {
          created_at: "desc",
        },
      })
    );

    const personalInfo = {
      name: contract.smeRepresentationName || representation?.fullName || "",
      gender: contract.smeRepresentationGender || representation?.gender || "",
      dob: contract.smeRepresentationDob || representation?.dob || "",
      maritalStatus: "",
      nationality: "VN",
      idCard: contract.smeRepresentationId || representation?.idNumber || "",
      issueDate: contract.smeRepresentationIssueDate || representation?.issueDate || "",
      issuePlace: contract.smeRepresentationIssuePlace || representation?.issuePlace || "",
      email: contract.smeRepresentationEmail || representation?.email || "",
      phone: contract.smeRepresentationPhoneNumber || representation?.phoneNumber || "",
      houseType: "",
      numDependants: "",
      otherIdCard: contract.smeRepresentationOtherId || representation?.otherIdNumber || "",
    };

    const [smeRepresentationWardCur, smeRepresentationDistrictCur, smeRepresentationProvinceCur,
          smeRepresentationNewWardCur, smeRepresentationNewProvinceCur,
          smeRepresentationWardPer, smeRepresentationDistrictPer, smeRepresentationProvincePer,
          smeRepresentationNewWardPer, smeRepresentationNewProvincePer,
          wardCur, districtCur, provinceCur,
          curNewWard, curNewProvince,
          wardPer,districtPer, provincePer,
          perNewWard, perNewProvince
      ] = await Promise.all([
      parseValueCode(contract.smeRepresentationWardCur, ADDRESS_CODE_TYPE.WARD),
      parseValueCode(contract.smeRepresentationDistrictCur, ADDRESS_CODE_TYPE.DISTRICT),
      parseValueCode(contract.smeRepresentationProvinceCur, ADDRESS_CODE_TYPE.PROVINCE),
      parseValueCode(contract.smeRepresentationNewWardCur, ADDRESS_CODE_TYPE.NEW_WARD),
      parseValueCode(contract.smeRepresentationNewProvinceCur, ADDRESS_CODE_TYPE.NEW_PROVINCE),
      parseValueCode(contract.smeRepresentationWardPer, ADDRESS_CODE_TYPE.WARD),
      parseValueCode(contract.smeRepresentationDistrictPer, ADDRESS_CODE_TYPE.DISTRICT),
      parseValueCode(contract.smeRepresentationProvincePer, ADDRESS_CODE_TYPE.PROVINCE),
      parseValueCode(contract.smeRepresentationNewWardPer, ADDRESS_CODE_TYPE.NEW_WARD),
      parseValueCode(contract.smeRepresentationNewProvincePer, ADDRESS_CODE_TYPE.NEW_PROVINCE),
      parseValueCode(contract.wardCur, ADDRESS_CODE_TYPE.WARD),
      parseValueCode(contract.districtCur, ADDRESS_CODE_TYPE.DISTRICT),
      parseValueCode(contract.provinceCur, ADDRESS_CODE_TYPE.PROVINCE),
      parseValueCode(contract.curNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
      parseValueCode(contract.curNewProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE),
      parseValueCode(contract.wardPer, ADDRESS_CODE_TYPE.WARD),
      parseValueCode(contract.districtPer, ADDRESS_CODE_TYPE.DISTRICT),
      parseValueCode(contract.provincePer, ADDRESS_CODE_TYPE.PROVINCE),
      parseValueCode(contract.perNewWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
      parseValueCode(contract.perNewProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE)
    ]);
    const address = {
      contactAddress: {},
      curAddress: {
        street: contract.smeRepresentationAddressCur || "",
        ward: smeRepresentationWardCur,
        district: smeRepresentationDistrictCur,
        city: smeRepresentationProvinceCur,
        newWard: smeRepresentationNewWardCur,
        newProvince: smeRepresentationNewProvinceCur,
        country: "Việt Nam",
        address: getFullyAddress(contract.smeRepresentationAddressCur, [smeRepresentationNewWardCur, smeRepresentationNewProvinceCur], [smeRepresentationWardCur, smeRepresentationDistrictCur, smeRepresentationProvinceCur])
      },
      perAddress: {
        street:
          contract.smeRepresentationAddressPer ||
          representation?.perDetailAddress,
        ward: smeRepresentationWardPer,
        district: smeRepresentationDistrictPer,
        city: smeRepresentationProvincePer,
        newWard: smeRepresentationNewWardPer,
        newProvince: smeRepresentationNewProvincePer,
        country: "Việt Nam",
        address: getFullyAddress(contract.smeRepresentationAddressPer, [smeRepresentationNewWardPer, smeRepresentationNewProvincePer], [smeRepresentationWardPer, smeRepresentationDistrictPer, smeRepresentationProvincePer])
      },
    };
    const smeInfo = {
      smeName: contract.smeName || "",
      taxCode: contract.smeTaxId || contract.taxId || "",
      registrationNumber: contract.registrationNumber || "",
      status: kunn.status,
      address: {
        curAddress: {
          street: contract.addressCur || "",
          ward: wardCur,
          district: districtCur,
          city: provinceCur,
          newWard: curNewWard,
          newProvince: curNewProvince,
          country: "Việt Nam",
          address: getFullyAddress(contract.addressCur, [curNewWard, curNewProvince], [wardCur, districtCur, provinceCur])
        },
        perAddress: {
          street: contract.addressPer || "",
          ward: wardPer ,
          district: districtPer ,
          city: provincePer ,
          newWard: perNewWard ,
          newProvince: perNewProvince ,
          country: "Việt Nam",
          address: getFullyAddress(contract.addressPer, [perNewWard, perNewProvince], [wardPer, districtPer, provincePer])
        },
      },
    };
    const rs = buildResApplicationForm(personalInfo, address, smeInfo);
    return {
      code: 1,
      msg: "success",
      data: rs,
    };
  } catch (error) {
    console.log(`[MISA][KUNN][V2][getApplicationForm] Error contractNumber ${contractNumber}, error: ${error} `);
    throw error;
  }
};

const getAddressValue = async (value, type) => {
  if (!value) return "";
  try {
    const config = global.config;
    const mapData = await getValueCode_v2(config, value, type);
    return mapData;
  } catch (error) {
    console.log(`[getAddressValue] error value ${value}`);
    return value;
  }
};

const genKunnTtrv = async (kunnNumber) => {
  try {
    if (!kunnNumber) {
      throw new Error(`kunnId is required`);
    }
    await genMisaKunnTTRV(kunnNumber);
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
      data: null,
    };
  } catch (error) {
    console.log(`[MISA][KUNN][V2] genKunnTtrv kunnNumber: ${kunnNumber} error: ${error}`);
    throw error;
  }
};

const genKunnLtt = async (kunnNumber) => {
  try {
    const kunnData = helper.snakeToCamel(await kunnRepo.getKunnData(kunnNumber));
    if (!kunnData) throw new Error(`kunn not found`);
    const lttFileName = `LTT_${kunnData.kunnId}.pdf`;
    const lttFileNameEnc = `LTT_ENC_${kunnData.kunnId}.pdf.pgp`;
    const contractNumber = kunnData.contractNumber;
    const { resultS3, encryptResultS3 } = await genMisaKunnPaymentSchedule(kunnData);
    await Promise.all([
      loanContractDocumentRepo.insert({
        contractNumber,
        kunnContractNumber: kunnNumber,
        docType: "LTT_ENC",
        docId: uuid.v4(),
        fileKey: encryptResultS3.Key,
        fileName: lttFileNameEnc,
        url: encryptResultS3.Location,
        typeCollection: TYPE_COLLECTION.DOC_INTERNAL,
        fileSize: encryptResultS3.fileSize,
        docGroup: DOC_GROUP.MISA_CONTRACT_KUNN,
      }),
      loanContractDocumentRepo.insert({
        contractNumber,
        kunnContractNumber: kunnNumber,
        docType: "LTT",
        docId: uuid.v4(),
        fileKey: resultS3.Key,
        fileName: lttFileName,
        url: resultS3.Location,
        typeCollection: TYPE_COLLECTION.DOC_INTERNAL,
        fileSize: resultS3.fileSize,
        docGroup: DOC_GROUP.MISA_CONTRACT_KUNN,
      }),
    ]);
    return {
      code: ERROR_CODE.SUCCESS,
      message: "sucesss",
      data: null,
    };
  } catch (error) {
    console.log(`[MISA][KUNN][V2] genKunnLtt kunnNumber: ${kunnNumber} error: ${error}`);
    throw error;
  }
};

const checkCicB11t = async (kunnData, loan) => {
  const step = KUNN_STEP.CHECK_CIC_S37;
  let status = KUNN_STATUS.ELIGIBLE;
  let statsDesc = KUNN_STATUS.ELIGIBLE;

  const cicBody = {
    contractNumber: kunnData.debtContractNumber,
    persons: loan.persons,
    // enterprises: loan.enterprises,
  };
  try {
    const cicResult = await antiFraudService.checkCicB11t(cicBody);
    if (!cicResult?.decision) {
      helper.throwServerError(`${kunnData.debtContractNumber} | KUNN | check checkCicB11t error`);
    }
    let result = true;
    if (cicResult?.decision && cicResult.decision != KUNN_STATUS.ELIGIBLE) {
      status = KUNN_STATUS.REFUSED;
      result = false;
    }
    await updateKunn(kunnData.debtContractNumber, {
      status,
      step,
    });
    return result;
  } catch (error) {
    console.log(`[MISA][KUNN][V2][checkCicB11t] Error body ${JSON.stringify(cicBody)}, error: ${error} `);
    statsDesc = `System error: ${error.message}`;
    throw error;
  } finally {
    await loggingRepo.saveWorkflow(WORKFLOW_STAGE.CHECK_CIC_B11T, status, loan.contractNumber, USER.SYSTEM, kunnData.debtContractNumber, statsDesc);
  }
};

module.exports = {
  saveKunn,
  convertBody,
  createKunn,
  updateKunn,
  getApplicationForm,
  genKunnTtrv,
  genKunnLtt,
};
