const { Router } = require("express");
const { handleResponseError, handleInternalResponseError } = require("../base/response.js");
const { addPaging, handlePartnerChanel, wrapService } = require("../utils/middleware.js");
const callbackService = require("../services/callback-service.js");
const router = Router();

router.use(handlePartnerChanel);

router.post("/v1/af2/approve", async (req, res) => {
  try {
    const result = await req.instanceService.lender.af2Approve(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/af2/reject", async (req, res) => {
  try {
    const result = await req.instanceService.lender.af2Reject(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get(
  "/v1/loan-request",
  addPaging,
  wrapService((instanceService) => async (req, res) => {
    try {
      // Call service method depending on partner
      const result = await instanceService.lender.getLoanRequest(req, res);
      return res.json(result);
    } catch (error) {
      return handleInternalResponseError(res, error);
    }
  })
);

router.post("/v1/af3/handler-documents-generated", async (req, res) => {
  try {
    console.log("handler-documents-generated callback invoked with payload: ", JSON.stringify(req?.body));
    const result = await req.instanceService.lender.handleDocumentsGenerated(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post(
  "/v1/loan-request/export",
  addPaging,
  //    async (req, res) => {
  //   try {
  //     const buffer = await req.instanceService.lender.exportLoanRequest(req, res);
  //     const filename = `export_${new Date().toISOString()}.xlsx`;
  //     res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
  //     res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
  //     res.send(buffer);
  //   } catch (error) {
  //     return handleInternalResponseError(res, error);
  //   }
  // }
  wrapService((instanceService) => async (req, res) => {
    try {
      const buffer = await instanceService.lender.exportLoanRequest(req, res);
      const filename = `export_${new Date().toISOString()}.xlsx`;
      res.setHeader("Content-Type", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
      res.setHeader("Content-Disposition", `attachment; filename="${filename}"`);
      res.send(buffer);
    } catch (error) {
      return handleInternalResponseError(res, error);
    }
  })
);

router.get(
  "/v1/loan-request/details",
  addPaging,
  wrapService((instanceService) => async (req, res) => {
    try {
      const result = await instanceService.lender.getLoanRequestDetails(req?.query?.contract_number);
      return res.json(result);
    } catch (error) {
      return handleInternalResponseError(res, error);
    }
  })
);

router.get(
  "/v1/loan-request/details/af1",
  wrapService((instanceService) => async (req, res) => {
    try {
      const result = await instanceService.lender.getLoanRequestDetailsAf1(req, res);
      return res.json(result);
    } catch (error) {
      return handleInternalResponseError(res, error);
    }
  })
);

router.get("/v1/loan-request/change-request/history", async (req, res) => {
  try {
    const { contract_number } = req.query;
    const result = await req.instanceService.lender.getLoanChangeRequestHistory(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2", addPaging,
 wrapService((instanceService) => async (req, res) => {
  try {
    const result = await instanceService.lender.getLoanRequestDetailsAf2(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
}));

router.get("/v1/loan-request/details/af2/common", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getLoanRequestCommonDetails(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/finance", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getLoanRequestFinanceInfoDetails(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/customer-info", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getCustomerInfoDetails(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/permanent-address", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getPermanentAddress(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/current-address", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getCurrentAddress(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/spouse", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getSpouseDetails(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/businesses-info", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getBusinessesInfoDetails(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/reference1", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getReferenceDetails1(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/reference2", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getReferenceDetails2(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/proposal", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getProposalDetails(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af2/business-operations", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getBusinessOperations(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/related", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getLoanRequestRelated(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/related-docs", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getLoanRequestRelatedDocs(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/loan-request/related-docs/save", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.lender.saveUploadDocument(body);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});
//delete document
router.delete("/v1/loan-request/related-docs", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.lender.deleteDocument(body.contract_number, body.doc_id);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/finance-model", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getLoanRequestFinanceModel(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/branches", async (req, res) => {
  try {
    const { contract_number } = req.query;
    const result = await req.instanceService.lender.getLoanBranches(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/branches/details", async (req, res) => {
  try {
    const { contract_number, id } = req.query;
    const result = await req.instanceService.lender.getLoanBranchDetails(contract_number, id);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

//warehouse endpoints
router.get("/v1/loan-request/warehouses", async (req, res) => {
  try {
    const { contract_number } = req.query;
    const result = await req.instanceService.lender.getLoanWarehouses(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/warehouses/details", async (req, res) => {
  try {
    const { contract_number, id } = req.query;
    const result = await req.instanceService.lender.getLoanWarehouseDetails(contract_number, id);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/representations", async (req, res) => {
  try {
    const { contract_number } = req.query;
    const result = await req.instanceService.lender.getLoanRepresentations(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/representations/details", async (req, res) => {
  try {
    const { contract_number, id } = req.query;
    const result = await req.instanceService.lender.getLoanRepresentationDetail(contract_number, id);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/managers/details", async (req, res) => {
  try {
    const { contract_number } = req.query;
    const result = await req.instanceService.lender.getLoanCustomerManagerDetails(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/shareholders", async (req, res) => {
  try {
    const { contract_number } = req.query;
    const result = await req.instanceService.lender.getLoanCustomerShareholders(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/shareholders/details", async (req, res) => {
  try {
    const { contract_number, id } = req.query;
    const result = await req.instanceService.lender.getLoanCustomerShareholdersDetails(contract_number, id);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/owners", async (req, res) => {
  try {
    const { contract_number } = req.query;
    const result = await req.instanceService.lender.getLoanCustomerOwners(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

//for loan customer owners details
router.get("/v1/loan-request/owners/details", async (req, res) => {
  try {
    const { contract_number, id } = req.query;
    const result = await req.instanceService.lender.getLoanCustomerOwnersDetails(contract_number, id);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

//for loan customer partners
router.get("/v1/loan-request/partners", async (req, res) => {
  try {
    const { contract_number } = req.query;
    const result = await req.instanceService.lender.getLoanCustomerPartners(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});
router.get("/v1/loan-request/partners/details", async (req, res) => {
  try {
    const { contract_number, id } = req.query;
    const result = await req.instanceService.lender.getLoanCustomerPartnersDetails(contract_number, id);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/loan-request/process", async (req, res) => {
  try {
    const result = await req.instanceService.lender.processData(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/loan-request/sub-info/process", async (req, res) => {
  try {
    const { contract_number, sub_type, reference_id, info, request_id } = req.body;
    const result = await req.instanceService.lender.processSubInfo({ contract_number, sub_type, reference_id, info, request_id });
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/loan-request/change-request/approve", async (req, res) => {
  try {
    const result = await req.instanceService.lender.approveChangeRequest(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/loan-request/change-request/reject", async (req, res) => {
  try {
    const result = await req.instanceService.lender.rejectChangeRequest(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/origin-documents", async (req, res) => {
  try {
    let { contract_number } = req.query;
    const result = await req.instanceService.lender.getOriginDocumentByDocType(contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/loan-request/details/af3", addPaging, async (req, res) => {
  try {
    const result = await req.instanceService.lender.getLoanRequestDetailsAF3(req?.query?.contract_number);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/loan-request/af3-process", async (req, res) => {
  try {
    const result = await req.instanceService.lender.processAf3Data(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/loan-request/change-request/af3-approve", async (req, res) => {
  try {
    const result = await req.instanceService.lender.approveAF3ChangeRequest(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/loan-request/change-request/af3-reject", async (req, res) => {
  try {
    const result = await req.instanceService.lender.rejectAF3ChangeRequest(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/search", async (req, res) => {
  try {
    const result = await req.instanceService.kunn.searchKunnLender(req, res);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/approve", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.kunn.approveKunnLender(body, req.originalUrl);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/cancel", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.kunn.cancelKunnLender(body);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/change-request/reject", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.kunn.rejectKunnChangeRequest(body);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/change-request/approve", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.kunn.approveKunnChangeRequest(body);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/resubmit", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.kunn.resubmitKunnLender(body, req.originalUrl);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/kunn/detail", async (req, res) => {
  try {
    const { kunnId } = req.query;
    const result = await req.instanceService.kunn.getKunnDetail(kunnId);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/kunn/loan-detail", async (req, res) => {
  try {
    const { kunnId } = req.query;
    const result = await req.instanceService.kunn.getLoanContractByKunn(kunnId);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/kunn/invoice", async (req, res) => {
  try {
    const { kunnId } = req.query;
    const result = await req.instanceService.kunn.getKunnInvoices(kunnId);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get('/v1/kunn/withdrawal-info', async (req, res) => {
  try {
    const { kunnId } = req.query;
    const result = await req.instanceService.kunn.getKunnWithdrawalInfo(kunnId);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get('/v1/kunn/beneficiary-info', async (req, res) => {
  try {
    const { kunnId } = req.query;
    const result = await req.instanceService.kunn.getKunnBeneficiaryInfo(kunnId);
    return res.json(result);
  } catch (error) {
    console.log(error);
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/process", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.kunn.saveKunn(body, req.originalUrl);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/document/save", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.kunn.saveUploadDocument(body);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/document/delete", async (req, res) => {
  try {
    const { body } = req;
    const result = await req.instanceService.kunn.deleteDocument(body);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/kunn/document/relation", async (req, res) => {
  try {
    const { kunnId } = req.query;
    const result = await req.instanceService.kunn.getRelationDocuments(kunnId);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/kunn/sub-info", async (req, res) => {
  try {
    const { kunnId, subType, referenceId } = req.query;
    const result = await req.instanceService.kunn.getSubInfo({ kunnId, subType, referenceId });
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/sub-info/process", async (req, res) => {
  try {
    const body = req.body;
    const result = await req.instanceService.kunn.processSubInfo(body);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/kunn/document/signed", async (req, res) => {
  try {
    const { kunnId } = req.query;
    const result = await req.instanceService.kunn.getKunnDocumentSigned(kunnId);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/document/signed/process", async (req, res) => {
  try {
    const { kunnId, files, userName } = req.body;
    const result = await req.instanceService.kunn.processKunnSignedDocument({ kunnId, files, updatedBy: userName });
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.get("/v1/kunn/relation", async (req, res) => {
  try {
    const { kunnId } = req.query;
    const result = await req.instanceService.kunn.getKunnRelation(kunnId);
    return res.json(result);
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

router.post("/v1/kunn/callback", async (req, res) => {
  try {
    const { kunnId } = req.body;
    await callbackService.callbackKunnBizzi(kunnId);
    return res.json({ message: "Callback processed successfully" });
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

const crmGw = require("../services/crm-service");
const antiFrauGw = require("../services/anti-fraud.js");
const { wrap } = require("lodash");
const { parseDG13 } = require("../utils/nfc")
router.post("/v1/test", async (req, res) => {
  try {
    // const { contract_number } = req.body;
    let result = [];
    // result = await antiFrauGw.checkWhitelistFullLoanHm(req.body);
    // result = await antiFrauGw.checkModelHm(req.body);
    // console.log("", JSON.stringify(result));
    // await crmGw.checkDedupEnterpriseFD(req.body);
    // result = parseDG13(contract_number);
    return res.json(result);
    
  } catch (error) {
    return handleInternalResponseError(res, error);
  }
});

module.exports = router;
