const caseStatus = {
    'KH01': 'Credit request',
    'KH02': 'Duplication check',
    'KH03': 'Credit rule check',
    'KH04': 'A1 - Reject ',
    'KH05': 'A1 - Finish',
    'KH06': 'A2 - Processing',
    'CP01': 'CP - New',
    'CP02': 'CP - Reopen',
    'CP03': 'CP - Resubmit',
    'CP04': 'CP - Inprocess',
    'CP05': 'CP - Approved',
    'CP06': 'CP - Rejected',
    'CP07': 'CP - Cancel',
    'CE01': 'CE - New',
    'CE02': 'CE - Reopen',
    'CE03': 'CE - Inprocess',
    'CE04': 'CE - Approved ',
    'CE05': 'CE - Resubmit',
    'CE06': 'CE - Canceled',
    'CE07': 'CE - Reject',
    'CE08': 'CE - Approved with Deviation',
    'MC01': 'MC - New',
    'MC02': 'MC - Reopen',
    'MC03': 'MC - Inprocess',
    'MC04': 'MC - Confirmed',
    'MC05': 'MC - Resubmit',
    'MC06': 'MC - Escalated',
    'MC07': 'MC - Rejected',
    'RP01': 'RP - New',
    'RP02': 'RP - Inprocess',
    'RP03': 'RP - Escalated',
    'C01': 'C - New',
    'C02': 'C - Approved ',
    'C03': 'C - Rejected',
    'C04': 'C - Inprocess',
    'KH07': 'Esign',
    'KH08': 'Signed',
    'KH09': 'Active',
    'KH10': 'Terminated',
    'KH11': 'Customer Select Offer',
    'KH12': 'Customer Reject Offer',
    'KKH01': 'Withdraw Request',
    'KKH02': 'Limit Check',
    'KKH03': 'Limit Reject ',
    'KKH04': 'Credit Rule Check',
    'KKH05': 'Credit Rule Reject ',
    'KKH06': 'Instalment Calculator',
    'KKH07': 'Customer Select Offer',
    'KKH08': 'Customer Reject Offer',
    // 'KKH09': 'Waiting assign to CP',
    'KCP01': 'CP - New',
    'KCP02': 'CP - Reopen',
    'KCP03': 'CP - Inprocess',
    'KCP04': 'CP - Reject ',
    'KCP05': 'CP - Approve',
    'KCP06': 'CP - Resubmit',
    'KCP07': 'CP - Cancel',
    'KKH09': 'Esign',
    'KKH10': 'Signed',
    'KKH11': 'To be disbursed',
    'KKH12': 'Disbursed',
    'KKH13': 'Active',
    'KKH14': 'Terminated',
    'KKH15': 'Failed Disbursement',
    'CA01': 'Canceled',
    'CA02': 'CA02',
}

const caseStatusCode = {
    'KH01': 'KH01',
    'KH02': 'KH02',
    'KH03': 'KH03',
    'KH04': 'KH04',
    'KH05': 'KH05',
    'KH06': 'KH06',
    'CP01': 'CP01',
    'CP02': 'CP02',
    'CP03': 'CP03',
    'CP04': 'CP04',
    'CP05': 'CP05',
    'CP06': 'CP06',
    'CP07': 'CP07',
    'CE01': 'CE01',
    'CE02': 'CE02',
    'CE03': 'CE03',
    'CE04': 'CE04',
    'CE05': 'CE05',
    'CE06': 'CE06',
    'CE07': 'CE07',
    'CE08': 'CE08',
    'CE09': 'CE09',
    'MC01': 'MC01',
    'MC02': 'MC02',
    'MC03': 'MC03',
    'MC04': 'MC04',
    'MC05': 'MC05',
    'MC06': 'MC06',
    'MC07': 'MC07',
    'RP01': 'RP01',
    'RP02': 'RP02',
    'RP03': 'RP03',
    'C01': 'C01',
    'C02': 'C02',
    'C03': 'C03',
    'C04': 'C04',
    'KH07': 'KH07',
    'KH08': 'KH08',
    'KH09': 'KH09',
    'KH10': 'KH10',
    'KH11': 'KH11',
    'KH12': 'KH12',
    'KH13': 'KH13',
    'CA01': 'CA01',
    'CA02': 'CA02',
    'KKH01': 'KKH01',
    'KKH02': 'KKH02',
    'KKH03': 'KKH03',
    'KKH04': 'KKH04',
    'KKH05': 'KKH05',
    'KKH06': 'KKH06',
    'KKH07': 'KKH07',
    'KKH08': 'KKH08',
    'KKH09': 'KKH09',
    'KCP01': 'KCP01',
    'KCP02': 'KCP02',
    'KCP03': 'KCP03',
    'KCP04': 'KCP04',
    'KCP05': 'KCP05',
    'KCP06': 'KCP06',
    'KCP07': 'KCP07',
    'KKH10': 'KKH10',
    'KKH11': 'KKH11',
    'KKH12': 'KKH12',
    'KKH13': 'KKH13',
    'KKH14': 'KKH14',
    'KKH15': 'KKH15',
    'KCA01': 'KCA01',
    'KCA02': 'KCA02',
    'IN_CP_QUEUE': 'IN_CP_QUEUE',
    'CP_CHECK_OK': 'CP_CHECK_OK',
    'CANCELLED': 'CANCELLED',
    'RESUBMIT': 'RESUBMIT',
    'REFUSED': 'REFUSED',
    'IN_CE_VER_QUEUE': 'IN_CE_VER_QUEUE',
    'APPROVED': 'APPROVED',
    IN_SS_QUEUE: 'IN_SS_QUEUE'
}

const STATUS = {
    ON_BOARD: 'ON_BOARD',
    ELIGIBLE: 'ELIGIBLE',
    NOT_ELIGIBLE: 'NOT_ELIGIBLE',
    NOT_ELIGIBLE2: 'NOT ELIGIBLE',
    FULL_LOAN: 'FULL_LOAN',
    RECEIVED: 'RECEIVED',
    IN_MANUAL_REVIEW_A1: 'IN_MANUAL_REVIEW_A1',
    PASSED_REVIEW_A1: 'PASSED_REVIEW_A1',
    IN_MANUAL_REVIEW_A2: 'IN_MANUAL_REVIEW_A2',
    PASSED_REVIEW_A2: 'PASSED_REVIEW_A2',
    PASSED_REVIEW_A3: 'PASSED_REVIEW_A3',
    IN_MANUAL_PROCESS: 'IN_MANUAL_PROCESS',
    IN_MANUAL_PROCESS_A3: 'IN_MANUAL_PROCESS_A3',
    IN_MANUAL_KYC: 'IN_MANUAL_KYC',
    FAIL_EKYC: 'FAIL_EKYC',
    FAIL_MANUAL_KYC: 'FAIL_MANUAL_KYC',
    KYC_RESUBMIT: 'KYC_RESUBMIT',
    IN_MANUAL_DECISION: 'IN_MANUAL_DECISION',
    APPROVED: 'APPROVED',
    PASSED_OFFER: 'PASSED_OFFER',
    WAITING_TO_BE_SIGNED: 'WAITING_TO_BE_SIGNED',
    SIGNING_IN_PROGRESS: 'SIGNING_IN_PROGRESS',
    SIGNED_WO_DOCS: 'SIGNED_WO_DOCS',
    SIGNED: 'SIGNED',
    SIGNED_TO_BE_DISBURED: 'SIGNED_TO_BE_DISBURED',
    ACTIVATED: 'ACTIVATED',
    TERMINATED: 'TERMINATED',
    REFUSED: 'REFUSED',
    CANCELLED: 'CANCELLED',
    IN_CE_QUEUE: 'IN_CE_QUEUE',
    IN_CP_QUEUE: 'IN_CP_QUEUE',
    IN_DE_QUEUE: 'IN_DE_QUEUE',
    TO_BE_STUDIED: 'TO_BE_STUDIED',
    PASSED_SCORING: 'PASSED_SCORING',
    ACCEPTED_WITH_OFFERS: 'ACCEPTED_WITH_OFFERS',
    CE_CHECK_DOC_OK: 'CE_CHECK_DOC_OK',
    SELECTED_OFFER: 'SELECTED_OFFER',
    AUTO_CE_APPROVED: 'AUTO_CE_APPROVED',
    AUTO_CP_APPROVED: 'AUTO_CP_APPROVED',
    CE_RESUBMIT: 'CE_RESUBMIT',
    CP_RESUBMIT: 'CP_RESUBMIT',
    CP_RESUBMIT_A3: 'CP_RESUBMIT_A3',
    PROCESS_FORM: 'PROCESS_FORM',
    COMPLETED_SIGN: 'COMPLETED_SIGN',
    TERMINATION_IN_PROGRESS: 'TERMINATION_IN_PROGRESS',
    STOPED: 'STOPED',
    PASSED_EKYC: 'PASSED_EKYC',
    DECISION_ENGINE: 'DECISION_ENGINE',
    PASSED_DECISION_ENGINE: 'PASSED_DECISION_ENGINE',
    WAITING_RECEIVE_DISBURSEMENT: 'WAITING_RECEIVE_DISBURSEMENT',
    BAD_DEBT: 'BAD_DEBT',
    DEVIATION: 'DEVIATION',
    MC_APPROVED: 'MC_APPROVED',
    MC_ESCALATED: 'MC_ESCALATED',
    CP_RESUBMIT_ESIGN: 'CP_RESUBMIT_ESIGN',
    IN_CP_POST_QUEUE: 'IN_CP_POST_QUEUE',
    IN_SECURITY_CE_QUEUE: 'IN_SECURITY_CE_QUEUE',
    IN_CE_MAXLOAN_QUEUE: 'IN_CE_MAXLOAN_QUEUE',
    IN_CP_BEFORE_SIGN_QUEUE: 'IN_CP_BEFORE_SIGN_QUEUE',
    IN_CP_DIS_QUEUE: 'IN_CP_DIS_QUEUE',
    IN_CL_QUEUE: 'IN_CL_QUEUE',
    IN_MC_QUEUE: 'IN_MC_QUEUE',
    IN_RP_QUEUE: 'IN_RP_QUEUE',
    CREDIT_REVIEW: 'CREDIT_REVIEW',
    CLOSED: 'CLOSED',
    IN_SS_QUEUE: 'IN_SS_QUEUE',
    SS_RESUBMIT: 'SS_RESUBMIT',
    IN_SS_MAXLOAN_QUEUE: 'IN_SS_MAXLOAN_QUEUE',
    SS_RESUBMIT_ESIGN: 'SS_RESUBMIT_ESIGN',
    KYC_CHECK: 'KYC check',
    KH09: 'KH09',
    APPROVE_TO_ACTIVE: 'APPROVE_TO_ACTIVE',
    LOAN_LIMIT_PASSED: 'LOAN_LIMIT_PASSED',
    RECEIVEDA2: 'RECEIVEDA2',
    RECEIVEDA1: 'RECEIVEDA1',
    RECEIVEDA3: 'RECEIVEDA3',
    RECEIVEDTC1: 'RECEIVEDTC1',
    RECEIVEDTC2: 'RECEIVEDTC2',
    WAITING_CIC_RESULT: 'WAITING_CIC_RESULT',
    WAITING_CUSTOMER_SIGNATURE: 'WAITING_CUSTOMER_SIGNATURE',
    WAITING_EVF_SIGNATURE: 'WAITING_EVF_SIGNATURE',
    NFC_INVALID:"NFC_INVALID",
    SUBMIT_AF3:"SUBMIT_AF3",
    WAITING_APPROVE_CHANGE_REQUEST: 'WAITING_APPROVE_CHANGE_REQUEST',
    APPROVED_CHANGE_REQUEST: 'APPROVED_CHANGE_REQUEST',
    REJECT_CHANGE_REQUEST: 'REJECT_CHANGE_REQUEST',
    WAITING_APPROVE_AF3_CHANGE_REQUEST: 'WAITING_APPROVE_AF3_CHANGE_REQUEST',
    APPROVED_AF3_CHANGE_REQUEST: 'APPROVED_AF3_CHANGE_REQUEST',
    REJECT_AF3_CHANGE_REQUEST: 'REJECT_AF3_CHANGE_REQUEST',
    RESUBMIT_A2: 'RESUBMIT_A2',
    RESUBMIT_A3: 'RESUBMIT_A3',
    EXPIRED: 'EXPIRED',
    INACTIVATED: 'INACTIVATED',
}

const CRM_STATUS = {
    'REFUSED': 1,
    'CANCEL': 2,
    'PROCESS': 4,
    'APPROVE': 6,
    'ACTIVATED': 0
}

const VSK_CUSTOMER = {
    LOAN_STANDARD_OFFICE: 'LSOF',
    LOAN_STANDARD_ONLINE: 'LSON',
    LOAN_STANDARD_OFFICE_ONLINE: 'LSOO',
    LOAN_VIP_OFFICE: 'LVOF',
    LOAN_VIP_ONLINE: 'LVON',
    LOAN_VIP_OFFICE_ONLINE: 'LVOO',
    LOAN_PREMIUM_OFFICE: 'LPOF',
    LOAN_PREMIUM_ONLINE: 'LPON',
    LOAN_PREMIUM_OFFICE_ONLINE: 'LPOO'
}

const VSK_PRODUCT = {
    STANDARD: 'MCBAS_STANDARD',
    VIP: 'MCBAS_VIP',
    PREMIUM: 'MCBAS_PREMIUM'
}

const CALLBACK_STAUS = {
    "ELIGIBLE": "ELIGIBLE",
    "FULL_LOAN": "FULL_LOAN",
    "REJECTED": "REJECTED",
    "CONTINUEA2": "CONTINUEA2",
    "RECEIVED": "RECEIVED",
    "ALT_OFFER": "ALT_OFFER",
    "RESUBMIT": "RESUBMIT",
    "APPROVED": "APPROVED",
    "ESIGN": "ESIGN",
    "SIGNED": "SIGNED",
    "ACTIVATED": "ACTIVATED",
    "ACTIVE": "ACTIVE",
    "INACTIVE": "INACTIVE",
    "CANCELLED": "CANCELLED",
    "TERMINATED": "TERMINATED",
    "APPROVE_SIGN": "APPROVE_SIGN",
    "CREATED_KUNN": "CREATED_KUNN",
    "CANCEL_KUNN": "CANCEL_KUNN",
    "APPROVE_KUNN_FOR_SIGN": "APPROVE_KUNN_FOR_SIGN",
    "REJECT_KUNN": "REJECT_KUNN",
    "ACTIVE_KUNN": "ACTIVE_KUNN",
    "SIGNED_KUNN": "SIGNED_KUNN",
    "RESUBMIT_KU": "RESUBMIT_KU",
    "WAITING_TO_BE_SIGNED": "WAITING_TO_BE_SIGNED",
    "ACCEPTED_WITH_OFFERS": "ACCEPTED_WITH_OFFERS",
    "NOT_ELIGIBLE": "NOT_ELIGIBLE",
    "SS_RESUBMIT": "SS_RESUBMIT",
    "CP_RESUBMIT": "CP_RESUBMIT",
    "IN_SS_QUEUE": "IN_SS_QUEUE",
    UPDATE_PAYMENT:'UPDATE_PAYMENT',
    RECEIVED_PAYMENT:'RECEIVED_PAYMENT',
    CONTRACT_STATUS: 'CONTRACT_STATUS',
    KUNN_STATUS: 'KUNN_STATUS',
    WAITING_CUSTOMER_SIGNATURE: 'WAITING_CUSTOMER_SIGNATURE',
    RESUBMIT_A2: 'RESUBMIT_A2',
    RESUBMIT_A3: 'RESUBMIT_A3',
    EXPIRED_LIMIT: 'EXPIRED_LIMIT'
}

const MISA_CALLBACK_STATUS = {
    ELIGIBLE: 2,
    FULLLOAN: 3,
    REFUSE: 9,
    CANCELLED: 10,
    APPROVE_SIGN: 4,
    ACTIVE: 8,
    CONTRACT_ELIGIBLE: 6,
    RESUBMIT_CONTRACT: 7,
    CREATED_KUNN: 1,
    SIGNED_KUNN: 4,
    ACTIVE_KUNN: 6,
    CANCEL_KUNN: 10,
    REJECT_KUNN: 9,
    APPROVE_FOR_SIGN_KUNN: 2,
    RESUBMIT_KUNN_CONTRACT: 5
}

const MAPPING_CALLBACK_STATUS = {
    "CP_RESUBMIT": "RESUBMIT",
    "CE_RESUBMIT": "RESUBMIT",
    "ACCEPTED_WITH_OFFERS": "APPROVED",
    "ELIGIBLE": "CONTINUEA2",
    "NOT_ELIGIBLE": "REJECTED",
    REFUSED: 'REJECTED',
}
const MAPPING_STATUS_CLIENT_CODE = {
    REFUSED: 'REJECTED',
    ELIGIBLE: 'CONTINUEA2',
    RECIVED: 'RECEIVED',
    AUTO_MERG: 'RECEIVED',
    NEW: 'RECEIVED',
    IN_MANUAL_PROCESS: 'RECEIVED',
    IN_CE_DUP_QUEUE: 'RECEIVED',
    MERGED: 'RECEIVED',
    PASSED_EKYC: 'RECEIVED',
    IN_MANUAL_KYC: 'RECEIVED',
    IN_CP_QUEUE: 'RECEIVED',
    IN_CP_POST_QUEUE: 'RECEIVED',
    CP_CHECK_OK: 'RECEIVED',
    IN_DE_QUEUE: 'RECEIVED',
    IN_CE_VER_QUEUE: 'RECEIVED',
    DEVIATION: 'RECEIVED',
    IN_SS_QUEUE: 'RECEIVED',
    IN_MC_QUEUE: 'RECEIVED',
    ESCALATED: 'RECEIVED',
    IN_RP_QUEUE: 'RECEIVED',
    IN_C_LEVEL_QUEUE: 'RECEIVED',
    PASSED_S37: 'CONTINUEA2',
    UNMATCH_OFFER_VIP: 'RECEIVED',
    UNMATCH_OFFER_PRE: 'RECEIVED',
    UNMATCH_OFFER_BSA: 'RECEIVED',
    UNMATCH_OFFER_STA: 'ALT_OFFER',
    SELECTING_OFFER: 'SELECTING OFFER',
    "CP_RESUBMIT": "RESUBMIT",
    "CE_RESUBMIT": "RESUBMIT",
    "ACCEPTED_WITH_OFFERS": "ACCEPTED_WITH_OFFERS",
    RESUBMIT: 'RESUBMIT',
    SELECTED_OFFER: 'APPROVED',
    WAITING_TO_BE_SIGNED: 'ESIGN',
    COMPLETED_SIGN: 'SIGNED',
    SIGNED_WO_DOCS: 'SIGNED',
    INACTIVE: 'INACTIVE',
    CANCELLED: 'CANCELLED',
    ACTIVE: 'ACTIVE',
    ACTIVATED: 'ACTIVATED',
    TERMINATED: 'TERMINATED',
    FAIL_EKYC: 'RECEIVED',
    APPROVED: 'RECEIVED',
    CE_RESUBMIT_DE: 'RECEIVED',
    CE_RESUBMIT_DOCS: 'RESUBMIT',
    DISBURED_SUCESSFULLY: 'DISBURED_SUCESSFULLY',
    IN_CL_QUEUE: 'RECEIVED',
    IN_CP_BEFORE_SIGN_QUEUE: 'RECEIVED',
    RECEIVEDA2: 'RECEIVEDA2'

}
const MAPPING_STATUS_CLIENT_NAME = {
    REJECTED: 'Từ Chối',
    REFUSED: 'Từ Chối',
    CONTINUEA2: 'Tiếp tục đơn vay',
    RECEIVED: 'Đang Thẩm Định',
    ACCEPTED_WITH_OFFERS: 'Chờ Khách Hàng Xác Nhận Khoản Vay',
    RESUBMIT: 'Bổ Sung Chứng Từ',
    APPROVED: 'Hồ Sơ Được Duyệt',
    ESIGN: 'Ký Hợp Đồng',
    SIGNED: 'Đã Ký Hợp Đồng',
    ACTIVE: 'Đang Hiệu Lực',
    ACTIVATED: 'Đang Hiệu Lực',
    INACTIVE: 'Hợp Đồng Hết Hạn',
    CANCELLED: 'Đã Huỷ',
    TERMINATED: 'Hoàn Tất Khoản Vay',
    DISBURED_SUCESSFULLY: 'Giải ngân thành công'
}

const WORKFLOW_STAGE = {
    CHECK_ELIGIBLE: 'CHECK_ELIGIBLE',
    CHECK_CIC_S37: 'CHECK_CIC_S37',
    SEND_FULLLOAN: 'SEND_FULLLOAN',
    PROCESS_FULLLOAN: 'PROCESS_FULLLOAN',
    CHECK_DEDUP: 'CHECK_DEDUP',
    MANUAL_DEDUP: 'MANUAL_DEDUP',
    EKYC: 'EKYC',
    'CP': 'CP',
    DE: 'DE',
    CE: 'CE',
    'SALE_SUPPORT': 'SALE_SUPPORT',
    'MERCHANT': 'MERCHANT',
    'RISK_POLICY': 'RISK_POLICY',
    'C_LEVEL': 'C_LEVEL',
    'OFFER_SELECTION': 'OFFER_SELECTION',
    'SIGN_CONTRACT': 'SIGN_CONTRACT',
    'CP_POST_CHECK DOC': 'CP_POST_CHECK',
    'ACTIVATE': 'ACTIVATE',
    'DISBURSEMENT': 'DISBURSEMENT',
    'TERMINATION': 'TERMINATION',
    'MANUAL': 'MANUAL',
    REQUEST_KUNN: 'REQUEST_KUNN',
    CHECK_CIC: 'CHECK_CIC',
    CIC_RESULT : 'CIC_RESULT',
    SIGNING: 'SIGNING',
    CANCELLED: 'CANCELLED',
    UI_REVIEW: 'UI_REVIEW',
    SUBMIT_AF3: 'SUBMIT_AF3',
    CHECK_CIC_B11T: 'CHECK_CIC_B11T',
}

const KUNN_STATUS = {
    RECIEVE: "RECIEVE",
    REFUSE: "REFUSE",
    CHECK_CIC: "CHECK_CIC",
    CHECK_ELIGIBLE: "CHECK_ELIGIBLE",
    CHECK_PCB: "CHECK_PCB",
    IN_CP_QUEUE: "IN_CP_QUEUE",
    IN_CE_QUEUE: "IN_CE_QUEUE",
    CANCELLED: "CANCELLED",
    SIGNED: "SIGNED",
    SIGNED_TO_BE_DISBURED: "SIGNED_TO_BE_DISBURED",
    ACTIVATED: "ACTIVATED",
    ACTIVATED_WITH_DIS_DOCS: "ACTIVATED_WITH_DIS_DOCS",
    NOT_ELIGIBLE: "NOT_ELIGIBLE",
    ELIGIBLE: "ELIGIBLE",
    TERMINATED: "TERMINATED",
    REFUSED: "REFUSED",
    WAITING_CIC_RESULT: "WAITING_CIC_RESULT",
    CANCELED: "CANCELED",
	PASSED_MANUAL_PROCESS: "PASSED_MANUAL_PROCESS",
	PASSED_REVIEW: "PASSED_REVIEW",
	PASSED_TD1: "PASSED_TD1",
	PASSED_TD2: "PASSED_TD2",
	RECEIVED: "RECEIVED",
	RESUBMIT: "RESUBMIT",
	SIGNED_TO_BE_DISBURSED: "SIGNED_TO_BE_DISBURSED",
	SIGNING_IN_PROGRESS: "SIGNING_IN_PROGRESS",
    REJECTED_MANUAL_PROCESS: "REJECTED_MANUAL_PROCESS",
    RESUBMITED: "RESUBMITED",
    WAITING_RESUBMIT: "WAITING_RESUBMIT",
    WAITING_APPROVE_CHANGE_REQUEST: "WAITING_APPROVE_CHANGE_REQUEST",
    APPROVED_CHANGE_REQUEST: "APPROVED_CHANGE_REQUEST",
    EVF_SIGNED: "EVF_SIGNED",
    WAITING_RESUBMIT_SIGNED: "WAITING_RESUBMIT_SIGNED",
    RESUBMITTED_SIGNED: "RESUBMITTED_SIGNED",
    WAITING_EVF_SIGNED: "WAITING_EVF_SIGNED",
    PASSED_CHECK_FULLLOAN: "PASSED_CHECK_FULLLOAN",
    PASSED_CHECK_EKYC: "PASSED_CHECK_EKYC",
    PASSED_CHECK_RENEW_DATE: "PASSED_CHECK_RENEW_DATE",
    WAITING_CUSTOMER_SIGNATURE: "WAITING_CUSTOMER_SIGNATURE"
};

const CONTENT_DISBURSE = {
    CONTENT1: 'EASY SME giai ngan kunn .1 cho ',
    CONTENT2: 'EASY SME giai ngan kunn .2 cho ',
    CONTENT3: 'EASY SME giai ngan kunn .3 cho ',
    CONTENT4: 'EASY SME giai ngan kunn .4 cho '
}

const SIMPLE_CONTENT_DISBURSE =  'EASY SME giai ngan kunn .{i} cho '
const SME_CONTENT_DISBURSE = 'EVF GN KU {KUNN} HD {HD}'

const KUNN_STEP = {
    'RECIEVE': 'RECIEVE',
    'CHECK_CIC_S37': 'CHECK_CIC_S37',
    'CHECK_CIC': 'CHECK_CIC',
    'CHECK_ELIGIBLE': 'CHECK_ELIGIBLE',
    'CHECK_PCB': 'CHECK_PCB',
    'IN_CP_QUEUE': 'IN_CP_QUEUE',
    'IN_CE_QUEUE': 'IN_CE_QUEUE',
    'CANCELLED': 'CANCELLED',
    'ESIGNING': 'ESIGNING',
    'SIGNED': 'SIGNED',
    'SIGNED_TO_BE_DISBURED': 'SIGNED_TO_BE_DISBURED',
    'ACTIVATED': 'ACTIVATED',
    'TERMINATED': 'TERMINATED',
    'ACTIVATED_WITH_DIS_DOCS': 'ACTIVATED_WITH_DIS_DOCS'
}

const ActionAuditCaseStatus = {
    CANCELLED: {
        CODE: 33,
        TEXT: "CANCELLED",
        ACTION: {
            CANCELLED: "CANCELLED"
        },
        STEP_CODE: "CANCEL",
    },
    REFUSED: {
        CODE: 34,
        TEXT: "REFUSED",
        ACTION: {
            REFUSED: "REFUSED"
        },
        STEP_CODE: "REFUSED",
    },
    INACTIVATED: {
        CODE: 35,
        TEXT: "INACTIVATED",
        ACTION: {
            INACTIVATED: "INACTIVATED"
        },
        STEP_CODE: "INACTIVATED",
    },
    IT_CANCEL: {
        CODE: 38,
        TEXT: "IT SUPPORT CANCEL",
        ACTION: {
            CANCEL: "IT CANCEL"
        },
        STEP_CODE: "IT_CANCEL"
    },
    UI_CANCEL: {
        CODE: 40,
        TEXT: "UI SUPPORT CANCEL",
        REASON: "Merchant cancellation",
        ACTION: {
            CANCEL: "CANCELLED"
        },
        STEP_CODE: "UI_CANCELLED"
    }
}

module.exports = {
    caseStatus,
    caseStatusCode,
    STATUS,
    CRM_STATUS,
    VSK_CUSTOMER,
    CALLBACK_STAUS,
    MAPPING_CALLBACK_STATUS,
    VSK_PRODUCT,
    MAPPING_STATUS_CLIENT_CODE,
    MAPPING_STATUS_CLIENT_NAME,
    WORKFLOW_STAGE,
    KUNN_STATUS,
    MISA_CALLBACK_STATUS,
    CONTENT_DISBURSE,
    KUNN_STEP,
    EKYC_RESULT_CODE: {
        REJECT_EKYC: 'REJECT_EKYC',
        PORTAL_EKYC: 'PORTAL_EKYC'
    },
    SIMPLE_CONTENT_DISBURSE,
    ActionAuditCaseStatus,
    SME_CONTENT_DISBURSE
}