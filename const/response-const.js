const RESPONSE_MSG = {
    INTERNAL_SERVER_ERROR: 'Internal Server Error',
    SELECTION_OFFER_RECEIVED: 'The offer selection request is received',
    PARAMS_INVALID: 'Params invalid',
    APPLICATION_RECEIVED: 'application form was received',
    INSERT_LOAN_ERROR: 'error when insert loan contract',
    UPLOAD_DOC_ERROR: 'error when upload document',
    COMPUTE_OFFER_SUCCESS: 'compute offer success',
    COMPUTE_OFFER_ERROR: 'compute offer error',
}

const RES_STT_CODE = {
    SUCCESS: 200,
    BAD_REQUEST: 400,
    SERVER_ERROR: 500
}

const ERROR_CODE = {
    MISSING: 'MISSING',
    FORMAT: 'FORMAT',
    INVALID: 'INVALID'
}

const RESPONSE_CODE_DE = {
    SUCCESS: '0',
    BAD_REQUEST: '-1'
}

const RESPONSE_CODE = {
    SUCCESS : 1,
    FAIL: 0,
    SERVICE_ERROR: -1
}

const MISA_ERROR_CODE = {
  E400: {
    code: "E400",
    message: "Thiếu hoặc sai định dạng tham số",
  },
  E401: {
    code: "E401",
    message: "Hợp đồng không tồn tại hoặc chưa có hạn mức",
  },
  E402: { code: "E402", message: "Thiếu hoặc sai thông tin mã số thuế" },
  E403: { code: "E403", message: "Thiếu hoặc sai định dạng thông tin địa chỉ" },
  E404: { code: "E404", message: "Thiếu thông tin người đại diện pháp luật" },
  E405: {
    code: "E405",
    message: "Thiếu hoặc sai định dạng ngày trả nợ hàng tháng",
  },
  E406: {
    code: "E406",
    message: "Số tiền giải ngân không hợp lệ hoặc không đáp ứng sản phẩm",
  },
  E407: { code: "E407", message: "Thiếu thông tin giải ngân" },
  E408: { code: "E408", message: "Mã ngân hàng không được hỗ trợ" },
  E409: { code: "E409", message: "Tên người thụ hưởng không khớp" },
  E410: { code: "E410", message: "Thiếu thông tin file đề nghị giải ngân" },
  E411: { code: "E411", message: "Đang có khế ước nhận nợ được xử lý" },
  E412: {
    code: "E412",
    message: "Mã khế ước nhận nợ không tồn tại hoặc không hợp lệ",
  },
  E413: { code: "E413", message: "Trạng thái khế ước nhận nợ không hợp lệ" },
  E414: { code: "E414", message: "File chưa có chữ ký" },
  E415: { code: "E415", message: "Hợp đồng đang bị tạm khóa" },
  E500: { code: "E500", message: "Lỗi hệ thống" },
  E501: { code: "E501", message: "Không tải được file" },
}; 

const ERROR_MSG = {
    BAD_REQUEST: 'Bad request'
};


module.exports = {
    RESPONSE_MSG,
    RES_STT_CODE,
    ERROR_CODE,
    RESPONSE_CODE_DE,
    RESPONSE_CODE,
    MISA_ERROR_CODE,
    ERROR_MSG
}