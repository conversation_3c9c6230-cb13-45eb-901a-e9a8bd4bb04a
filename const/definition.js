const collectingDocType = {
  CE: "CE_UPLOAD_DOC",
  USER: "USER_UPLOAD_DOC",
  EXTERNAL: "PARTNER_UPLOAD_DOC",
  SS: "SS_UPLOAD_DOC",
};

const roleCode = {
  CP: "CP",
  CE: "CE",
  SS: "SS",
  MC: "MC",
  RP: "RP",
  CL: "CL",
  CO: "CO",
  DE: "DE",
  customer: "customer",
};

const additionDoc = {
  docGroup: "OTHER DOCUMENTS",
  docGroupSma: "OTHER_DOCUMENTS",
  docName: "ADDITON DOC",
  docType: "SAD",
  minDoc: 1,
  maxDoc: 5,
};

const chekedDocByRoleField = {
  CP: "is_cp_checked",
  CE: "is_ce_checked",
  SS: "is_ss_checked",
};
const TYPE_COLLECTION = {
  DOC_EXTERNAL: "DOC_EXTERNAL",
  DOC_INTERNAL: "DOC_INTERNAL",
};

const request_type = {
  basic: "basic",
  fullLoan: "fullLoan",
};

const REQUEST_TYPE = {
  BASIC: "basic",
  MISA_AF1: "misaAf1",
  FULL_LOAN: "fullLoan",
  MISA_AF2: "misaAf2",
  DIBUR: "dibursement",
  BZHM_AF1: "bzhmAf1",
  BZHM_AF2: "bzhmAf2",
  BZHM_AF3: "bzhmAf3",
  BIZZ_AF1: "bizzAf1",
  BIZZ_AF2: "bizzAf2",
  BIZZ_AF2_OWNER: "bizzAf2Owner",
  BZHM_AF2_OWNER: "bzhmAf2Owner",
  BIZZ_AF3: "bizzAf3",
  FINV_AF1: "finvAf1",
  FINV_AF2: "finvAf2",
  FINV_AF3: "finvAf3",
  MISA_TC1: "misaTc1",
  MISA_TC2: "misaTc2",
};

const PARTNER_CODE = {
  VPL: "VPL",
  KOV: "KOV",
  VTP: "VTP",
  VSK: "VSK",
  SPL: "SPL",
  MISA: "MIS",
  EZBIZ: "EZBIZ",
  MCAPP: "MCA",
  SMA: "SMA",
  SMASYNC: "SMASYNC",
  FINV: "FINV",
  BIZZ: "BIZZ",
  BZHM: "BZHM",
};

const LIST_PARTNER_CODE = ["VPL", "KOV", "VTP", "VSK", "SPL", "MIS", "EZBIZ", "MCA", "SMA", "FINV", "BIZZ", "BZHM"];

const RESPONSE_CODE = {
  RECIEVED: "RECEIVED",
  ERROR: "ERROR",
  SERVER_ERROR: "SERVER_ERROR",
  INVALID_REQUEST: "INVALID_REQUEST",
  ELIGIBLE: "ELIGIBLE",
  NOT_ELIGIBLE: "NOT_ELIGIBLE",
  SUCCESS: "SUCCESS",
};

const MANUAL_TASK_CODE = {
  CP_MANUAL: {
    CP_APPROVE: "CP_APPROVE_DOC",
    CP_RESUBMIT: "CP_RESUBMIT_DOC",
    CP_REJECT: "CP_REJECT",
    CP_CANCEL: "CP_CANCEL",
    CP_APPROVE_CL: "CP_APPROVE_DOC_CL",
  },
  DEDUP_MANUAL: {
    DEDUP_APPROVE: "DEDUP_APPROVE",
    DEDUP_CANCEL: "DEDUP_CANCEL",
  },
  EKYC_MANUAL: {
    EKYC_APPROVE: "EKYC_APPROVE",
    EKYC_REJECT: "EKYC_REJECT",
    EKYC_CANCEL: "EKYC_CANCEL",
  },
  CE_MANUAL: {
    CE_APPROVE_MATCH_OFFER: "CE_APPROVE_MATCH_OFFER",
    CE_APPROVE_UNMATCH_OFFER: "CE_APPROVE_UNMATCH_OFFER",
    CE_CANCEL: "CE_CANCEL",
    CE_RESUBMIT_DE: "CE_RESUBMIT_DE",
    CE_CALCULATE_MAXLOAN: "CE_CALCULATE_MAXLOAN",
  },
  DE_MANUAL: {
    DE_SAVE_SUBMIT: "DE_SAVE_SUBMIT",
  },
  CL_MANUAL: {
    CL_APPROVE: "CL_APPROVE",
  },
  SS_MANUAL: {
    SS_APPROVE: "SS_APPROVE_DOC",
    SS_CALCULATE_MAXLOAN: "SS_CALCULATE_MAXLOAN",
  },
};

const DATE_FORMAT = {
  DDMMYYYY: "DD/MM/YYYY",
  DB_FORMAT: "YYYY-MM-DD",
  DD_MM_YYYY: "DD-MM-YYYY",
  YYYYMMDD_HHmmss: "YYYY-MM-DD HH:mm:ss",
  DDMMYYYY_HHmmss: "DD-MM-YYYY HH:mm:ss",
  DDMMYYYYN: "DDMMYYYY",
};

const TIME_CONVERT = {
  MONTHS_IN_MS: 30 * 24 * 60 * 60 * 1000,
};

const TASK_FLOW = {
  START: "START",
  BASIC: "BASIC",
  CHECK_ELIGIBLE: "CHECK_ELIGIBLE",
  CHECK_ELIGIBLE_INDIVIDUAL: "CHECK_ELIGIBLE_INDIVIDUAL",
  CHECK_FULL_LOAN_INDIVIDUAL: "CHECK_FULL_LOAN_INDIVIDUAL",
  CHECK_CIC: "CHECK_CIC",
  CHECK_PCB: "CHECK_PCB",
  CHECK_DEDUP_MC: "CHECK_DEDUP_MC",
  FULL_LOAN: "FULL_LOAN",
  CHECK_DEDUP: "CHECK_DEDUP",
  CHECK_EKYC: "CHECK_EKYC",
  PUSH_TASK_CP: "PUSH_TASK_CP",
  PUSH_TASK_CE: "PUSH_TASK_CE",
  PUSH_TASK_DE: "PUSH_TASK_DE",
  CHECK_DE: "CHECK_DE",
  MANUAL_PROCESS: "MANUAL_PROCESS",
  CALL_BACK: "CALL_BACK",
  CHECK_DE_PRESCORE: "CHECK_DE_PRESCORE",
  CHECK_DE_INTERNAL_SCORE: "CHECK_DE_INTERNAL_SCORE",
  COMPUTE_OFFER_VDS: "COMPUTE_OFFER_VDS",
  GENERATE_CONTRACT: "GENERATE_CONTRACT",
  COMPUTE_OFFER_VTP: "COMPUTE_OFFER_VTP",
  CHECK_AF1: "CHECK_AF1",
  CHECK_AF2: "CHECK_AF2",
  COMPUTE_OFFER_VSK: "COMPUTE_OFFER_VSK",
  COMPUTE_OFFER_KOV: "COMPUTE_OFFER_KOV",
  CE_RESUBMIT_DE: "CE_RESUBMIT_DE",
  DIBUR_REQUEST: "DIBUR_REQUEST",
  CHECK_CIC_KU: "CHECK_CIC_KU",
  CHECK_PCB_KU: "CHECK_PCB_KU",
  PUSH_TASK_CP_KU: "PUSH_TASK_CP_KU",
  PUSH_TASK_CE_KU: "PUSH_TASK_CE_KU",
  CHECK_DEDUP_SME: "CHECK_DEDUP_SME",
  CHECK_PCB_SME: "CHECK_PCB_SME",
  KOV_CASH_CALCULATE_DI: "KOV_CASH_CALCULATE_DI",
  PUSH_TASK_SS: "PUSH_TASK_SS",
  PUSH_TASK_SS_KU: "PUSH_TASK_SS_KU",
  CHECK_ELIGIBLE_KUNN: "CHECK_ELIGIBLE_KUNN",
  COMPUTE_OFFER: "COMPUTE_OFFER",
  SUBMITED_LOAN_LIMIT: "SUBMITED_LOAN_LIMIT",
  CHECK_NFC_WITHOUT_C06: "CHECK_NFC_WITHOUT_C06",
  CHECK_NFC_WITH_C06: "CHECK_NFC_WITH_C06",
  END_TASK: "END_TASK",
  MANUAL_REVIEW: "MANUAL_REVIEW",
  MANUAL_REVIEW_A3: "MANUAL_REVIEW_A3",
  CHECK_EKYC_ANTI_FRAUD: "CHECK_EKYC_ANTI_FRAUD",
  SUBMIT_AF3: "SUBMIT_AF3",
  CHECK_ELIGIBLE_SME: "CHECK_ELIGIBLE_SME",
  CHECK_DEDUP_SME_V2: "CHECK_DEDUP_SME_V2",
  CHECK_DEDUP_SME_FD: "CHECK_DEDUP_SME_FD",
  CHECK_CIC_DETAIL_SME: "CHECK_CIC_DETAIL_SME",
  KUNN_MANUAL_REVIEW: "KUNN_MANUAL_REVIEW",
  KUNN_TD1: "KUNN_TD1",
  KUNN_TD2: "KUNN_TD2",
  KUNN_GENERATE_TEMPLATE: "KUNN_GENERATE_TEMPLATE",
  KUNN_SIGNING_INPROGRESS: "KUNN_SIGNING_INPROGRESS",
  KUNN_SIGNED: "KUNN_SIGNED",
  KUNN_SIGNED_TO_BE_DISBURSED: "KUNN_SIGNED_TO_BE_DISBURSED",
  KUNN_CREATE_LMS_DEBT: "KUNN_CREATE_LMS_DEBT",
  CHECK_CONTRACT_RENEW_DATE: "CHECK_CONTRACT_RENEW_DATE",
  CHECK_CIC_B11T_SME: "CHECK_CIC_B11T_SME",
  CHECK_WHITELIST_FULL_LOAN: "CHECK_WHITELIST_FULL_LOAN",
  CHECK_MODEL: "CHECK_MODEL",
  CHECK_CONTRACT_RENEW_DATE_AF1: "CHECK_CONTRACT_RENEW_DATE_AF1",
  CHECK_BLACK_LIST: "CHECK_BLACK_LIST",
  MANUAL_REVIEW_A2: "MANUAL_REVIEW_A2",
  CHECK_CONTRACT_RENEW_DATE_AF2: "CHECK_CONTRACT_RENEW_DATE_AF2",
  BIZZ_LIMIT_GENERATE_TEMPLATE: "BIZZ_LIMIT_GENERATE_TEMPLATE",
  SIGN_EVF_SIGNATURE: "SIGN_EVF_SIGNATURE",
  EXPORT_FINANCIAL_REPORT: "EXPORT_FINANCIAL_REPORT",
  KUNN_CHECK_TEMPLATE_EXIST: "KUNN_CHECK_TEMPLATE_EXIST",
  SIGN_EVF_SIGNATURE_KUNN: "SIGN_EVF_SIGNATURE_KUNN",
  AF3_GENERATE_TEMPLATE: "AF3_GENERATE_TEMPLATE",
  AF3_CHECK_TEMPLATE_EXIST: "AF3_CHECK_TEMPLATE_EXIST",
  AF3_BIZZI_SIGNING_INPROGRESS: "AF3_BIZZI_SIGNING_INPROGRESS",
  AF3_BIZZI_SIGNED: "AF3_BIZZI_SIGNED",
  AF3_IN_MANUAL_PROCESS: "AF3_IN_MANUAL_PROCESS",
  AF3_PASSED_MANUAL_REVIEW: "AF3_PASSED_MANUAL_REVIEW",
  AF3_EVF_SIGNING_INPROGRESS: "AF3_EVF_SIGNING_INPROGRESS",
  AF3_EVF_SIGNED: "AF3_EVF_SIGNED",
  ACTIVE_CREDIT_LIMIT: "ACTIVE_CREDIT_LIMIT",
  INACTIVE_CREDIT_LIMIT: "INACTIVE_CREDIT_LIMIT",
  CHECK_WHITELIST_AF1: "CHECK_WHITELIST_AF1",
  SEND_LIMIT_FILES_TO_BIZZI: "SEND_LIMIT_FILES_TO_BIZZI",
  START_KUNN: "START_KUNN",
  RESUBMIT_A2: "RESUBMIT_A2",
  MERCHANT_LIMIT_CREATE: "MERCHANT_LIMIT_CREATE",
  RESUBMIT_A3: "RESUBMIT_A3",
  CALLBACK_CREDIT_LIMIT: "CALLBACK_CREDIT_LIMIT",
  CHECK_A3_FILE_INTEGRITY: "CHECK_A3_FILE_INTEGRITY",
  KUNN_CREATE_CRM_SERVICE: "KUNN_CREATE_CRM_SERVICE",
  START_AF1: "START",
  CHECK_CIC_B11T_INDIVIDUAL: "CHECK_CIC_B11T_INDIVIDUAL",
  CHECK_C06_WITH_FALSE: "CHECK_C06_WITH_FALSE",
  CHECK_C06_WITH_TRUE: "CHECK_C06_WITH_TRUE",
  CHECK_CONTRACT_RENEW_DATE_INDIVIDUAL: "CHECK_CONTRACT_RENEW_DATE_INDIVIDUAL",
  READ_OCR: "READ_OCR",
  CHECK_EKYC_V2: "CHECK_EKYC_V2",
  GENERATE_CONTRACT_FINV: "GENERATE_CONTRACT_FINV",
  APPROVE_AF1: "APPROVE_AF1",
  CHECK_RENEW_DATE_KUNN: 'CHECK_RENEW_DATE_KUNN',
  CHECK_BLACKLIST_KUNN: 'CHECK_BLACKLIST_KUNN',
  CHECK_EKYC_KUNN: 'CHECK_EKYC_KUNN',
  CHECK_FULL_LOAN_KUNN: 'CHECK_FULL_LOAN_KUNN',
  CHECK_CONTRACT_PROGRESS: "CHECK_CONTRACT_PROGRESS",
  CHECK_CONTRACT_ACTIVE: "CHECK_CONTRACT_ACTIVE",
  FINV_CHECK_ELIGIBLE_KUNN: "FINV_CHECK_ELIGIBLE_KUNN",
  CALLBACK_KUNN_STATUS: "CALLBACK_KUNN_STATUS",
  CALLBACK_CIC: "CALLBACK_CIC",
  APPROVE_AF2: "APPROVE_AF2"
};

const SERVICE_NAME = {
  EKYC: "EKYC",
  CHECK_EKYC: "CHECK_EKYC",
  DE: "DE",
  GET_OFFER: "GET_OFFER",
  DE_PRESCORE: "DE_PRESCORE",
  DE_INTERNAL_SCORE: "DE_INTERNAL_SCORE",
  CRM: "CRM",
  CRM_DEDUP: "CRM_DEDUP",
  CRM_CREATE_SERVICE: "CRM_CREATE_SERVICE",
  CRM_UPDATE_APPROVE: "CRM_UPDATE_APPROVE",
  CRM_UPDATE_CUSTOMER: "CRM_UPDATE_CUSTOMER",
  LMS: "LMS",
  CREATE_LOAN_ACCOUNT: "CREATE_LOAN_ACCOUNT",
  MASTERDATA: "MASTERDATA",
  AAD: "AAD",
  PUSH_TASK_CP: "PUSH_TASK_CP",
  PUSH_TASK_CE: "PUSH_TASK_CE",
  PUSH_TASK_DE: "PUSH_TASK_DE",
  ELIGIBLE: "ELIGIBLE",
  CHECK_ELIGIBLE: "CHECK_ELIGIBLE",
  S37: "S37",
  CHECK_PCB: "CHECK_PCB",
  PRODUCT: "PRODUCT",
  SMS: "SMS",
  CHECK_PCB_KUNN: "CHECK_PCB_KUNN",
  CHECK_CIC_KUNN: "CHECK_CIC_KUNN",
  CREATE_KUNN: "CREATE_KUNN",
  ANTI_FRAUD: "ANTI_FRAUD",
  CIC_DETAIL: "CIC_DETAIL",
  MC_LOS: "MC_LOS",
  UI_REVIEW: "UI_REVIEW",
  EXTERNAL: "EXTERNAL",
  CONVERT_SERVICE: "CONVERT_SERVICE",
  CHECK_NFC_WITHOUT_C06: "CHECK_NFC_WITHOUT_C06",
  CHECK_NFC_WITH_C06: "CHECK_NFC_WITH_C06",
  B11T: "B11T",
  TD1: "TD1",
  CHECK_CIC_B11T_SME: "CHECK_CIC_B11T_SME",
  KUNN_GENERATE_TEMPLATE: "KUNN_GENERATE_TEMPLATE",
  KUNN_CHECK_TEMPLATE_EXIST: "KUNN_CHECK_TEMPLATE_EXIST",
  AF3_CALLBACK: "AF3_CALLBACK",
  AF3_CHECK_TEMPLATE_EXIST: "AF3_CHECK_TEMPLATE_EXIST",
  KUNN_CALLBACK: "KUNN_CALLBACK",
  CALLBACK_3P: "CALLBACK_3P",
  CRM_UPDATE_INFO: "CRM_UPDATE_INFO",
  CRM_UPDATE_ACTIVE: "CRM_UPDATE_ACTIVE",
  CRM_UPDATE_INACTIVE: "CRM_UPDATE_INACTIVE"
};

const SYS_CHANEL = {
  MCC: "MCC",
};

const CONTRACT_TYPE = {
  CREDIT_LINE: "CREDITLINE",
  CASH_LOAN: "CASHLOAN",
};

const OFFER_CONFIG = {
  MIN_VPL_OFFER: 10000000,
};

const FILE_STORAGE = {
  storageContractSignedPath: "/los-united/signed-contract",
  storageContractUnSignedPath: "/los-united/unsigned-contract",
  storageCustomerDocs: "/los-united/customer-docs",
  storageAnotherFile: "/los-united/another",
  storageWelcomePackageFile: "/los-united/welcome-package",
  storageEKYCFile: "/los-united/ekyc",
  storageCCNSignedFile: "/los-united/ccn-signed",
  storageQDCVHmMisaFile: "/los-united/misa/bctd",
  storageMisaDocs: "/los-united/customer-docs/mis",
};

const VTP_CUST_SCHEMA = {
  STANDARD: "STANDARD",
  VIP: "VIP",
  PREMIUM: "PREMIUM",
};

const DKKD_INFO = {
  // có đăng ký kinh doanh.
  CO_DKKD: "Y",
  // không đăng ký kinh doanh.
  KO_DKKD: "N",
};

const BUNDLE_STAGE = {
  PRE_SCORING: "PRE_SCORING",
  DIBURSEMENT: "DIBURSEMENT",
  SIGNING: "SIGNING",
};

const TURNOVER_TYPE = {
  TURNOVER: "TURNOVER",
  TRANSACTION: "TRANSACTION",
  TURNOVER_AFTER_LOAN: "TURNOVER_AFTER_LOAN",
  PROFIT_AFTER_LOAN: "PROFIT_AFTER_LOAN",
};

const IGNORE_LOG_LIST = ["/los-mc-credit/v1/ss/uploadResubmit", "/v2/misa/download-misa-file-test","/v1/loan-request/export"];
const ENABLE_LOG_LIST = ["/misa/bank-info"];
const VTP_MAXLOAN = *********;
const DISURSEMENT_METHOD = {
  TRANSFER: "Transfer",
};
const CHANNEL = {
  MC: "MC",
  SMA: "SMA",
};
const LIST_SUPER_APP_DOCS = ["SPID", "SNID", "SPIC", "SVRD", "SVCI", "SCAI", "SCAR"];
const SMA_PAYMENT_METHOD = ["PPEOP", "PIM"];

const ERROR_CODE = {
  SUCCESS: 0,
  ERROR: 1,
  INVALID_REQUEST: -1,
  INT_SERVER_ERROR: -2,
};

const RESP_MESSAGE = {
  SUCCESS: `successfully`,
  INVALID_REQUEST: `invalid request`,
  INT_SERVER_ERROR: `internal server error`,
};

const PARTNER_TYPE = {
  INT: "IN",
  OUT: "OUT",
};

const LOCK_STATUS = {
  ACTIVE: 0,
  LOCKED: 1,
};

const CIC_STEP_CHECK = {
  AF1: "AF1",
  AF2: "AF2",
  AF2_DETAIL: "AF2_DETAIL",
  KUNN: "KUNN",
  TC1: "TC1",
  TC2: "TC2",
  TC2_DETAIL: "TC2_DETAIL",
};

const FILE_TYPE_MISA = {
  BCTD: "BCTD", //Báo cáo thẩm định
  HDHM: "HDHM", //Hợp đồng hạn mức
};

const BUSINESS_TYPE = {
  "01": "01", //Doanh Nghiệp Tư nhân
  "02": "02", //Công ty TNHH
  "03": "03", //Công ty Cổ phần
  "04": "04", //Công ty TNHH một thành viên
};

const MisaStep = {
  AF1: "AF1",
  AF2: "AF2",
  TC1: "TC1",
  TC2: "TC2",
  MODEL: "MODEL",
  AF3: "AF3",
  SIGNATURE: "SIGNATURE",
  KUNN: "KUNN",
  CHECK_CIC: "CHECK_CIC",
  CHECK_ELIGIBLE: "CHECK_ELIGIBLE",
  CHECK_CIC_DETAIL: "CHECK_CIC_DETAIL",
  SEND_RESULT_AF1: "SEND_RESULT_AF1",
  SEND_RESULT_AF2: "SEND_RESULT_AF2",
  SEND_RESULT_TC1: "SEND_RESULT_TC1",
  SEND_RESULT_TC2: "SEND_RESULT_TC2",
  SEND_BCTD: "SEND_BCTD",
  SEND_CONTRACT_FILE: "SEND_CONTRACT_FILE",
  UNKNOWN_STEP: "UNKNOWN_STEP",
  RECEIVED_MODEL_RESULT: "RECEIVED_MODEL_RESULT",
  RECEIVED_SIGNED_FILE: "RECEIVED_SIGNED_FILE",
  EVF_START_SIGNING: "EVF_START_SIGNING",
  EVF_SIGNED: "EVF_SIGNED",
  CHECK_NFC: "CHECK_NFC",
};

const KUNN_WORKFLOW = {
  START_KUNN: "START_KUNN",
  KUNN_MANUAL_REVIEW: "KUNN_MANUAL_REVIEW",
  KUNN_TD1: "KUNN_TD1",
  KUNN_TD2: "KUNN_TD2",
  KUNN_GENERATE_TEMPLATE: "KUNN_GENERATE_TEMPLATE",
  CHECK_RENEW_DATE: "CHECK_RENEW_DATE",
  CHECK_BLACK_LIST: "CHECK_BLACK_LIST",
  CHECK_B11_SME: "CHECK_B11_SME",
  KUNN_SIGNING_INPROGRESS: "KUNN_SIGNING_INPROGRESS",
  KUNN_SIGNED: "KUNN_SIGNED",
  KUNN_SIGNED_TO_BE_DISBURSED: "KUNN_SIGNED_TO_BE_DISBURSED",
  KUNN_CHECK_TEMPLATE_EXIST: "KUNN_CHECK_TEMPLATE_EXIST",
  CALLBACK: "CALLBACK",
  CHECK_RENEW_DATE_KUNN: "CHECK_RENEW_DATE_KUNN",
  CHECK_ELIGIBLE_KUNN: "CHECK_ELIGIBLE_KUNN",
  CHECK_BLACKLIST_KUNN: "CHECK_BLACKLIST_KUNN",
  CHECK_EKYC_KUNN: "CHECK_EKYC_KUNN",
  CHECK_FULL_LOAN_KUNN: "CHECK_FULL_LOAN_KUNN"
};

const BZHMStep = {
  AF1: "AF1",
  AF2: "AF2",
  MODEL: "MODEL",
  AF3: "AF3",
  SIGNATURE: "SIGNATURE",
  KUNN: "KUNN",
  CHECK_CIC: "CHECK_CIC",
  CHECK_ELIGIBLE: "CHECK_ELIGIBLE",
  CHECK_CIC_DETAIL: "CHECK_CIC_DETAIL",
  SEND_RESULT_AF1: "SEND_RESULT_AF1",
  SEND_RESULT_AF2: "SEND_RESULT_AF2",
  SEND_BCTD: "SEND_BCTD",
  SEND_CONTRACT_FILE: "SEND_CONTRACT_FILE",
  UNKNOWN_STEP: "UNKNOWN_STEP",
  RECEIVED_MODEL_RESULT: "RECEIVED_MODEL_RESULT",
  RECEIVED_SIGNED_FILE: "RECEIVED_SIGNED_FILE",
  EVF_START_SIGNING: "EVF_START_SIGNING",
  EVF_SIGNED: "EVF_SIGNED",
  CHECK_NFC: "CHECK_NFC",
  ACTIVE_CREDIT_LIMIT: "ACTIVE_CREDIT_LIMIT",
};

const BizziHmStep = {
  AF1: "AF1",
  AF2: "AF2",
  MODEL: "MODEL",
  AF3: "AF3",
  SIGNATURE: "SIGNATURE",
  KUNN: "KUNN",
  CHECK_CIC: "CHECK_CIC",
  CHECK_ELIGIBLE: "CHECK_ELIGIBLE",
  CHECK_CIC_DETAIL: "CHECK_CIC_DETAIL",
  SEND_RESULT_AF1: "SEND_RESULT_AF1",
  SEND_RESULT_AF2: "SEND_RESULT_AF2",
  SEND_BCTD: "SEND_BCTD",
  SEND_CONTRACT_FILE: "SEND_CONTRACT_FILE",
  UNKNOWN_STEP: "UNKNOWN_STEP",
  RECEIVED_MODEL_RESULT: "RECEIVED_MODEL_RESULT",
  RECEIVED_SIGNED_FILE: "RECEIVED_SIGNED_FILE",
  EVF_START_SIGNING: "EVF_START_SIGNING",
  EVF_SIGNED: "EVF_SIGNED",
  CHECK_NFC: "CHECK_NFC",
  ACTIVE_CREDIT_LIMIT: "ACTIVE_CREDIT_LIMIT",
};

const DOCUMENT_REFERENCE_TABLE = {
  LOAN_CUSTOMER_REPRESENTATIONS: "loan_customer_representations",
  LOAN_CUSTOMER_SHAREHOLDERS: "loan_customer_shareholders",
  LOAN_CUSTOMER_MANAGERS: "loan_customer_managers",
  LOAN_CUSTOMER_WAREHOUSES: "loan_customer_warehouses",
  LOAN_CUSTOMER_PARTNERS: "loan_customer_partners",
  LOAN_CUSTOMER: "loan_customer",
  LOAN_BUSINESS_OWNER: "loan_business_owner",
  LOAN_BRANCH_ADDRESS: "loan_branch_address",
  LOAN_REVENUES: "loan_revenues",
  LOAN_VAT_FORMS: "loan_vat_forms",
};

const WORKFLOW_CODE = {
  FINV_AF1: "FINV_AF1",
  FINV_AF2: "FINV_AF2"
}

module.exports = {
  collectingDocType,
  roleCode,
  additionDoc,
  chekedDocByRoleField,
  TYPE_COLLECTION,
  request_type,
  REQUEST_TYPE,
  PARTNER_CODE,
  RESPONSE_CODE,
  DATE_FORMAT,
  TIME_CONVERT,
  TASK_FLOW,
  MANUAL_TASK_CODE,
  SERVICE_NAME,
  SYS_CHANEL,
  FILE_STORAGE,
  KUNN_WORKFLOW,
  BZHMStep,
  MESSAGE_NOTI: {
    SUCCESS: `Success`,
    FAIL: `Fail`,
    DATA_NOT_FOUND: `Data not found`,
  },
  MOMENT_FORMAT: {
    DATE_BE: "YYYY-MM-DD",
  },
  STATUS_HDHM: {
    KH05: "KH05", // A1 finish
    KH08: "KH08", // Esign
    CA01: "CA01", // Cancel
  },
  STATUS_KUNN: {
    KKH09: "KKH09", // Esign
    KCA01: "KCA01", // Cancel
  },
  BUDGET_ANALYSIS: {
    REVENUE_GROWTH_RATE: 125,
    MIN_CAPITAL: 20,
    COST_PROVINCE: ["01", "79", "48"], // HN, HCM, DN
    COST_LIVE_CITY: 6000000,
    COST_LIVE_ANOTHER: 4000000,
  },
  OFFER_CONFIG,
  CONTRACT_TYPE,
  VTP_CUST_SCHEMA,
  DKKD_INFO,
  VTP_MAXLOAN,
  BUNDLE_STAGE,
  IGNORE_LOG_LIST,
  DISURSEMENT_METHOD,
  TURNOVER_TYPE,
  DOC_TYPE: {
    LCT: "LCT",
    LD: "LD",
    SPAR: "SPAR",
    FVR: "FVR",
    SFSTD2: "SFSTD2",
    SNFS2: "SNFS2",
    LCTKU: "LCTKU",
    TTRV: "TTRV",
    ID_CARD: {
      FRONT: "PID",
      BACK: "PID1",
    },
    SELFIE: "SPIC",
    VIDEO: "VSRE",
    SPIDOS: "SPIDOS",
    SBIZ: "SBIZ",
    STAX: "STAX",
    SPCB: "SPCB",
    SVATSTA: "SVATSTA",
    PTC: "PTC",
    SLOH: "SLOH",
    SOLH: "SOLH",
    SBLSTA: "SBLSTA",
    SCPS: "SCPS",
    RIOD: "RIOD",
    NFC: "NFC",
  },
  PRODUCT_CODE: {
    MCTAX_HMTD: "MCTAX_HMTD",
    KU_MCTAX_HMTD: "KU_MCTAX_HMTD",
    KIOT_VIET_KU: "KIOT_VIET_KU",
    SUPER_APP: {
      LIMIT: ["SMA_MCTAX_HMTD", "SMA_MC_BHH_HMTD", "SMA_MC_CAR_HMTD", "SMA_MC_HOUSE_HMTD"],
      WITHDRAW_WITHOUT_LIMIT: ["SMA_MCTAX_VM", "SMA_MC_BHH_VM", "SMA_MC_CAR_VM", "SMA_MC_HOUSE_VM"],
      WITHDRAW: ["SMA_MCTAX_HMTD_KU", "SMA_KU_MC_BHH_HMTD", "SMA_KU_MC_CAR_HMTD", "SMA_KU_MC_HOUSE_HMTD", "SMA_MCTAX_VM", "SMA_MC_BHH_VM", "SMA_MC_CAR_VM", "SMA_MC_HOUSE_VM"],
      SMA_KU_MCTAX_HMTD: "SMA_MCTAX_HMTD_KU",
      SMA_KU_MC_BHH_HMTD: "SMA_KU_MC_BHH_HMTD",
      SMA_KU_MC_CAR_HMTD: "SMA_KU_MC_CAR_HMTD",
      SMA_KU_MC_HOUSE_HMTD: "SMA_KU_MC_HOUSE_HMTD",
      SMA_MCTAX_HMTD: "SMA_MCTAX_HMTD",
      SMA_MCTAX_VM: "SMA_MCTAX_VM",
      SMA_MC_BHH_HMTD: "SMA_MC_BHH_HMTD",
      SMA_MC_BHH_VM: "SMA_MC_BHH_VM",
      SMA_MC_CAR_HMTD: "SMA_MC_CAR_HMTD",
      SMA_MC_CAR_VM: "SMA_MC_CAR_VM",
      SMA_MC_HOUSE_HMTD: "SMA_MC_HOUSE_HMTD",
      SMA_MC_HOUSE_VM: "SMA_MC_HOUSE_VM",
    },
    FINV: {
      LIMIT: {
        HKD_FINVIET_HM_SILVER: "HKD_FINVIET_HM_SILVER",
        HKD_FINVIET_HM_STANDARD: "HKD_FINVIET_HM_STANDARD",
        HKD_FINVIET_HM_GOLD: "HKD_FINVIET_HM_GOLD",
        HKD_FINVIET_HM_DIAMOND: "HKD_FINVIET_HM_DIAMOND",
      },
    },
    MISA: {
      SME_MISA_HM_DIAMOND: "SME_MISA_HM_DIAMOND",
      SME_MISA_HM_GOLD: "SME_MISA_HM_GOLD",
      SME_MISA_HM_SILVER: "SME_MISA_HM_SILVER",
      SME_MISA_HM_PLATINUM_LS: "SME_MISA_HM_PLATINUM_LS",
      SME_MISA_HM_PREMIUM_LS: "SME_MISA_HM_PREMIUM_LS",
      SME_MISA_HM_STANDARD_LS: "SME_MISA_HM_STANDARD_LS",
      SME_MISA_HM_VIP_LS: "SME_MISA_HM_VIP_LS",
    },
  },
  LOCK_STATUS,
  MANUAL_DECISION: {
    APPROVE: "APPROVE",
    RESUBMIT: "RESUBMIT",
    REJECT: "REJECT",
    CANCEL: "CANCEL",
  },
  CHANNEL,
  LIST_SUPER_APP_DOCS,
  SMA_PAYMENT_METHOD,
  LIST_PARTNER_CODE,
  ERROR_CODE,
  RESP_MESSAGE,
  PARTNER_TYPE,
  CIC_STEP_CHECK,
  FILE_TYPE_MISA,
  BUSINESS_TYPE,
  MisaStep,
  ENABLE_LOG_LIST,
  LOS_TYPE: "MC_LOS",
  BizziHmStep,
  DOCUMENT_REFERENCE_TABLE,
  WORKFLOW_CODE,
  DEFAULT_PAYMENT_DATE : ['05', '15', '25']};
