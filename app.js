const express = require("express");
let app = express();
let httpServer = require("http").Server(app);
const multer = require("multer");
const bp = require("body-parser");
require("dotenv").config();
// app.use(express.json());
app.use(express.text({limit: '10mb'}), express.json({limit: '10mb' }), express.raw({ type: "text/xml" }));
app.use(bp.json({limit: '10mb' }));
app.use(bp.urlencoded({ limit: '10mb', extended: true }));

const { loadConvertCache } = require("./utils/converter/initCache");
const { initWFCache, routing, initWFCacheV2 } = require("./services/workflow-service");
const { offerConfig } = require("./repositories/offer");
const serviceName = "los";
const common = require("./utils/common.js");
const {
  encryptDataMisa,
  decryptFileMisa,
  encryptFileMisa,
  encryptDataFinv,
  decryptDataFinv,
  decryptDataMisa,
} = require("./utils/encrypt/encrypt.js");
const {
  decryptRequestMisa,
  encryptResponseMisa,
  decryptRequestFinv,
  encryptResponseFinv,
} = require("./utils/middleware.js");

const Pool = require("pg").Pool;
const kunnV2 = require("./routes/kunn-v2-router.js");

let NODE_ENV = process.env.NODE_ENV;
require("dotenv").config({ path: `.env.${NODE_ENV}` });
let env = process.env.HOST_ENV;
let configurationServiceLink =
  `${process.env.HOST_CONFIGURATION}/services?service=` + serviceName;
console.log("configurationServiceLink", configurationServiceLink);
let poolRead;
let poolWrite;
let config;

common
  .getAPI(configurationServiceLink, { "Content-Type": "application/json" })
  .then(async (data) => {
    if (data == undefined) {
      console.log("Khong lay duoc cau hinh:");
    } else {
      config = data;
      poolWrite = new Pool({
        database: process.env.DB_DATABASE,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        port: process.env.DB_PORT,
        host: process.env.DB_HOST_WRITE,
      });
      poolRead = new Pool({
        database: process.env.DB_DATABASE,
        user: process.env.DB_USER,
        password: process.env.DB_PASSWORD,
        port: process.env.DB_PORT,
        host: process.env.DB_HOST_READ,
      });
      config.offerConfig = await offerConfig(poolRead);
      global.config = config;
      global.poolRead = poolRead;
      global.poolWrite = poolWrite;
      global.convertCache = await loadConvertCache(poolRead);
      global.workflowCache = await initWFCache(poolRead);
      let port = config.data.http.port;
      httpServer.listen(port);
      global.config.env = env;
      global.env = process.env;
      global.workflowCacheV2 = await initWFCacheV2(poolRead);
      console.log("Service phuc vu o port:" + port);
    }
  })
  .catch((error) => {
    console.log("Khong lay duoc cau hinh:" + error);
    status = -1;
  });

app.use(function (req, res, next) {
  req.poolWrite = poolWrite;
  req.poolRead = poolRead;
  req.config = config;
  next();
});

const a1Service = require("./a1_application/a1-recieve-service");
const a2Service = require("./a2_application/a2-recieve-service");
const dedupService = require("./services/crm-service");
const deService = require("./services/de-service");
const a1GatewayService = require("./a1_application/a1_gateway");
const uploadDocService = require("./upload_document/document-service");
const genContractService = require("./contract/gen_contract");
const contractService = require("./contract/signed_and_call_lms.js");
const contractInfoService = require("./contract/get-contract-info.js");
const getBundleService = require("./UI_API/getBundle-service");
const loanContractUpdateRequestService = require("./UI_API/loanContractUpdateRequest-service");
const getCommentService = require("./UI_API/getComment-service");
const updateCheckListService = require("./UI_API/updateCheckList-service");
const authenService = require("./utils/aaaService");
const vpl = require("./routes/vpl-router");
const vtp = require("./routes/vtp-router");
const gateway = require("./routes/gateway-router");
const apiRouter = require("./routes/base-api-router");
const apiSmeRouter = require("./routes/base-sme-api-router");
const uiRouter = require("./routes/ui-router");
const kov = require("./routes/kov-router");
const vsk = require("./routes/vsk-router");
const spl = require("./routes/spl-router");
const kunn = require("./routes/kunn-router");
const a1 = require("./routes/a1-router");
const a2 = require("./routes/a2-router");
const misa = require("./routes/sme-misa-router");
const autoCancel = require("./routes/job-cancel-router");
const review = require("./routes/review-router");
const marketingRouter = require("./routes/marketing-router");
// const review = require("./routes/review-router")
const externalRouter = require("./routes/external-router");
const { receiveContractSme } = require("./contract/sme_flow_esign");
const { logIncoming } = require("./utils/loggingService");
const appMcRouter = require("./routes/app-mc-router");
const antiFraud = require("./routes/anti-fraud-router");
const bizziRouter = require("./routes/bizzi-router");
const bizziLimitRouter = require("./routes/bizzi-limit-router");
const smeIntRouter = require("./routes/sme-int-router");
const cicReportRouter = require("./routes/cic-router");
const jobRouter = require("./routes/job-router");
const finvRouter = require("./routes/finv-router");
 
app.use(logIncoming);

app.use("/los-mc-credit/v1", cicReportRouter);
app.use("/los-mc-credit/v1/anti-fraud", antiFraud);
app.use("/los-mc-credit/v1/vds", vpl);
app.use("/los-mc-credit/v1/vtp/mc", vtp);
app.use("/los-mc-credit/v1/gateway", gateway);
app.use("/los-mc-credit/v1", apiRouter);
app.use("/los-mc-credit/v1/sme", apiSmeRouter);
app.use("/los-mc-credit/v1/", uiRouter);
app.use("/los-mc-credit/v1/ui", uiRouter);
app.use("/los-mc-credit/v1", kov);
app.use("/los-mc-credit/v1/vsk", vsk);
app.use("/los-mc-credit/v1/spl", spl);
app.use("/los-mc-credit/v1/kunn", kunn);
app.use("/los-mc-credit/v2", kunnV2);
app.use("/los-mc-credit/v1/a1", a1);
app.use("/los-mc-credit/v1/a2", a2);
app.use("/los-mc-credit/", misa);
app.use("/los-mc-credit/v1", autoCancel);
app.use("/los-mc-credit/v1/review", review);
app.use("/los-mc-credit/v1", marketingRouter);
// app.use('/los-mc-credit/v1/review',review)
app.use("/los-mc-credit/v1", externalRouter);
app.use("/los-mc-credit/v1/app-mc", appMcRouter);
const superAppRouter = require("./routes/super-app-router");
app.use("/los-mc-credit/v1/sma", superAppRouter);
app.use("/los-mc-credit/v1/bizz-limit", bizziLimitRouter);
app.use("/los-mc-credit/", bizziRouter);
app.use("/los-mc-credit/ui/lender",smeIntRouter);
app.use("/los-mc-credit/", jobRouter);
app.use("/los-mc-credit/", finvRouter);

app.get("/los-mc-credit/v1/health", (req, res) => {
  res.status(200).json({
    status: 1,
    msg: "Service hoat dong binh thuong",
  });
});
app.get("/los-mc-credit/v1/hello", (req, res) => {
  res.status(200).json({
    status: 1,
    msg: "Hello World 3",
  });
});
app.post(
  "/los-mc-credit/decrypt-data",
  async (req, res) => {
    try {
      const { body } = req;
      const partnerCode = req.query.partnerCode;
      let decrypted ;
      if(partnerCode === 'FINV'){
        decrypted =  await decryptDataFinv(body, { misaEcPublicKey: global.env.EVN_EC_PUBLIC_KEY });
      }
      else{
        decrypted = await decryptDataMisa(body);
      }
      res.status(200).json({
        reqBody: decrypted,
        code: 1,
      });
    } catch (error) {
      console.log(`error ${error}`);
      res.status(200).json({
        msg: "Error",
        code: -1,
      });
    }
  }
);
app.post("/los-mc-credit/pgp-decrypt", async (req, res) => {
  try {
    // const { body } = req;
    const file = fs.readFileSync("/home/<USER>/Downloads/test.docx");

    const decrypted = await decryptFileMisa(file);
    fs.writeFileSync(
      "/home/<USER>/Downloads/result.docx",
      decrypted
    );

    res.status(200).json({
      data: decrypted,
      code: 1,
    });
  } catch (error) {
    console.log(`error ${error}`);
    res.status(200).json({
      msg: "Error",
      code: -1,
    });
  }
});
const fs = require("fs");
const { generateBctdMisa } = require("./utils/misa-file-handler.js");
app.post("/los-mc-credit/pgp-encrypt", async (req, res) => {
  try {
    // const file = fs.readFileSync('/home/<USER>/abc.txt');
    // const { body } = req;
    const data = await generateBctdMisa(req.body.contractNumber);
    // const encrypted = await encryptFileMisa(file, Buffer.from(global.env.MISA_PGP_PUBLIC_KEY, "base64").toString("utf-8"));
    res.status(200).json({
      data: data,
      code: 1,
    });
  } catch (error) {
    console.log(`error ${error}`);
    res.status(200).json({
      msg: "Error",
      code: -1,
    });
  }
});
app.post("/los-mc-credit/encrypt-data", async (req, res) => {
  try {
    const { body } = req;
    const partnerCode = req.query.partnerCode;
    let encrypted;
    if(partnerCode === 'FINV'){
      encrypted = await encryptDataFinv(body);
    }
    else{
      encrypted = await encryptDataMisa(body, {
        misaRsaPublicKey: global.env.EVN_RSA_PUBLIC_KEY,
      });
    } 
    res.status(200).json({
      data: encrypted,
      code: 1,
    });
  } catch (error) {
    console.log(`error ${error}`);
    res.status(200).json({
      msg: "Error",
      code: -1,
    });
  }
});
app.post(
  "/los-mc-credit/test",
  decryptRequestMisa,
  encryptResponseMisa,
  async (req, res) => {
    try {
      res.status(200).json({
        reqBody: req.body,
        code: 1,
      });
    } catch (error) {
      console.log(`error ${error}`);
      res.status(200).json({
        msg: "Error",
        code: -1,
      });
    }
  }
);
app.get("/los-mc-credit/v1/cache/reload", (req, res) => {
  try {
    common
      .getAPI(configurationServiceLink, { "Content-Type": "application/json" })
      .then(async (data) => {
        if (data == undefined) {
          common.log("Khong lay duoc cau hinh:");
        } else {
          config = data;
          config.offerConfig = await offerConfig(poolRead);
          global.config = data;
          global.config.env = env;
          global.convertCache = await loadConvertCache(poolRead);
          global.workflowCache = await initWFCache(poolRead);
          global.env = process.env;
          res.status(200).json({
            status: 1,
            msg: "Reload config successfully",
            config: data,
          });
        }
      })
      .catch((error) => {
        common.log("Khong lay duoc cau hinh:" + error.message);
        console.log(error);
      });
  } catch (e) {
    common.log(e);
    res.status(502).json({
      status: -4,
      msg: "Reload config failed: " + e.message,
    });
  }
});

const { KOVBasicValidate } = require("./utils/validator/kov-validator");
app.post(
  "/los-mc-credit/v1/application1",
  authenService.authenticate,
  KOVBasicValidate,
  a1Service.a1_recieve
);

app.post(
  "/los-mc-credit/v1/application2",
  authenService.authenticate,
  a2Service.a2_recieve
);

app.post("/los-mc-credit/v1/genContract", genContractService.generateContract);

app.post(
  "/los-mc-credit/v1/genCalculatorRepayment",
  genContractService.genCalculatorRepayment
);

app.post(
  "/los-mc-credit/v1/callback/signedContract",
  contractService.completed
);

app.post("/los-mc-credit/v1/mc-gateway/deduplicatoin", dedupService.checkDedup);

app.post("/los-mc-credit/v1/mc-gateway/eligible", deService.checkEligibleMc);

app.post("/los-mc-credit/v1/mc-gateway/pcb", deService.checkPCBMc);

app.post("/los-mc-credit/v1/mc-gateway/cic", deService.checkCIC);

app.post(
  "/los-mc-credit/v1/mc-gateway/a1-application",
  a1GatewayService.a1GateWay
);

app.post(
  "/los-mc-credit/v1/resubmit",
  authenService.authenticate,
  uploadDocService.resubmitDoc
);

const worstFlowService = require("./manual_flow/recieve_status");
const productInfoService = require("./UI_API/getProductInfo-service.js");
const saveOfferService = require("./UI_API/saveOffer-service.js");
const saveCheckListService = require("./UI_API/saveChecklist-service");
const getBudgetService = require("./UI_API/getBudget-service");

app.get("/los-mc-credit/v1/getLoanContractNotUpdate", loanContractUpdateRequestService.getLoanContractNotUpdate);
app.post("/los-mc-credit/v1/createLoanContractUpdateRequest", loanContractUpdateRequestService.createLoanContractUpdateRequest);
app.post("/los-mc-credit/v1/updateLoanContractUpdateRequest", loanContractUpdateRequestService.updateLoanContractUpdateRequest);
app.post("/los-mc-credit/v1/deleteLoanContractUpdateRequest", loanContractUpdateRequestService.deleteLoanContractUpdateRequest);
app.post("/los-mc-credit/v1/requestLoanContractUpdateRequest", loanContractUpdateRequestService.requestLoanContractUpdateRequest);
app.post("/los-mc-credit/v1/approveLoanContractUpdateRequest", loanContractUpdateRequestService.approveLoanContractUpdateRequest);
app.post("/los-mc-credit/v1/rejectLoanContractUpdateRequest", loanContractUpdateRequestService.rejectLoanContractUpdateRequest);
app.get("/los-mc-credit/v1/getLoanContractUpdateRequest", loanContractUpdateRequestService.getLoanContractUpdateRequest);

app.get("/los-mc-credit/v1/getBundleList", getBundleService.getBundle);

app.get(
  "/los-mc-credit/v1/getDocumentWithoutCheckDocs",
  getBundleService.getDocumentWithoutCheckDocs
);

app.get(
  "/los-mc-credit/v1/getDocumentWithoutCheckDocsV3",
  getBundleService.getDocumentWithoutCheckDocsV3
);

app.get(
  "/los-mc-credit/v1/caseAction/getDocumentWithoutCheckDocsV2",
  getBundleService.getDocumentWithoutCheckDocsV2
);

app.get("/los-mc-credit/v1/getCmtList", getCommentService.getCmt);

app.get("/los-mc-credit/v1/getCECmtList", getCommentService.getCECmt);

app.post(
  "/los-mc-credit/v1/updateCaseStatus",
  worstFlowService.updateCaseCenter
);

app.post(
  "/los-mc-credit/v1/updateCheckList",
  updateCheckListService.updateCheckList
);

app.get("/los-mc-credit/v1/getVariableList", productInfoService.getVariable);

app.get(
  "/los-mc-credit/v1/getChecklistData",
  productInfoService.getChecklistData
);

app.get("/los-mc-credit/v1/getBudgetData", productInfoService.getBudgetData);

app.get("/los-mc-credit/v1/getCICData", productInfoService.getCICData);

app.post("/los-mc-credit/v1/saveOffer", saveOfferService.saveOffer);

app.post("/los-mc-credit/v1/saveCheckList", saveCheckListService.saveCheckList);

app.get("/los-mc-credit/v1/getBudgetInfo", getBudgetService.getBudgetData);

app.get("/los-mc-credit/v1/getDocumentLOV", getCommentService.getDocumentLOV);

const donwloadContract = require("./contract/download_contract");
const uploadSignedContract = require("./contract/upload_contract");

app.get(
  "/los-mc-credit/v1/downloadContract",
  donwloadContract.downloadFileContract
);
app.get(
  "/los-mc-credit/v1/downloadFileContractSme",
  authenService.authenticate_oauth2,
  donwloadContract.downloadFileContractForSmePartner
);

const LCT = [{ name: "LCT", maxCount: 1 }];
app.post(
  "/los-mc-credit/v1/uploadSignedFile",
  multer({
    limits: { fileSize: 5000000 },
    files: 1,
    parts: 10,
  }).fields(LCT),
  uploadSignedContract.uploadContractSignedFile
);

app.get(
  "/los-mc-credit/v1/contractInfo",
  contractInfoService.getEsignContractInfo
);

const s3Service = require("./upload_document/s3-service");
const getProdService = require("./KUNN/select_prod_scheme");
const createRequest = require("./KUNN/createRequest");
const uploadDoc = require("./KUNN/upload");
const select_offer = require("./KUNN/select_offer");
const get_offer_list = require("./KUNN/getOfferList");
const kunnResubmit = require("./KUNN/kunnResubmit");
const checkCreditRule = require("./KUNN/check_DE");
const genOffer = require("./KUNN/generate_offer");
const update_status = require("./KUNN/update_status");
const {
  getProdSchemeValidate,
  createRequestValidate,
  selectOfferValidate,
  updateStatus,
  uploadValidation,
  resubmitValidation,
  createRequestVskValidate,
} = require("./KUNN/helpers/validation-middleware");
app.get(
  "/los-mc-credit/v1/getProdScheme",
  authenService.authenticate,
  getProdSchemeValidate,
  (req, res) => {
    getProdService.getProdScheme(req, res);
  }
);
app.post(
  "/los-mc-credit/v1/mc-gateway/createRequest",
  authenService.authenticate,
  createRequestValidate,
  (req, res) => {
    createRequest.createRequest(req, res);
  }
);
const { VSKCreateKunnValidate } = require("./utils/validator/vsk-validator");
app.post(
  "/los-mc-credit/v1/mc-gateway/vsk/createRequest",
  VSKCreateKunnValidate,
  (req, res) => {
    createRequest.createRequest(req, res);
  }
);
app.post(
  "/los-mc-credit/v1/uploadKunn",
  multer({
    limits: { fileSize: 5000000 },
    files: 1,
    parts: 10,
  }).any(),
  authenService.authenticate,
  uploadValidation,
  uploadDoc.uploadDoc
);
app.post(
  "/los-mc-credit/v1/mc-gateway/getOfferList",
  get_offer_list.getOfferList
);
app.post(
  "/los-mc-credit/v1/mc-gateway/selectOffer",
  authenService.authenticate,
  selectOfferValidate,
  select_offer.selectOffer
);
app.post(
  "/los-mc-credit/v1/vsk/selectOffer",
  selectOfferValidate,
  select_offer.selectOffer
);
app.post(
  "/los-mc-credit/v1/kunnResubmit",
  authenService.authenticate,
  kunnResubmit.resubmit
);
app.post("/los-mc-credit/v1/vsk/kunnResubmit", kunnResubmit.resubmit);
app.get("/los-mc-credit/v1/kunnResubmit", kunnResubmit.getResubmit);
app.post(
  "/los-mc-credit/v1/sme/kunnResubmit",
  authenService.authenticate_oauth2,
  kunnResubmit.resubmit
);
app.get(
  "/los-mc-credit/v1/sme/kunnResubmit/get",
  authenService.authenticate_oauth2,
  kunnResubmit.getResubmit
);
app.post(
  "/los-mc-credit/v1/updateKunnStatus",
  updateStatus,
  update_status.updateStatus
);
app.post(
  "/los-mc-credit/v1/mc-gateway/checkCreditRule",
  checkCreditRule.callDE
);

app.post(
  "/los-mc-credit/v1/mc-gateway/generateOffer",
  genOffer.generateOfferList
);

app.post(
  "/los-mc-credit/v1/uploadDoc",
  multer({
    limits: { fileSize: 10000000 },
    files: 1,
    parts: 10,
  }).any(),
  uploadDoc.uploadDoc
);

app.post(
    "/los-mc-credit/v1/uploadDocV3",
    multer({
      limits: { fileSize: 10000000 },
      files: 1,
      parts: 10,
    }).any(),
    uploadDoc.uploadDocV3
);

app.put(
  "/los-mc-credit/v1/external-doc/upload-doc",
  multer({
    limits: { fileSize: 10000000 },
    files: 1,
    parts: 10,
  }).any(),
  s3Service.uploadV3
);

app.post(
  "/los-mc-credit/v1/document/upload-doc-v2",
  multer({
    limits: { fileSize: 10000000 },
    files: 1,
    parts: 10,
  }).any(),
  uploadDoc.uploadDocV2
);

const { getTaskInfo, getWorkflowStatus } = require("./UI_API/get_task_info");
const {
  getBudgetAnalysis,
  updateBudgetAnalysis,
} = require("./UI_API/update_budget_analysis");
const { getKunnInfo } = require("./UI_API/get_kunn_info");
const { getOffers } = require("./UI_API/getOfferList");
const { getKunnOtherInfo } = require("./UI_API/get_kunn_other_info");
const { getDocument, getDocPreSigned } = require("./UI_API/get_document");
const { getKunnDetail } = require("./UI_API/get_kunn_detail");
const { getBankNameByCode } = require("./utils/masterdataService");

const sms = require("./utils/smsService");
const updateContractStatus = require("./contract/update_contract_status");

app.get("/los-mc-credit/v1/getTaskInfo", getTaskInfo);

app.get("/los-mc-credit/v1/workflow", getWorkflowStatus);

app.get("/los-mc-credit/v1/getBudgetAnalysis", getBudgetAnalysis);

app.post("/los-mc-credit/v1/updateBudgetAnalysis", updateBudgetAnalysis);

app.get("/los-mc-credit/v1/getKUNNInfo", getKunnInfo);

app.get("/los-mc-credit/v1/getDoc", getDocument);

app.get("/los-mc-credit/v1/getKunnDetail", getKunnDetail);

app.get("/los-mc-credit/v1/getBankName", getBankNameByCode);

app.get("/los-mc-credit/v1/getOffers", getOffers);

app.get("/los-mc-credit/v1/getTaskOtherInfo", getKunnOtherInfo);

app.get("/los-mc-credit/v1/getDocument", getDocument);

app.post(
  "/los-mc-credit/v1/kunn/activate",
  updateContractStatus.activeContract
);

//callback update khi co thay doi du no (hoan han muc)
app.post(
  "/los-mc-credit/v1/kunn/update-payment",
  updateContractStatus.updatePayment
);

//callback update khi co thay doi du no (hoan han muc)
app.post(
  "/los-mc-credit/v1/kunn/received-payment",
  updateContractStatus.updateReceivedPayment
);

app.post(
  "/los-mc-credit/v1/kunn/deactivate",
  updateContractStatus.deactiveContract
);

app.post("/los-mc-credit/v1/kunn/cancel", updateContractStatus.cancelContract);

const getLoanInfoService = require("./KUNN/get_loan_info");
app.post("/los-mc-credit/v1/loan/loanInfo", getLoanInfoService.getLoanInfo);

const searchService = require("./UI_API/searchCase");
const ceService = require("./manual_flow/ce-service");

app.get("/los-mc-credit/v1/case/search", searchService.searchCaseEasy);
app.get("/los-mc-credit/v1/case/searchKunn", searchService.searchKunn);
app.post("/los-mc-credit/v1/case/paymentInfo", searchService.paymentInfo);
app.get("/los-mc-credit/v1/case/installment", searchService.installment);
app.get("/los-mc-credit/v1/case/listContract", searchService.listContract);
app.get("/los-mc-credit/v1/case/repayment", searchService.repayment);
app.get("/los-mc-credit/v1/case/app/search", searchService.searchCaseEasyApp);
app.get("/los-mc-credit/v1/case/applicationForm", searchService.getApplicationForm);
app.post("/los-mc-credit/v1/document/presignedS3", s3Service.genPresignedUrl);
// app.post(
//   "/los-mc-credit/v1/finv/document/presignedS3",
//   authenService.authenticateOauth2V04WithToken,
//   decryptRequestFinv,
//   encryptResponseFinv,
//   s3Service.genPresignedUrl
// );
app.post(
  "/los-mc-credit/v1/document/presignedS3/afterDisbursement",
  s3Service.genPresignedUrlV2
);
app.post("/los-mc-credit/v1/ce/saveUploadDoc", ceService.saveCeUploadDoc);
app.post("/los-mc-credit/v1/ce/saveUploadDocV2", ceService.saveCeUploadDocV2);
app.post("/los-mc-credit/v1/ce/replaceUploadDoc", ceService.replaceCeUploadDoc);

app.get("/los-mc-credit/v1/ce/getDocUploadHistory", ceService.getDocUploadHistory);

app.get("/los-mc-credit/v1/ce/getUploadedDoc", ceService.getCeUploadDoc);
app.get("/los-mc-credit/v1/sme/getUploadedDocSme", ceService.getUploadDocSme);
app.post("/los-mc-credit/v1/ce/updateUploadDoc", ceService.updateUploadDoc);
app.post("/los-mc-credit/v1/ce/deleteUploadDoc", ceService.deleteUploadDoc);

const ssService = require("./manual_flow/ss-service");
app.post(
  "/los-mc-credit/v1/ss/uploadResubmit",
  multer().any(),
  ssService.uploadResubmit
);
app.post("/los-mc-credit/v1/ss/saveCheckedDoc", ssService.saveSScheckDoc);
app.post("/los-mc-credit/v1/ss/saveComment", ssService.saveSSComment);
app.get("/los-mc-credit/v1/ss/getComment", ssService.getSSComment);
app.post("/los-mc-credit/v1/ss/updateSSComment", ssService.updateSSComment);

const contractInfo = require("./UI_API/getContractInfo-service");
app.get("/los-mc-credit/v1/contract/caseStatus", contractInfo.getCurCaseCode);
app.get(
  "/los-mc-credit/v1/contract/getContractByTaxIdAndRegistId",
  contractInfo.getContractByTaxIdAndRegistId
);

const updateAfService = require("./UI_API/updateAF-service");
app.put("/los-mc-credit/v1/af/updateAF", updateAfService.updateAF);

const financialIndicators = require("./UI_API/get_financial_indicators");
app.get(
  "/los-mc-credit/v1/financial-indicators/getFI",
  financialIndicators.getFI
);
app.get(
  "/los-mc-credit/v1/financial-indicators/selectFI",
  financialIndicators.selectFI
);
app.post(
  "/los-mc-credit/v1/financial-indicators/saveFI",
  financialIndicators.saveFI
);
app.get(
  "/los-mc-credit/v1/financial-indicators/getFIByContractNumber",
  financialIndicators.getFIByContractNumber
);

const offerMIS = require("./offer/MIS-offer");
app.post("/los-mc-credit/v1/offer/mis-offer/save", offerMIS.saveMISOffer);
app.post(
  "/los-mc-credit/v1/sme/receive",
  authenService.authenticate_oauth2,
  multer({
    limits: { fileSize: 5000000 },
    files: 1,
    parts: 10,
  }).any(),
  receiveContractSme
);

app.post(
  "/los-mc-credit/v1/searchCase/contractList",
  searchService.searchCaseByContractList
);
app.post(
  "/los-mc-credit/v1/searchCase/phoneList",
  searchService.searchCaseByPhoneList
);

app.post("/los-mc-credit/v1/ss/saveUploadDoc", ssService.saveSsUploadDoc);
app.get("/los-mc-credit/v1/ss/getUploadedDoc", ssService.getSsUploadDoc);

const ccToolService = require("./UI_API/cc-tool");
const { LMS_DATE } = require("./utils/dateHelper.js");
app.get("/los-mc-credit/v1/cc-tool/getInfo", ccToolService.getInfo);

app.get("/los-mc-credit/v1/doc/pre-signed", getDocPreSigned);

app.get("/los-mc-credit/v2/health", (req, res) => {
  res.status(200).json({
    status: 1,
    msg: `${LMS_DATE()} | Service hoat dong binh thuong`,
  });
});

app.post("/los-mc-credit/v1/workflow/routing", (req, res) => {
  const {body} = req;
  routing(body);
  res.status(200).json({
    status: 0,
    msg: "Routing request processed successfully",
  });
});
