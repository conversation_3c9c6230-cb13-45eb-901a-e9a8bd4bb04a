const {
    REQUEST_TYPE,
    PARTNER_CODE,
    BUDGET_ANALYSIS,
    DISURSEMENT_METHOD,
    CONTRACT_TYPE,
    BUNDLE_STAGE,
    PRODUCT_CODE,
    CHANNEL,
    ERROR_CODE,
    RESP_MESSAGE,
    PARTNER_TYPE,
    CIC_STEP_CHECK,
    BUSINESS_TYPE,
    MisaStep,
    TYPE_COLLECTION,
    DOC_TYPE,
    TIME_CONVERT,
    LOCK_STATUS,
    SERVICE_NAME,
    BZHMStep,
    WORKFLOW_CODE
} = require("../const/definition")
const { CALLBACK_STAUS, WORKFLOW_STAGE } = require("../const/caseStatus")
const loanContractRepo = require("../repositories/loan-contract-repo")
const { routing } = require("../services/workflow-service")
const common = require("../utils/common")
const productService = require("../utils/productService")
const documentRepo = require("../repositories/document")
const { convertBody, convertBodyVer2 } = require("../utils/converter/convert")
const { STATUS } = require("../const/caseStatus")
const utils = require("../utils/helper")
const moment = require('moment-timezone')
moment().tz('Asia/Ho_Chi_Minh').format()
const loggingService = require("../utils/loggingService")
const turnoverRepo = require("../repositories/turnover-repo")
const { RESPONSE_CODE, TASK_FLOW } = require("../const/definition")
const callbackService = require("../utils/callbackService")
const callbackServiceV2 = require("../services/callback-service")
const { ResponseBaseKov } = require("../entity/response-base-kov")
const { ResponseBaseFinv } = require("../entity/response-base-finv")
const { RESPONSE_MSG } = require("../const/response-const")
const { validNotEnoughDocumentKovCash } = require("../utils/validator/base-api-validator")
const kunnPrepareAttributeRepo = require('../repositories/kunn-prepare-attribute-repo')
const lmsService = require("../services/lms-service")
const loanAttributeRepo = require("../repositories/loan-atribute-repo")
const { ServerErrorResponse, Response, BadRequestResponse } = require("../base/response")
const Joi = require("joi")
const sqlHelper = require("../utils/sqlHelper")
const loanBusinessOwnerRepo = require('../repositories/loan-business-owner-repo')
const antiFraudService = require("../services/anti-fraud")
const uuid = require("uuid")
const crmService = require("../services/crm-service")
const loanCustomerRepo = require('../repositories/loan-customer-repo')
const smeMisaService = require("../services/sme-misa-v2");
const masterdataService = require("../utils/masterdataService")
const { LMS_DATE } = require("../utils/dateHelper")
const loggingRepo = require("../repositories/logging-repo")
const {getValueCode_v3, parseValueCode} = require("../utils/masterdataService");
const {ADDRESS_CODE_TYPE} = require("../const/variables-const");
const { HTTP_STATUS, DOC_GROUP, LOAN_CUSTOMER_SUBJECT, FINV_DOCTYPES, FINV_KUNN_DOCTYPES } = require("../const/variables-const")
const { getProductByCodeApi } = require("../apis/product-api")
const loanContractDocumentRepo = require("../repositories/document");
const esingingRepo = require("../repositories/loan-esigning")
const { STEP } = require("../const/step-const")
const loanContractService = require("../services/loan-contract-service")
const { goNextStep } = require("../services/workflow-continue")
const _ = require("lodash")


class baseA2 {
    constructor(req, res) {
        this.body = req.body
        this.req = req
        this.res = res
        this.poolWrite = req.poolWrite
        this.contractNumber = req.body.contractNumber
    }

    async saveTurnoverTransaction() {
        Promise.all([
            turnoverRepo.saveTurnOrTrans(global.poolWrite, this.convertedBody.turnover, "turnover", this.contractNumber),
            turnoverRepo.saveTurnOrTrans(global.poolWrite, this.convertedBody.expenses, "expenses", this.contractNumber)
        ])
    }
    async receiveA2() {
        await this.convertA2Body()
        await this.createA2()
    }
    async createA2() {
        try {
            const costProvince = BUDGET_ANALYSIS.COST_PROVINCE;
            const minCostProvince = parseInt(6000000);
            let responseBody = {
                code: RESPONSE_CODE.RECIEVED,
                message: ""
            };
            const contractData = await loanContractRepo.getLoanContract(this.contractNumber)
            if (![STATUS.ELIGIBLE, STATUS.LOAN_LIMIT_PASSED].includes(contractData.status)) {
                responseBody.code = RESPONSE_CODE.INVALID_REQUEST;
                responseBody.message = "Contract number is not eligible";
            }
            if (costProvince.includes(this.convertedBody.province_per) && this.convertedBody.m_household_expenses < minCostProvince) this.convertedBody.m_household_expenses = minCostProvince;
            const updateFullLoanRs = await Promise.all([
                loanContractRepo.updateLoanContract(this.convertedBody),
                loanContractRepo.updateContractStatus(STATUS.FULL_LOAN, this.contractNumber),
                turnoverRepo.insertOtherTurnover(this.contractNumber)
            ])
            if (!updateFullLoanRs[0] || !updateFullLoanRs[1] || !updateFullLoanRs[2]) {
                responseBody.code = RESPONSE_CODE.SERVER_ERROR;
                responseBody.message = "Internal Server Error";
                this.saveLog(responseBody);
            }

            const numAdditiondoc = this.req.config.data.additionalDoc.numDoc;
            if (this.body.channel == PARTNER_CODE.SMA) {
                documentRepo.insertOtherDoc(this.contractNumber, numAdditiondoc, { partnerCode: this.body.channel, productCode: this.body.productCode })
            } else {
                documentRepo.insertOtherDoc(this.contractNumber, numAdditiondoc)
            }
            return responseBody;
        } catch (error) {
            common.log('create a2 error:' + error.message);
        }

    }
    async convertA2Body() {
        const newBody = convertBody(this.body, REQUEST_TYPE.FULL_LOAN, global.convertCache)
        this.convertedBody = newBody
        this.convertedBody.contract_number = this.contractNumber
    }
    responseInternalServer() {
        return this.res.status(200).json({
            code: RESPONSE_CODE.SERVER_ERROR,
            message: "Internal Server Error"
        })
    }
    async saveLog(responseBody) {
        await loggingService.saveRequestV2(this.poolWrite, this.convertedBody, responseBody, this.contractNumber, this.body.requestId, this.body.partnerCode)
    }
    responseCreateRequestError() {
        return common.responseErrorPublic(this.res)
    }
}

class kovA2 extends baseA2 {
    constructor(req, res) {
        // const currentTimestamp = new Date().getTime()
        // const requestId = "KOV" + currentTimestamp
        req.body.partnerCode = PARTNER_CODE.KOV
        // req.body.requestId = requestId
        super(req, res)
    }

    async a2Receive() {
        let response;
        try {
            let documentlist = this.body.listDocCollecting;
            const externalDocs = this.body.externalDocument || [];
            let allDocument = documentlist.concat(externalDocs);
            //validate docs
            const resultValidation = await validNotEnoughDocumentKovCash(allDocument, this.body.productCode, BUNDLE_STAGE.SIGNING);
            if (!resultValidation.isEnough) {
                response = new ResponseBaseKov(400, RESPONSE_CODE.INVALID_REQUEST, resultValidation.msg, null, []);
                super.saveLog(response);
                return this.res.status(400).json(response);
            }
            let docList = [...documentlist, ...externalDocs];
            const bundleInfo = await productService.getBundle(global.config, this.body.productCode);
            docList = productService.mapBundleGroupKOV(docList, bundleInfo.data);
            const updateDoc = await documentRepo.saveUploadedDocumentKOV(this.poolWrite, this.contractNumber, docList);
            if (!updateDoc) {
                response = new ResponseBaseKov(500, RESPONSE_CODE.SERVER_ERROR, RESPONSE_MSG.INTERNAL_SERVER_ERROR, null, []);
                super.saveLog(response)
                return this.res.status(500).json(response);
            }
            await super.convertA2Body();
            const a2Result = await super.createA2();
            response = new ResponseBaseKov(200, a2Result.code, a2Result.message, null, []);
            if (a2Result.code == RESPONSE_CODE.RECIEVED) {
                if (!utils.isNullOrEmpty(this.body.branchAddress)) {
                    loanContractRepo.saveBranchAddress(this.contractNumber, this.body.branchAddress);
                }
                if (this.body.accountTrading != '') {
                    loanContractRepo.saveLoanAcountTrading(this.contractNumber, this.body.accountTrading);
                }
                callbackService.callbackRecieved(this.poolWrite, this.req.config, this.contractNumber);
                const curTaskCode = TASK_FLOW.FULL_LOAN
                this.convertedBody.currentTask = curTaskCode
                routing(this.convertedBody)
                const dataResponse = {
                    contractNumber: this.contractNumber,
                    partnerCode: PARTNER_CODE.KOV
                }
                response = new ResponseBaseKov(200, RESPONSE_CODE.RECIEVED, "The application is received", dataResponse, []);
            }
            await super.saveLog(response);
            return this.res.status(200).json(response);
        }
        catch (err) {
            console.log(err)
            // super.responseCreateRequestError()
            // response = new ResponseBaseKov(500, RESPONSE_CODE.SERVER_ERROR, RESPONSE_MSG.INTERNAL_SERVER_ERROR, null, []);
        }
    }
}

class finvA2 extends baseA2 {
    constructor(req, res) {
        req.body.partnerCode = PARTNER_CODE.FINV;
        super(req, res);
    }

    async validateAf2(requestPayload) {
        try {
            let { ownerEquity, capitalNeed } = requestPayload;

            if (ownerEquity < 0.15 * capitalNeed) {
                return {
                    isValid: false,
                    errorCode: "BAD_REQUEST",
                    errorMessage: "ownerEquity invalid",
                };
            }

            //validate productCode
            //   if (!requestPayload.productInfo) {
            //     return {
            //       isValid: false,
            //       errorCode: "BAD_REQUEST",
            //       errorMessage: "productCode invalid",
            //     };
            //   }

            //check docs

            return { isValid: true };
        } catch (e) {
            console.error(`func | validateAf2 | error: `, e);
            return {
                isValid: false,
                errorCode: `server_error`,
                errorMessage: `server_error`,
            };
        }
    }

    async convertA2BodyFinv(body) {
        this.convertedBody = convertBodyVer2(_.cloneDeep(body), REQUEST_TYPE.FINV_AF2, global.convertCache);
        this.convertedBody.business_data = JSON.stringify(body.businessData);
    }

    async convertA2Body(body) {
        // const body = this.body;
        const {
            businessData,
            partnerInfo
        } = body;

        body.totalTurnoverNextYear = Number(businessData.totalTurnOverNextYear || 0);
        body.totalCostOverNextYear = Number(businessData.totalCostOverNextYear || 0);
        body.preTaxProfitNextYear =
            Number(businessData.totalTurnOverNextYear || 0) - Number(businessData.totalCostOverNextYear || 0);
        body.turnoverAmount = Number(businessData.turnoverAmount || 0);

        // body.position = productCode.startsWith("HKD") ? "BOR" : "OTH";

        businessData.profit =
            Number(businessData.turnoverAmount || 0) -
            Number(businessData.businessCost || 0);
        businessData.projectedNextTimeProfit =
            Number(businessData.projectedNextTimeTurnover || 0) -
            Number(businessData.projectedNextTimeCost || 0);
        // body.businessData = JSON.stringify(businessData);
        body.currentTask = TASK_FLOW.START;
        body.workflowCode = WORKFLOW_CODE.FINV_AF2;
        body.contractType = CONTRACT_TYPE.CREDIT_LINE;

        await this.convertA2BodyFinv(body);
        this.body.partnerInfo = partnerInfo;
    }

    async createA2() {
        //save partner info
        const { partnerInfo } = this.body;
        if (partnerInfo?.identityCard) {
            const saveData = {
                contractNumber: this.contractNumber,
                subject: LOAN_CUSTOMER_SUBJECT.PARTNER,
                fullName: partnerInfo.fullname,
                // tax_id: e.taxId,
                id: partnerInfo.identityCard,
                dob: partnerInfo.dateOfBirth,
                idType: "CCCD",
                issueDate: partnerInfo.issueDate,
                issuePlace: partnerInfo.issuePlace,
                phoneNumber: partnerInfo.phoneNumber,
                email: partnerInfo.email,
                perProvinceCode: partnerInfo.perAddress.provinceCode,
                perDistrictCode: partnerInfo.perAddress.districtCode,
                perWardCode: partnerInfo.perAddress.wardCode,
                perDetailAddress: partnerInfo.perAddress.detailAddress,
                gender: partnerInfo.gender,
            };
            await loanContractRepo.saveLoanCustomerShareholders(this.contractNumber, [
                saveData,
            ]);
        }

        return await super.createA2();
    }
    async saveDocs(additionalDocs = []) {
        try {
            let task = [];
            const docs = [...(this.body.docs || []), ...(this.body.otherDocs || []), ...additionalDocs];

            const bundleInfo = await productService.getBundleV4(
                global.config,
                this.body.productCode
            );
            const documentRs = await masterdataService.getValueCodeByCodeType("DOCUMENT");
            const documentList = productService.mapBundleGroupV2(docs, bundleInfo);
            for (const doc of documentList) {
                let docNameVnDetail = ''
                if (Array.isArray(documentRs) && documentRs.length > 0) {
                    docNameVnDetail = documentRs.find((document) => document.code === doc.docType)?.value || '';
                }
                const data = {
                    table: "loan_contract_document",
                    columns: [
                        "contract_number",
                        "doc_type",
                        "doc_group",
                        "doc_name",
                        "doc_name_vn",
                        "doc_name_vn_detail",
                    ],
                    values: [
                        this.contractNumber,
                        doc.docType,
                        doc.docGroup,
                        doc.bundleName,
                        doc.bundleNameVi,
                        docNameVnDetail,
                    ],
                    conditions: {
                        doc_id: doc.docId,
                    },
                }
                task.push(
                    sqlHelper.updateData(data)
                );
            }
            const resultList = await Promise.all(task);
            for (const i in resultList) {
                let rs = resultList[i];
                if (rs.rowCount == 0) {
                    common.log("Update document error", contractNumber);
                    return false;
                }
            }
            return true;
        } catch (error) {
            console.log(err);
            return false;
        }
    }
    async a2Receive() {
        let response;
        const requestBody = JSON.parse(JSON.stringify(this.body || {}));
        try {
            //set productCode
            let body = _.cloneDeep(this.body);

            const validateSmeInfoResult = await this.validateAf2(body);
            if (!validateSmeInfoResult.isValid) {
                const errors = [
                    {
                        code: validateSmeInfoResult.errorCode,
                        message: validateSmeInfoResult.errorMessage,
                    },
                ];
                return this.res
                    .status(HTTP_STATUS.BAD_REQUEST)
                    .json(new BadRequestResponse(errors));
            }

            const loan = await loanContractRepo.findByContractNumber({
                contractNumber: this.contractNumber,
                partnerCode: body.partnerCode
            });
            if (loan?.status !== STATUS.PASSED_REVIEW_A1) {
                return this.res.status(400).json(new BadRequestResponse([], `trạng thái hồ sơ không để submit af2`));
            }

            await this.convertA2Body(body);

            let loanCustomerData = {
                contract_number: this.contractNumber,
                married_status: body.marriedStatus,
                created_at: new Date()
            }
            if (body?.customerInfo?.marriedStatus === 'M') {
                loanCustomerData = {
                    ...loanCustomerData,
                    partner_full_name: body?.partnerInfo?.fullname,
                    partner_phone_number: body?.partnerInfo?.phoneNumber,
                    partner_id_number: body?.partnerInfo?.identityCard,
                    partner_per_new_province_code: body?.partnerInfo?.perAddress?.provinceCode,
                    partner_per_new_ward_code: body?.partnerInfo?.perAddress?.wardCode,
                    partner_per_detail_address: body?.partnerInfo?.perAddress?.detailAddress
                }
            };

            await Promise.all([
                sqlHelper.insertData(
                    `loan_customer`,
                    loanCustomerRepo.columns,
                    sqlHelper.generateValues(loanCustomerData, loanCustomerRepo.columns)
                ),
                loanContractRepo.updateLoanContract(this.convertedBody)
            ])

            const saveDocs = await this.saveDocs(body.partnerInfo.docs);
            if (!saveDocs) {
                response = new ResponseBaseFinv(200, RESPONSE_CODE.ERROR, "Error when upload docs", {}, [])
                super.saveLog(response)
                return this.res.status(response.statusCode).json(response.body);;
            }

            goNextStep(this.contractNumber);
            const dataResponse = {
                contractNumber: this.contractNumber,
                partnerCode: PARTNER_CODE.FINV,
            };
            response = new ResponseBaseFinv(
                HTTP_STATUS.SUCCESS,
                RESPONSE_CODE.RECIEVED,
                "The application is received",
                dataResponse,
                []
            );

            await super.saveLog(response);
            await loggingRepo.saveStepLog(
                this.contractNumber,
                SERVICE_NAME.MC_LOS,
                WORKFLOW_STAGE.SEND_FULLLOAN,
                requestBody,
                response,
                this.req.originalUrl
            );
            return this.res
                .status(response?.statusCode || HTTP_STATUS.SUCCESS)
                .json(response.body);
        } catch (err) {
            console.log(
                `[FINV][A2] a2Receive contract ${this.contractNumber}, error: ${err} `
            );
            response = new ResponseBaseFinv(
                HTTP_STATUS.INTERNAL_SERVER,
                ERROR_CODE.INT_SERVER_ERROR,
                RESPONSE_MSG.INTERNAL_SERVER_ERROR,
                null,
                []
            );
            return this.res.status(response.statusCode).json(response.body);
        }
    }

    async a3Receive() {
        let response = new ResponseBaseFinv(
            HTTP_STATUS.SUCCESS,
            0,
            "success",
            null,
            []
        );
        const requestBody = JSON.parse(JSON.stringify(this.body || {}));
        try {
            //set productCode
            await this.saveDocs();
            const loan = await loanContractRepo.getLoanContract(this.contractNumber);
            await loanContractRepo.updateContractStatus(
                STATUS.RECEIVEDA3,
                this.contractNumber
            );
            await loanContractRepo.updateLoanContract({
                contract_number: loan.contractNumber,
                status: STATUS.RECEIVEDA3,
                updated_date: new Date(),
                agree_insurance: this.body.agreeInsurance ? true : false,
            });
            goNextStep(this.contractNumber);
            const dataResponse = {
                contractNumber: this.contractNumber,
                partnerCode: PARTNER_CODE.FINV,
            };
            response = new ResponseBaseFinv(
                HTTP_STATUS.SUCCESS,
                RESPONSE_CODE.RECIEVED,
                "The application is received",
                dataResponse,
                []
            );
            await super.saveLog(response);

            return this.res
                .status(response?.statusCode || HTTP_STATUS.SUCCESS)
                .json(response.body);
        } catch (err) {
            console.log(
                `[FINV][A2] a3Receive contract ${this.contractNumber}, error: ${err} `
            );
            response = new ResponseBaseFinv(
                HTTP_STATUS.INTERNAL_SERVER,
                RESPONSE_CODE.SERVER_ERROR,
                RESPONSE_MSG.INTERNAL_SERVER_ERROR,
                null,
                []
            );
            return this.res.status(response.statusCode).json(response.body);
        } finally {
            await loggingRepo.saveStepLog(
                this.contractNumber,
                SERVICE_NAME.MC_LOS,
                WORKFLOW_STAGE.SUBMIT_AF3,
                requestBody,
                response,
                this.req.originalUrl
            );
        }
    }
    async processFlowA2() {
        try {
            const loan = await loanContractRepo.getLoanContract(this.contractNumber);
            await loanContractRepo.updateContractStatus(
                STATUS.PASSED_REVIEW_A2,
                this.contractNumber
            );
            routing(loan);
        } catch (error) {
            console.log(
                `[FINV][A2] processFlowA2 contract ${this.contractNumber}, error: ${err} `
            );
        } finally {
            await loggingRepo.saveStepLog(
                this.contractNumber,
                SERVICE_NAME.MC_LOS,
                WORKFLOW_STAGE.PROCESS_FULLLOAN,
                this.body,
                {}
            );
        }
    }
    async processFlowA3() {
        try {
            const loan = await loanContractRepo.getLoanContract(this.contractNumber);
            await loanContractRepo.updateContractStatus(
                STATUS.PASSED_REVIEW_A3,
                this.contractNumber
            );
            routing(loan);
        } catch (error) {
            console.log(
                `[FINV][A2] processFlowA2 contract ${this.contractNumber}, error: ${err} `
            );
        } finally {
            await loggingRepo.saveStepLog(
                this.contractNumber,
                SERVICE_NAME.MC_LOS,
                WORKFLOW_STAGE.PROCESS_FULLLOAN,
                this.body,
                {}
            );
        }
    }
}

class MisaA2 extends baseA2 {
    constructor(req, res) {
        const requestId = utils.genRequestId(PARTNER_CODE.MISA);
        req.body.partnerCode = PARTNER_CODE.MISA
        req.body.requestId = requestId
        req.body.disbursementMethod = DISURSEMENT_METHOD.TRANSFER
        super(req, res)
    }
    async a2Receive() {
        try {
            const body = this.body;
            // let documentlist = body.listDocCollecting;
            // const externalDocs = body.externalDocument || [];
            // let docList = [...documentlist, ...externalDocs];
            let docList = body.listDocCollecting;
            const contractData = await loanContractRepo.getLoanContract(this.contractNumber);
            let contractType = '';
            let bundleInfo;
            if (contractData.contract_type == CONTRACT_TYPE.CREDIT_LINE) {
                bundleInfo = await productService.getBundle(global.config, 'SME_MISA_HM_STANDARD');
                contractType = 'HM';
            }
            else {
                bundleInfo = await productService.getBundle(global.config, 'SME_MISA_VM_STANDARD');
                contractType = 'VM';
            }
            const bundleData = bundleInfo.data;
            let bundleResult = [];
            let skipBundle = ['SME_MISA_' + contractType + '_SCAN POWER OF ATTORNEY OF THE LEGAL REPRESENTATIVE',
            'SME_MISA_' + contractType + '_POWER OF ATTORNEY OF THE LEGAL REPRESENTATIVE'];
            for await (const b of bundleData) {
                if (!skipBundle.includes(b.bundleName)) {
                    bundleResult.push(b);
                }
            }
            docList = productService.mapBundleGroup(docList, bundleResult);
            const updateDoc = await documentRepo.saveUploadedDocumentKOV(this.poolWrite, this.contractNumber, docList);
            if (!updateDoc) {
                let res = {
                    code: RESPONSE_CODE.INVALID_REQUEST,
                    msg: "Error when upload docs"
                }
                super.saveLog(res)
                return this.res.status(200).json(res);
            }
            await super.convertA2Body();
            const a2Result = await super.createA2();
            if (a2Result.code == RESPONSE_CODE.RECIEVED) {
                if (!utils.isNullOrEmpty(body.branchAddress)) {
                    loanContractRepo.saveBranchAddress(this.contractNumber, body.branchAddress);
                }
                if (!utils.isNullOrEmpty(body.profit)) {
                    turnoverRepo.saveTurnOrTrans(this.poolWrite, body.profit, "profit", this.contractNumber)
                }
                const curTaskCode = TASK_FLOW.FULL_LOAN
                this.convertedBody.currentTask = curTaskCode
                routing(this.convertedBody)
                a2Result.code = RESPONSE_CODE.RECIEVED;
                a2Result.message = "The application is received";
                a2Result.conractNumber = this.contractNumber;
                a2Result.loanAmount = contractData.approval_amt;
            }
            await super.saveLog(a2Result);
            callbackServiceV2.callbackPartner(this.contractNumber, PARTNER_CODE.MISA, CALLBACK_STAUS.FULL_LOAN)
            return this.res.status(200).json(a2Result);
        }
        catch (err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }
}

class McAppA2 extends baseA2 {
    constructor(req, res) {
        const requestId = utils.genRequestId(PARTNER_CODE.MCAPP);
        req.body.productCode = PRODUCT_CODE.MCTAX_HMTD;
        req.body.partnerCode = PARTNER_CODE.MCAPP;
        req.body.requestId = requestId;
        req.body.disbursementMethod = DISURSEMENT_METHOD.TRANSFER;
        req.body.loanPurpose = 'EBP';
        super(req, res);
    }
    async a2Receive() {
        try {
            const body = this.body;
            let docList = body.listDocCollecting;
            const contractData = await loanContractRepo.getLoanContract(this.contractNumber);
            let bundleInfo = await productService.getBundle(global.config, PRODUCT_CODE.MCTAX_HMTD);
            if (contractData.contract_type == CONTRACT_TYPE.CASH_LOAN) bundleInfo = await productService.getBundle(global.config, PRODUCT_CODE.MCTAX_HMTD);
            const bundleData = bundleInfo.data;
            docList = productService.mapBundleGroup(docList, bundleData);
            // console.log({docList})
            const updateDoc = await documentRepo.saveUploadedDocumentKOV(this.poolWrite, this.contractNumber, docList);
            if (!updateDoc) {
                let res = {
                    code: RESPONSE_CODE.INVALID_REQUEST,
                    msg: "Error when upload docs"
                }
                super.saveLog(res)
                return this.res.status(200).json(res);
            }
            await super.convertA2Body();
            const a2Result = await super.createA2();
            if (a2Result.code == RESPONSE_CODE.RECIEVED) {
                if (!utils.isNullOrEmpty(body.branchAddress)) {
                    loanContractRepo.saveBranchAddress(this.contractNumber, body.branchAddress);
                }
                const productData = await productService.getProductInfoV2(PRODUCT_CODE.MCTAX_HMTD);
                if (productData) {
                    loanContractRepo.updateFieldLoanContract(this.contractNumber, 'request_int_rate', productData?.productVar[0]?.intRate / 100)
                }
                const curTaskCode = TASK_FLOW.FULL_LOAN
                this.convertedBody.currentTask = curTaskCode
                routing(this.convertedBody)
                a2Result.code = RESPONSE_CODE.RECIEVED;
                a2Result.message = "The application is received";
                a2Result.conractNumber = this.contractNumber;
            }
            await super.saveLog(a2Result);
            return this.res.status(200).json(a2Result);
        }
        catch (err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }
}

// class SuperAppA2 extends baseA2 {
//     constructor(req, res) {
//         const requestId = utils.genRequestId(PARTNER_CODE.SMA);
//         //   req.body.productCode = PRODUCT_CODE.MCTAX_HMTD;
//         //   req.body.partnerCode = PARTNER_CODE.SMA;
//         req.body.requestId = requestId;
//         req.body.disbursementMethod = DISURSEMENT_METHOD.TRANSFER;
//         req.body.loanPurpose = 'EBP';
//         super(req, res);
//     }
//     async a2Receive() {
//         try {
//             const body = this.body;
//             let docList = body.listDocCollecting;
//             let kunnDocList = body.listKunnDocCollecting;
//             const kunnCode = body.kunnCode;
//             const productCode = body.productCode;
//             let bundleInfo = await productService.getBundle(global.config, productCode);
//             let bundleKunnInfo = await productService.getBundle(global.config, kunnCode);
//             const bundleData = bundleInfo.data;
//             const bundleKunnData = bundleKunnInfo.data;
//             docList = productService.mapBundleGroup(docList, bundleData);
//             kunnDocList = productService.mapBundleGroup(kunnDocList, bundleKunnData);
//             //   console.log({docList})
//             const updateDoc = await Promise.all([
//                 documentRepo.saveUploadedDocumentKOV(this.poolWrite, this.contractNumber, docList),
//                 documentRepo.saveUploadedDocumentSuperApp(this.poolWrite, this.contractNumber, kunnDocList),
//             ]);
//             if (!updateDoc[0] || !updateDoc[1]) {
//                 let res = {
//                     code: RESPONSE_CODE.INVALID_REQUEST,
//                     msg: "Error when upload docs"
//                 }
//                 super.saveLog(res)
//                 return this.res.status(200).json(res);
//             }
//             await super.convertA2Body();
//             const a2Result = await super.createA2();
//             if (a2Result.code == RESPONSE_CODE.RECIEVED) {
//                 //   if (!utils.isNullOrEmpty(body.branchAddress)) {
//                 //       loanContractRepo.saveBranchAddress(this.contractNumber, body.branchAddress);
//                 //   }
//                 //   const productData = await productService.getProductInfoV2(productCode);
//                 //   if (productData) {
//                 //       loanContractRepo.updateFieldLoanContract(this.contractNumber, 'request_int_rate', productData?.productVar[0]?.intRate / 100)
//                 //   }
//                 Promise.all([
//                     // kunnPrepareAttributeRepo.save({ contractNumber: this.contractNumber, field: "requestKunnAmount", value: body.withdrawAmount }),
//                     kunnPrepareAttributeRepo.save({ contractNumber: this.contractNumber, field: "billDay", value: body.billDay }),
//                     // kunnPrepareAttributeRepo.save({ contractNumber: this.contractNumber, field: "requestIrAmount", value: body.ir }),
//                     // kunnPrepareAttributeRepo.save({ contractNumber: this.contractNumber, field: "requestTenorAmount", value: body.tenor }),
//                     kunnPrepareAttributeRepo.save({ contractNumber: this.contractNumber, field: "kunnCode", value: body.kunnCode }),
//                     kunnPrepareAttributeRepo.save({ contractNumber: this.contractNumber, field: "paymentMethod", value: body.paymentMethod })
//                 ])
//                 const curTaskCode = TASK_FLOW.FULL_LOAN
//                 this.convertedBody.currentTask = curTaskCode
//                 this.convertedBody.partnerCode = PARTNER_CODE.SMA
//                 // const contractData = await loanContractRepo.getLoanContract(this.contractNumber)
//                 Promise.all([
//                     // lmsService.createLMS(this.contractNumber, contractData.contract_type, contractData.phone_number1, PARTNER_CODE.SMA, CHANNEL.SMA),
//                     routing(this.convertedBody)
//                 ])

//                 a2Result.code = RESPONSE_CODE.RECIEVED;
//                 a2Result.message = RESPONSE_MSG.APPLICATION_RECEIVED;
//                 a2Result.conractNumber = this.contractNumber;
//             }

//             super.saveLog(a2Result);
//             return a2Result;
//             //   return this.res.status(200).json(a2Result);
//         }
//         catch (err) {
//             console.log(err)
//             super.responseCreateRequestError()
//         }
//     }
// }

class SuperAppA2 extends baseA2 {
    constructor(req, res) {
        const requestId = utils.genRequestId(PARTNER_CODE.SMA);
        // req.body.productCode = PRODUCT_CODE.MCTAX_HMTD;
        req.body.partnerCode = PARTNER_CODE.SMA;
        req.body.requestId = requestId;
        req.body.disbursementMethod = DISURSEMENT_METHOD.TRANSFER;
        req.body.loanPurpose = 'EBP';
        req.body.tenor = '36';
        super(req, res);
    }
    async #saveOtherInfo() {
        const body = this.body
        const contractNumber = this.contractNumber
        const request = this.req
        super.saveTurnoverTransaction()
        loanAttributeRepo.save({ contractNumber, field: 'assetsPrice', value: "0" })
        if (!utils.isNullOrEmpty(body?.carInfo?.carBrand)) {
            const carInfo = body.carInfo
            Promise.all([
                loanAttributeRepo.save({ contractNumber, field: 'carBrand', value: carInfo.carBrand }),
                loanAttributeRepo.save({ contractNumber, field: 'carModel', value: carInfo.carModel }),
                loanAttributeRepo.save({ contractNumber, field: 'carKmTraveled', value: carInfo.carKmTraveled }),
                loanAttributeRepo.save({ contractNumber, field: 'carFuel', value: carInfo.carFuel }),
                loanAttributeRepo.save({ contractNumber, field: 'carManufactureOrigin', value: carInfo.carManufactureOrigin }),
                loanAttributeRepo.save({ contractNumber, field: 'carManufactureYear', value: carInfo.carManufactureYear }),
            ])
            const bodyPredict = {
                "brand": carInfo.carBrand,
                "phien_ban": carInfo.carModel,
                "fuel": carInfo.carFuel,
                "origin": carInfo.carManufactureOrigin,
                "year_manufacture": carInfo.carManufactureYear,
                "odometer": carInfo.carKmTraveled
            }
            const url = request.config.basic.pucpCore[request.config.env] + "/pucp-core/v1/car-model/predict"
            const rsPredict = await common.postApiV2(url, bodyPredict)
            loanAttributeRepo.update({ contractNumber, field: 'assetsPrice', value: !utils.isNullOrEmpty(rsPredict?.data?.car_price) ? (parseFloat(rsPredict?.data?.car_price) * 1000000).toString() : "0" })
        }
        // if(!utils.isNullOrEmpty(body.propertyInfo)){
        //     const propertyInfo = body.propertyInfo
        //     Promise.all([
        //         loanAttributeRepo.save({ contractNumber, field: 'propertyArea', value: propertyInfo.area }),
        //         loanAttributeRepo.save({ contractNumber, field: 'propertyFloorNumber', value: propertyInfo.floorNumber }),
        //         loanAttributeRepo.save({ contractNumber, field: 'propertyProvince', value: propertyInfo.province }),
        //         loanAttributeRepo.save({ contractNumber, field: 'propertyDistrict', value: propertyInfo.district }),
        //         loanAttributeRepo.save({ contractNumber, field: 'propertyWard', value: propertyInfo.ward }),
        //         loanAttributeRepo.save({ contractNumber, field: 'propertyDetail', value: propertyInfo.detail }),
        //     ])
        // }
        if (!utils.isNullOrEmpty(body.branchAddress)) {
            loanContractRepo.saveBranchAddress(this.contractNumber, body.branchAddress);
        }
        if (!utils.isNullOrEmpty(body.assetsType)) {
            loanAttributeRepo.save({ contractNumber, field: 'assetsType', value: body.assetsType })
        }
        // if (!utils.isNullOrEmpty(body.propertyType)) {
        //     loanAttributeRepo.save({ contractNumber, field: 'propertyType', value: body.propertyType })
        // }
    }
    async a2Receive() {
        try {
            const body = this.body;
            const contractNumber = this.contractNumber;
            let docList = body.listDocCollecting;
            let bundleInfo = await productService.getBundle(global.config, body.productCode);
            const bundleData = bundleInfo.data;
            docList = productService.mapBundleGroup(docList, bundleData);
            // console.log({docList})
            const updateDoc = await documentRepo.saveUploadedDocumentKOV(this.poolWrite, contractNumber, docList);
            if (!updateDoc) {
                let res = {
                    code: RESPONSE_CODE.INVALID_REQUEST,
                    msg: "Error when upload docs"
                }
                super.saveLog(res)
                return this.res.status(200).json(res);
            }
            await super.convertA2Body();
            const a2Result = await super.createA2();
            if (a2Result.code == RESPONSE_CODE.RECIEVED) {
                await this.#saveOtherInfo()
                const productData = await productService.getProductInfoV2(body.productCode);
                if (productData) {
                    loanContractRepo.updateFieldLoanContract(this.contractNumber, 'request_int_rate', productData?.productVar[0]?.intRate / 100)
                }
                const curTaskCode = TASK_FLOW.FULL_LOAN
                this.convertedBody.currentTask = curTaskCode
                routing(this.convertedBody)
                a2Result.code = RESPONSE_CODE.RECIEVED;
                a2Result.message = "The application is received";
                a2Result.contractNumber = this.contractNumber;
            }
            await super.saveLog(a2Result);
            callbackServiceV2.callbackPartner(this.contractNumber, PARTNER_CODE.SMA, CALLBACK_STAUS.FULL_LOAN)
            return this.res.status(200).json(a2Result);
        }
        catch (err) {
            console.log(err)
            super.responseCreateRequestError()
        }
    }
}

class MisaAf2 extends baseA2 {
    constructor(req, res) {
        req.body.partnerCode = PARTNER_CODE.MISA
        req.body.disbursementMethod = DISURSEMENT_METHOD.TRANSFER
        req.body.contract_type = CONTRACT_TYPE.CREDIT_LINE
        req.body.lmsType = CONTRACT_TYPE.CREDIT_LINE
        super(req, res)
    }

    async convertA2Body() {
        const newBody = convertBody(this.body, REQUEST_TYPE.MISA_AF2, global.convertCache)
        this.convertedBody = newBody
    }

    async validateAf2(requestPayload) {
        try {
            const schema = Joi.object({
                requestId: Joi.string().required(),
                // loanPurpose: Joi.string().required(),
                totalTurnoverNextYear: Joi.string().required(),//doanh thu dự kiến 12 tháng tiếp theo
                totalCostOverNextYear: Joi.string().required(),//Tổng chí phí dự kiến 12 tháng tiếp theo, //chưa có
                profitBeforeTaxNextYear: Joi.string().required(),
                capitalNeed: Joi.string().required(),//Tổng nhu cầu vốn lưu động dự kiến (A)
                loanAmount: Joi.string().required(),
                loansOtherFinancialInstitutions: Joi.string().required(),//Vốn vay các TCTD khác (C) //chưa có
                ownerEquity: Joi.string().allow(null).allow(''), //Vốn chủ sở hữu tham gia vào phương án (D), // chưa có
                otherCapital: Joi.string().required(),//Vốn huy động khác (E)
                tenor: Joi.string().required(),
                tenorPerKunn: Joi.string().required(), //Thời hạn vay mỗi KUNN // chưa có
                representations: Joi.array().items(
                    Joi.object({
                        fullName: Joi.string().required(),
                        position: Joi.string().required(),
                        dob: Joi.string().required(),
                        id: Joi.string().required(),
                        issueDate: Joi.string().required(),
                        issuePlace: Joi.string().required(),
                        phoneNumber: Joi.string().required(),
                        email: Joi.string().required(),
                        perAddress: Joi.object({
                            provinceCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            districtCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            wardCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            detail: Joi.string().required(),
                            newProvinceCode: Joi.string().allow(null).allow(''),
                            newWardCode: Joi.string().allow(null).allow('')
                        })
                    }).unknown(true).required()
                ).required(),
                managers: Joi.array().items(
                    Joi.object({
                        fullName: Joi.string().required(),
                        position: Joi.string().required(),
                        id: Joi.string().required(),
                        issueDate: Joi.string().required(),
                        issuePlace: Joi.string().required(),
                        phoneNumber: Joi.string().required(),
                        email: Joi.string().required(),
                        perAddress: Joi.object({
                            provinceCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            districtCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            wardCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            detail: Joi.string().required(),
                            newProvinceCode: Joi.string().allow(null).allow(''),
                            newWardCode: Joi.string().allow(null).allow('')
                        }),
                        curAddress: Joi.object({
                            provinceCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            districtCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            wardCode: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            detail: Joi.when('newWardCode', {
                                is: Joi.valid(null, ''),
                                then: Joi.string().required(),
                                otherwise: Joi.optional()
                            }),
                            newProvinceCode: Joi.string().allow(null).allow(''),
                            newWardCode: Joi.string().allow(null).allow('')
                        })
                    }).unknown(true).required()
                ),
                bankCode: Joi.string().required(),
                bankAccount: Joi.string().required(),
                // repaymentMethod, //Phương thức trả nợ // chưa có
                // repaymentSources, //Nguồn trả nợ
                // registrationNumber, //mã số DN
                // companyName, //tên DN
                // businessRegistrationAddress, //đkkd
                // isChange: Joi.number().isValid(0,1).required(),//có thay đổi thông tin hay không?
                // changedInfo, // thông tin thay đổi
                // businessLicenseUrl: Joi.number().required(),
                // registrationCertNo, //'Số Giấy chứng nhận ĐKKD
                // registrationCertIssueDate, //Ngày cấp chứng nhận ĐKKD
                // registrationCertIssuePlace, //Nơi cấp chứng nhận ĐKKD
                // charterCapital, //vốn điều lệ
                // businessType, //Loại hình pháp lý doanh nghiệp
                // businessIndustry, //Ngành nghề kinh doanh chính
                // conditionalBusinessIndustry, //Ngành nghề kinh doanh có điều kiện
                loanApplicationFile: Joi.object({
                    fileName: Joi.string().required(),
                    fileType: Joi.string().required(),
                    fileUrl: Joi.string().required(),
                    docType: Joi.string().required()
                }).unknown(true).required(),
                financialInformation: Joi.object({
                    inputPartners: Joi.array().items(
                        Joi.object({
                            taxId: Joi.string().required(),
                            companyName: Joi.string().required()
                        }).unknown(true).required()
                    ).required(),
                    outputPartners: Joi.array().items(
                        Joi.object({
                            taxId: Joi.string().required(),
                            companyName: Joi.string().required()
                        }).unknown(true).required()
                    ).required()
                }).unknown(true).required()
            }).unknown(true)
            const { error } = schema.validate(requestPayload)
            if (error) {
                console.log(`error: `, JSON.stringify(error))
                return { isValid: false, errorCode: error?.details?.[0]?.context?.key, errorMessage: error?.details?.[0].message }
            }

            //check bao cao tai chinh
            if (requestPayload?.financialInformation?.revenues?.length === 0) {
                return { isValid: false, errorCode: `financialInformation`, errorMessage: `Thiều file báo cáo tài chính` }
            }
            const latestRevenue = requestPayload.financialInformation.revenues.reduce((latest, current) => {
                return parseInt(current.year) > parseInt(latest.year) ? current : latest;
            }, requestPayload.financialInformation.revenues[0]);
            if (latestRevenue?.financialReportDocs?.length === 0 ||
                !latestRevenue.financialReportDocs.find(doc => doc.docType === 'TDTBCTC') ||
                !latestRevenue.financialReportDocs.find(doc => doc.docType === 'TDTBCTC')?.fileUrl) {
                return { isValid: false, errorCode: `financialInformation`, errorMessage: `Thiều file báo cáo tài chính` }
            }
            const isExistedTDTBCTC = await smeMisaService.isExistedFile(latestRevenue.financialReportDocs.find(doc => doc.docType === 'TDTBCTC').fileUrl, { req: this.req });
            if (!isExistedTDTBCTC) {
                return { isValid: false, errorCode: `financialInformation`, errorMessage: `file báo cáo tài chính không hợp lệ` }
            }
            return { isValid: true };
        } catch (e) {
            console.error(`func | validateAf2 | error: `, e);
            return { isValid: false, errorCode: `server_error`, errorMessage: `server_error` };
        }
    }

    async processAf2() {
        try {
            let misaBody = this.body;
            const validateSmeInfoResult = await this.validateAf2(misaBody);
            if (!validateSmeInfoResult.isValid) {
                const errors = [{
                    errorCode: validateSmeInfoResult.errorCode,
                    errorMessage: validateSmeInfoResult.errorMessage
                }]
                return this.res.status(400).json(new BadRequestResponse(errors));
            }
            const loan = await loanContractRepo.findOneMisaLoanContract(this.contractNumber);
            if (loan?.status !== STATUS.ELIGIBLE) {
                return this.res.status(400).json(new BadRequestResponse([], `trạng thái hồ sơ không để submit af2`));
            }
            // if (loan?.loan_customer_representations?.[0]?.id_number != misaBody.representations?.[0]?.id) {
            //     const errors = [{
            //         errorCode: `representations[0].id`,
            //         errorMessage: `không khớp với thông tin af1`
            //     }]
            //     return this.res.status(400).json(new BadRequestResponse(errors, RESP_MESSAGE.INVALID_REQUEST));
            // }
            misaBody.headquarters = {
                provinceCode: misaBody?.smeHeadquartersProvince,
                districtCode: misaBody?.smeHeadquartersDistrict,
                wardCode: misaBody?.smeHeadquartersWard,
                newProvinceCode: misaBody?.smeHeadquartersNewProvince,
                newWardCode: misaBody?.smeHeadquartersNewWard,
                detail: misaBody?.smeHeadquartersAddress,
            }
            let body = await masterdataService.convertEvfLov({ partnerCode: PARTNER_CODE.MISA, convertObject: misaBody });
            body = {
                ...body,
                smeHeadquartersProvince: body.headquarters.provinceCode,
                smeHeadquartersDistrict: body.headquarters.districtCode,
                smeHeadquartersWard: body.headquarters.wardCode,
                smeHeadquartersAddress: body.headquarters.detail,
                smeHeadquartersNewProvince: body.headquarters.newProvinceCode,
                smeHeadquartersNewWard: body.headquarters.newWardCode,
            }
            this.body = { ...body }
            await this.convertA2Body();

            const costProvince = BUDGET_ANALYSIS.COST_PROVINCE;
            const minCostProvince = parseInt(6000000);
            if (costProvince.includes(this.convertedBody.province_per)
                && this.convertedBody.m_household_expenses < minCostProvince) {
                this.convertedBody.m_household_expenses = minCostProvince;
            }
            //set default value
            this.convertedBody.status = STATUS.RECEIVEDA2
            this.convertedBody = this.setRepresentation(this.convertedBody);
            this.convertedBody = await this.setManager(this.convertedBody);
            const updateFullLoanRs = await Promise.all([
                loanContractRepo.updateLoanContract(this.convertedBody),
                loggingRepo.saveWorkflow(MisaStep.AF2, this.convertedBody.status, this.contractNumber, 'system')
            ])
            if (!updateFullLoanRs[0]) {
                return this.res.status(500).json(new ServerErrorResponse());
            }
            let businessOwnerWrap = {
                ...body.businessOwner,
                idNumber: body.businessOwner.id,
                contractNumber: this.contractNumber,
                businessProvinceCode: body.businessOwner.businessAddress?.provinceCode,
                businessDistrictCode: body.businessOwner.businessAddress?.districtCode,
                businessWardCode: body.businessOwner.businessAddress?.wardCode,
                businessDetailAddress: body.businessOwner.businessAddress?.detail,
                businessNewProvinceCode: body.businessOwner.businessAddress?.newProvinceCode,
                businessNewWardCode: body.businessOwner.businessAddress?.newWardCode,
                perProvinceCode: body.businessOwner.perAddress.provinceCode,
                perDistrictCode: body.businessOwner.perAddress.districtCode,
                perWardCode: body.businessOwner.perAddress.wardCode,
                perDetailAddress: body.businessOwner.perAddress.detail,
                perNewProvinceCode: body.businessOwner.perAddress.newProvinceCode,
                perNewWardCode: body.businessOwner.perAddress.newWardCode,
                partnerFullName: body?.businessOwner?.partner?.fullName,
                partnerIdNumber: body?.businessOwner?.partner?.id,
                partnerDob: body?.businessOwner?.partner?.dob,
                partnerPhoneNumber: body?.businessOwner?.partner?.phoneNumber,
                partnerPerProvinceCode: body?.businessOwner?.partner?.perAddress?.provinceCode,
                partnerPerDistrictCode: body?.businessOwner?.partner?.perAddress?.districtCode,
                partnerPerWardCode: body?.businessOwner?.partner?.perAddress?.wardCode,
                partnerPerDetailAddress: body?.businessOwner?.partner?.perAddress?.detail,
                partnerPerNewProvinceCode: body?.businessOwner?.partner?.perAddress?.newProvinceCode,
                partnerPerNewWardCode: body?.businessOwner?.partner?.perAddress?.newWardCode,
                partnerCurProvinceCode: body?.businessOwner?.partner?.curAddress?.provinceCode,
                partnerCurDistrictCode: body?.businessOwner?.partner?.curAddress?.districtCode,
                partnerCurWardCode: body?.businessOwner?.partner?.curAddress?.wardCode,
                partnerCurDetailAddress: body?.businessOwner?.partner?.curAddress?.detail,
                partnerCurNewProvinceCode: body?.businessOwner?.partner?.curAddress?.newProvinceCode,
                partnerCurNewWardCode: body?.businessOwner?.partner?.curAddress?.newWardCode,
                curProvinceCode: body.businessOwner.curAddress.provinceCode,
                curDistrictCode: body.businessOwner.curAddress.districtCode,
                curWardCode: body.businessOwner.curAddress.wardCode,
                curDetailAddress: body.businessOwner.curAddress.detail,
                //new address
                curNewProvinceCode: body.businessOwner.curAddress.newProvinceCode,
                curNewWardCode: body.businessOwner.curAddress.newWardCode,
                isDeleted: 0
            }
            businessOwnerWrap.marriedStatus = body?.businessOwner?.marriedStatus
                ? body?.businessOwner?.marriedStatus === 'SINGLE' ? 'C' : 'M' : null;
            let loanCustomerData = { ...body, phoneNumber: body.companyPhoneNumber, email: body.companyEmail };
            if (body.isChange == 1) {
                loanCustomerData = {
                    ...loanCustomerData,
                    companyName: body?.changedInfo?.companyName,
                    addressOnLicense: body?.changedInfo?.addressOnLicense,
                    provinceOnLicense: body?.changedInfo?.provinceCode,
                    districtOnLicense: body?.changedInfo?.districtCode,
                    wardOnLicense: body?.changedInfo?.wardCode,
                    detailOnLicense: body?.changedInfo?.detailAddress,
                    businessLicenseUrl: body?.changedInfo?.businessLicenseUrl,
                    // new address
                    newProvinceCode: body?.changedInfo?.newProvinceCode,
                    newWardCode: body?.changedInfo?.newWardCode,
                    oldInfo: JSON.stringify({
                        companyName: body?.companyName,
                        addressOnLicense: body?.addressOnLicense,
                        provinceOnLicense: body?.addressOnLicenseDetail?.provinceCode,
                        districtOnLicense: body?.addressOnLicenseDetail?.districtCode,
                        wardOnLicense: body?.addressOnLicenseDetail?.wardCode,
                        detailOnLicense: body?.addressOnLicenseDetail?.detailAddress,
                        businessLicenseUrl: body?.businessLicenseUrl,
                        // new address
                        newProvinceOnLicense: body?.addressOnLicenseDetail?.newProvinceCode,
                        newWardOnLicense: body?.addressOnLicenseDetail?.newWardCode,
                    })
                }
            } else {
                loanCustomerData = {
                    ...loanCustomerData,
                    provinceOnLicense: body?.addressOnLicenseDetail?.provinceCode,
                    districtOnLicense: body?.addressOnLicenseDetail?.districtCode,
                    wardOnLicense: body?.addressOnLicenseDetail?.wardCode,
                    detailOnLicense: body?.addressOnLicenseDetail?.detailAddress,
                    // new address
                    newProvinceOnLicense: body?.addressOnLicenseDetail?.newProvinceCode,
                    newWardOnLicense: body?.addressOnLicenseDetail?.newWardCode,
                }
            }
            const preparingShareholders = await this.prepareInsertShareholders(body?.shareholders?.members);
            
            // Process warehouses - keep newProvinceCode and newWardCode as provided by user
            let processedWarehouses = body.warehouses;
            
            // Process representations - keep newProvinceCode and newWardCode in perAddress as provided by user
            let processedRepresentations = body.representations;
            
            await Promise.all([
                loanContractRepo.saveBranches(this.contractNumber, body.branches),
                loanContractRepo.insertLoanCustomerManagers(this.contractNumber, body.managers || []),
                sqlHelper.patchUpdate({
                    table: 'loan_customer',
                    columns: loanCustomerRepo.columns,
                    values: sqlHelper.generateValues(utils.convertCamelToSnake(loanCustomerData), loanCustomerRepo.columns),
                    conditions: {
                        contract_number: this.contractNumber
                    }
                }),
                loanContractRepo.updateLoanCustomerRepresentations(this.contractNumber, body.registrationNumber, processedRepresentations),
                loanContractRepo.saveLoanCustomerWarehouses(this.contractNumber, processedWarehouses),
                sqlHelper.insertData(
                    `loan_business_owner`,
                    loanBusinessOwnerRepo.columns,
                    sqlHelper.generateValues(utils.convertCamelToSnake(businessOwnerWrap), loanBusinessOwnerRepo.columns)
                ),
                loanContractRepo.saveLoanCustomerShareholders(this.contractNumber, preparingShareholders),
                loanContractRepo.insertLoanRevenues(this.contractNumber, body.financialInformation.revenues),
                loanContractRepo.insertLoanVatForms(this.contractNumber, body.financialInformation.vatForms),
                loanContractRepo.insertLoanCustomerPartners(this.contractNumber, PARTNER_TYPE.INT, body.financialInformation.inputPartners),
                loanContractRepo.insertLoanCustomerPartners(this.contractNumber, PARTNER_TYPE.OUT, body.financialInformation.outputPartners)
            ])
            this.saveDocuments(this.contractNumber, body);
            smeMisaService.scanDownloadDocuments(this.contractNumber, { req: this.req });

            this.checkAntiFraud(this.contractNumber);

            return this.res.status(200).json(new Response(
                ERROR_CODE.SUCCESS,
                `The application is received`,
                {
                    contractNumber: this.contractNumber,
                    status: STATUS.RECEIVEDA2
                }
            ));
        } catch (e) {
            console.error(e)
            return this.res.status(500).json(new ServerErrorResponse());
        }
    }

    setRepresentation = (body) => {
        if (!body?.representations?.[0]) {
            return body;
        }
        body = {
            ...body,
            sme_representation_name: body.representations[0].fullName,
            // sme_representation_gender,
            sme_representation_dob: body.representations[0].dob,
            sme_representation_id: body.representations[0].id,
            sme_representation_address_cur: body.representations[0]?.curAddress?.detail ?? body.representations[0].perAddress.detail, //không có cur
            sme_representation_issue_date: body.representations[0].issueDate,
            sme_representation_position: body.representations[0].position,
            sme_representation_issue_place: body.representations[0].issuePlace,
            sme_representation_email:  body.representations[0].email || body.businessOwner?.[0]?.email,
            sme_representation_address_per: body.representations[0].perAddress.detail,
            sme_representation_ward_per: body.representations[0].perAddress?.wardCode,
            sme_representation_district_per: body.representations[0].perAddress?.districtCode,
            sme_representation_province_per: body.representations[0].perAddress?.provinceCode,
            sme_representation_new_ward_per: body.representations[0].perAddress?.newWardCode,
            sme_representation_new_province_per: body.representations[0].perAddress?.newProvinceCode,
            sme_representation_ward_cur: body.representations[0]?.curAddress?.wardCode, //không có cur
            sme_representation_district_cur: body.representations[0]?.curAddress?.districtCode, //không có cur
            sme_representation_province_cur: body.representations[0]?.curAddress?.provinceCode, //không có cur
            sme_representation_phone_number: body.representations[0].phoneNumber
        }
        return body;
    }

    setManager = async (body) => {
        if (!body?.managers?.[0]) {
            return body;
        }

        const masterData = await Promise.all([
            getValueCode_v3(body.managers[0].perAddress?.wardCode,"WARD"),
            getValueCode_v3(body.managers[0].perAddress?.districtCode,'DISTRICT'),
            getValueCode_v3(body.managers[0].perAddress?.provinceCode,'PROVINCE'),
            parseValueCode(body.managers[0].perAddress?.newWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
            parseValueCode(body.managers[0].perAddress?.newProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE),
            getValueCode_v3(body.managers[0]?.curAddress?.wardCode,"WARD"),
            getValueCode_v3(body.managers[0]?.curAddress?.districtCode,'DISTRICT'),
            getValueCode_v3(body.managers[0]?.curAddress?.provinceCode,'PROVINCE'),
            parseValueCode(body.managers[0]?.curAddress?.newWardCode, ADDRESS_CODE_TYPE.NEW_WARD),
            parseValueCode(body.managers[0]?.curAddress?.newProvinceCode, ADDRESS_CODE_TYPE.NEW_PROVINCE),
        ])

        body = {
            ...body,
            sme_manager_name: body.managers[0].fullName,
            sme_manager_id: body.managers[0].id,
            sme_manager_address_per: [
                body.managers[0].perAddress.detail,
                masterData[0],
                masterData[1],
                masterData[2],
                masterData[3],
                masterData[4],
            ].filter(element => element)
                .join(", "),
            sme_manager_address_cur: [
                body.managers[0]?.curAddress?.detail,
                masterData[5],
                masterData[6],
                masterData[7],
                masterData[8],
                masterData[9],
            ].filter(element => element)
                .join(", "),
        }
        return body;
    }

    prepareInsertShareholders = async (shareholders) => {
        if (!shareholders || shareholders?.length === 0) {
            return shareholders
        }
        
        // Debug logging
        console.log(`prepareInsertShareholders - input:`, JSON.stringify(shareholders, null, 2));
        
        let newShareholders = []
        for (const shareholder of shareholders) {
            // Check both subject field and isIndividual field for compatibility
            const isIndividual = (shareholder.subject === `INDIVIDUAL`) || (shareholder.isIndividual === true);
            
            console.log(`Processing shareholder:`, {
                subject: shareholder.subject,
                isIndividual: shareholder.isIndividual,
                computed_isIndividual: isIndividual
            });
            
            const s = {
                ...shareholder,
                fullName: isIndividual ? shareholder.fullName : shareholder.representations?.[0]?.fullName,
                identityType: isIndividual ? shareholder.identityType : shareholder.representations?.[0]?.identityType,
                id: isIndividual ? shareholder.id : shareholder.representations?.[0]?.id,
                issueDate: isIndividual ? shareholder.issueDate : shareholder.representations?.[0]?.issueDate,
                issuePlace: isIndividual ? shareholder.issuePlace : shareholder.representations?.[0]?.issuePlace,
                perProvinceCode: isIndividual ? shareholder.perAddress.provinceCode : shareholder.representations?.[0]?.perAddress?.provinceCode,
                perDistrictCode: isIndividual ? shareholder.perAddress.districtCode : shareholder.representations?.[0]?.perAddress?.districtCode,
                perWardCode: isIndividual ? shareholder.perAddress.wardCode : shareholder.representations?.[0]?.perAddress?.wardCode,
                perDetailAddress: isIndividual ? shareholder.detail : shareholder.representations?.[0]?.perAddress?.detail,
                curProvinceCode: isIndividual ? shareholder.curAddress.provinceCode : shareholder.representations?.[0]?.curAddress?.provinceCode,
                curDistrictCode: isIndividual ? shareholder.curAddress.districtCode : shareholder.representations?.[0]?.curAddress?.districtCode,
                curWardCode: isIndividual ? shareholder.curAddress.wardCode : shareholder.representations?.[0]?.curAddress?.wardCode,
                curDetailAddress: isIndividual ? shareholder.curAddress.detail : shareholder.representations?.[0]?.curAddress?.detail,
                businessProvinceCode: shareholder.businessAddress?.provinceCode,
                businessDistrictCode: shareholder.businessAddress?.districtCode,
                businessWardCode: shareholder.businessAddress?.wardCode,
                businessDetailAddress: shareholder.businessAddress?.detail,
                capitalContributionRatio: shareholder.capitalContributionRatio,
                // new address
                perNewProvinceCode: isIndividual ? shareholder.perAddress?.newProvinceCode : shareholder.representations?.[0]?.perAddress?.newProvinceCode,
                perNewWardCode: isIndividual ? shareholder.perAddress?.newWardCode : shareholder.representations?.[0]?.perAddress?.newWardCode,
                curNewProvinceCode: isIndividual ? shareholder.curAddress?.newProvinceCode : shareholder.representations?.[0]?.curAddress?.newProvinceCode,
                curNewWardCode: isIndividual ? shareholder.curAddress?.newWardCode : shareholder.representations?.[0]?.curAddress?.newWardCode,
                businessNewProvinceCode: shareholder.businessAddress?.newProvinceCode,
                businessNewWardCode: shareholder.businessAddress?.newWardCode
            }
            newShareholders.push(s);
        }
        return newShareholders
    }

    checkAntiFraud = async (contractNumber) => {
        try {
            const loan = await loanContractRepo.findOneMisaLoanContract(contractNumber);
            if (!loan?.id) {
                throw Error(`${contractNumber} | checkAntiFraud error: loan not found`);
            }

            let persons = loan.loan_customer_representations.map((rep) => ({
                idNumber: rep.id_number,
                otherIdNumber: '',
                fullName: rep.full_name,
                address: ''
            }));
            
            let eligiblePersons = [];
            let enterprises = [{
                registrationNumber: loan.loan_customer.registration_number,
                taxCode: loan.loan_customer.tax_id,
                companyName: loan.loan_customer.company_name,
                address: loan.loan_customer.address_on_license,
            }];
            if (loan.loan_business_owner.subject === 'INDIVIDUAL') {
                if (!this.isDuplicatePerson({ idNumber: loan.loan_business_owner.id_number }, persons)) {
                    persons.push({
                        idNumber: loan.loan_business_owner.id_number,
                        otherIdNumber: '',
                        fullName: loan.loan_business_owner.full_name,
                        address: ''
                    })
                    eligiblePersons.push({
                        customerName: loan.loan_business_owner.full_name,
                        idNumber: loan.loan_business_owner.id_number,
                        otherIdNumber: '',
                        issueDate: loan.loan_business_owner.issue_date,
                        issuePlace: loan.loan_business_owner.issue_place,
                        dateOfBirth: loan.loan_business_owner.dob,
                        gender: null,
                        phoneNumber: loan.loan_business_owner.phone_number
                    })
                }
                //đã kết hôn
                if (loan.loan_business_owner.married_status === 'M') {
                    // persons.push({
                    //     idNumber: loan.loan_business_owner.partner_id_number,
                    //     otherIdNumber: '',
                    //     fullName: loan.loan_business_owner.partner_full_name,
                    //     address: ''
                    // })
                    eligiblePersons.push({
                        customerName: loan.loan_business_owner.partner_full_name,
                        idNumber: loan.loan_business_owner.partner_id_number,
                        otherIdNumber: '',
                        issueDate: null,
                        issuePlace: null,
                        dateOfBirth: null,
                        gender: null,
                        phoneNumber: null
                    })
                }
            } else {
                enterprises.push({
                    registrationNumber: '',
                    taxCode: loan.loan_business_owner.tax_id,
                    companyName: loan.loan_business_owner.company_name,
                    address: '',
                });
            }
            if (loan.loan_customer_shareholders?.length > 0) {
                const shareholders = loan.loan_customer_shareholders;
                for (const shareholder of shareholders) {
                    if (shareholder.subject === 'INDIVIDUAL') {
                        if (!this.isDuplicatePerson({ idNumber: shareholder.id_number }, persons)) {
                            persons.push({
                                idNumber: shareholder.id_number,
                                otherIdNumber: '',
                                fullName: shareholder.full_name,
                                address: ''
                            })
                            eligiblePersons.push({
                                customerName: shareholder.full_name,
                                idNumber: shareholder.id_number,
                                otherIdNumber: '',
                                issueDate: shareholder.issue_date,
                                issuePlace: shareholder.issue_place,
                                dateOfBirth: null,
                                gender: null,
                                phoneNumber: shareholder.phone_number
                            })
                        }
                    } else {
                        if (!this.isDuplicateEnterprise({ taxCode: shareholder.tax_id }, enterprises)) {
                            enterprises.push({
                                registrationNumber: '',
                                taxCode: shareholder.tax_id,
                                companyName: shareholder.company_name,
                                address: '',
                            });
                        }
                    }
                }
            }

            const eligiblePayload = {
                requestId: uuid.v4(),
                legalRepresentative: {
                    customerName: loan.loan_customer_representations[0].full_name,
                    idNumber: loan.loan_customer_representations[0].id_number,
                    otherIdNumber: '',
                    issueDate: loan.loan_customer_representations[0].issue_date,
                    issuePlace: loan.loan_customer_representations[0].issue_place,
                    dateOfBirth: loan.loan_customer_representations[0].dob,
                    gender: null,
                    phoneNumber: loan.loan_customer_representations[0].phone_number,
                },
                otherPersons: eligiblePersons,
                productType: '',
                registrationNumber: loan.loan_customer.registration_number,
                taxCode: loan.sme_tax_id,
                companyName: loan.loan_customer.company_name,
                registrationDate: loan.loan_customer.registration_date,
                productCode: '',
                caseCreationTime: new Date(),
                partnerCode: loan.partner_code,
                channel: loan.channel,
                contractNumber: loan.contract_number,
                companyType: loan.loan_customer.business_type
            }

            const eligibleResult = await antiFraudService.checkEligibleSmeV2(eligiblePayload)
            if (!eligibleResult?.decision) {
                throw new Error(`${contractNumber} | AF2 | check eligible error`)
            }
            await loggingRepo.saveWorkflow(MisaStep.CHECK_ELIGIBLE, eligibleResult.decision, contractNumber, 'system');
            if (eligibleResult?.decision && eligibleResult.decision != STATUS.ELIGIBLE) {
                const { decision } = eligibleResult || {};
                await loanContractRepo.updateContractStatus(decision, contractNumber);
                //handle callback misa here
                await smeMisaService.callbackCicResult({
                    step: smeMisaService.CIC_STEP_MISA_CALLBACK.AF2,
                    contractNumber,
                    isPass: false,
                    loanEffectTime: LMS_DATE()
                });
                return;
            }
            
            const cicBody = {
                contractNumber: this.contractNumber,
                persons: persons,
                // enterprises: enterprises,
                stepCheck: CIC_STEP_CHECK.AF2
            }
            const cicResult = await antiFraudService.checkCicB11t(cicBody);
            if (!cicResult?.decision) {
                throw new Error(`${contractNumber} | AF2 | check checkCicB11t error`)
            }
            if (cicResult?.decision && cicResult.decision != STATUS.ELIGIBLE) {
                const { decision } = cicResult || {};
                await loanContractRepo.updateContractStatus(decision, contractNumber);
                //handle callback misa here
                await smeMisaService.callbackCicResult({
                    step: smeMisaService.CIC_STEP_MISA_CALLBACK.AF2,
                    contractNumber,
                    isPass: false,
                    loanEffectTime: cicResult?.nextTimeCanRequest ?? LMS_DATE()
                });
                return;
            }
            //create cust crm
            const createCustResult = await crmService.checkDedupEnterpriseV3(contractNumber);
            if (!createCustResult) {
                throw new Error(`${contractNumber} | AF2 | Create Customer error from CRM`);
            }

            await this.checkCicDetail(contractNumber);
        } catch (e) {
            console.error(`${contractNumber} | checkAntiFraud | error: `, e);
        }
    }

    checkCicDetail = async (contractNumber) => {
        const loan = await loanContractRepo.findOneMisaLoanContract(contractNumber);
        let persons = loan.loan_customer_representations.map((rep) => ({
            idNumber: rep.id_number,
            otherIdNumber: '',
            fullName: rep.full_name,
            address: ''
        }));
        let enterprises = [{
            registrationNumber: loan.loan_customer.registration_number,
            taxCode: loan.loan_customer.tax_id,
            companyName: loan.loan_customer.company_name,
            address: loan.loan_customer.address_on_license,
        }];
        if (loan.loan_business_owner.subject === 'INDIVIDUAL') {
            if (!this.isDuplicatePerson({ idNumber: loan.loan_business_owner.id_number }, persons)) {
                persons.push({
                    idNumber: loan.loan_business_owner.id_number,
                    otherIdNumber: '',
                    fullName: loan.loan_business_owner.full_name,
                    address: ''
                })
            }
            //đã kết hôn
            let businessTypeChecked = [BUSINESS_TYPE["01"], BUSINESS_TYPE["04"]];
            if (businessTypeChecked.includes(loan.business_type) && loan.loan_business_owner.married_status === 'M') {
                if (!loan.loan_business_owner.partner_id_number ||
                    !loan.loan_business_owner.partner_full_name) {
                    throw new Error(`checkCicDetail | ${contractNumber} | Thiếu thông tin vợ/ chồng`)
                }
                persons.push({
                    idNumber: loan.loan_business_owner.partner_id_number,
                    otherIdNumber: '',
                    fullName: loan.loan_business_owner.partner_full_name,
                    address: ''
                })
            }
        } else {
            enterprises.push({
                registrationNumber: '',
                taxCode: loan.loan_business_owner.tax_id,
                companyName: loan.loan_business_owner.company_name,
                address: '',
            });
        }
        if (loan.loan_customer_shareholders?.length > 0) {
            const shareholders = loan.loan_customer_shareholders;
            for (const shareholder of shareholders) {
                if (shareholder.subject === 'INDIVIDUAL') {
                    if (!this.isDuplicatePerson({ idNumber: shareholder.id_number }, persons)) {
                        persons.push({
                            idNumber: shareholder.id_number,
                            otherIdNumber: '',
                            fullName: shareholder.full_name,
                            address: ''
                        })
                    }
                } else {
                    if (!this.isDuplicateEnterprise({ taxCode: shareholder.tax_id }, enterprises)) {
                        enterprises.push({
                            registrationNumber: '',
                            taxCode: shareholder.tax_id,
                            companyName: shareholder.company_name,
                            address: '',
                        });
                    }
                }
            }
        }

        const cicResult = await antiFraudService.checkCicDetailsSme({
            contractNumber,
            custId: loan.cust_id,
            persons: persons,
            enterprises: enterprises,
            contractType: 'LIMIT',
            taxCode: loan?.sme_tax_id,
            partnerCode: loan?.partner_code,
        })
        return cicResult
    }

    saveDocuments = async (contractNumber, body) => {
        let documents = [];
        if (Array.isArray(body?.otherDocs) && body.otherDocs.length > 0) {
            documents.push(...body.otherDocs);
        }
        if (body?.loanApplicationFile &&
            body?.loanApplicationFile?.fileUrl &&
            body?.loanApplicationFile?.fileType) {
            documents.push(body.loanApplicationFile);
        }
        if (Array.isArray(body?.branches) && body.branches.length > 0) {
            for (const branch of body.branches) {
                if (Array.isArray(branch.docs) && branch.docs.length > 0) {
                    documents.push(...branch.docs);
                }
            }
        }
        if (Array.isArray(body?.warehouses?.docs) && body.warehouses.docs.length > 0) {
            documents.push(...body.warehouses.docs);
        }
        if (Array.isArray(body?.representations) && body.representations.length > 0) {
            for (const rep of body.representations) {
                if (Array.isArray(rep.docs) && rep.docs.length > 0) {
                    documents.push(...rep.docs);
                }
            }
        }
        if (Array.isArray(body?.managers) && body.managers.length > 0) {
            for (const rep of body.managers) {
                if (Array.isArray(rep.docs) && rep.docs.length > 0) {
                    documents.push(...rep.docs);
                }
            }
        }
        if (Array.isArray(body?.businessOwner?.docs) && body.businessOwner.docs.length > 0) {
            documents.push(...body.businessOwner.docs);
        }
        if (Array.isArray(body?.businessOwner?.partner?.docs) && body?.businessOwner?.partner?.docs?.length > 0) {
            documents.push(...body.businessOwner.partner.docs);
        }
        if (Array.isArray(body?.shareholders?.docs) && body.shareholders.docs.length > 0) {
            documents.push(...body.shareholders.docs);
        }
        if (body?.businessLicenseUrl) {
            let contentType = utils.getContentTypeFromMisaUrl(body.businessLicenseUrl);
            let doc = {
                fileUrl: body.businessLicenseUrl,
                fileType: contentType,
                docType: 'SBIZ'
            }
            documents.push(doc);
        }
        if (body?.charterUrl) {
            let contentType = utils.getContentTypeFromMisaUrl(body.charterUrl);
            let doc = {
                fileUrl: body.charterUrl,
                fileType: contentType,
                docType: 'SCR'
            }
            documents.push(doc);
        }
        if (body?.taxRegistrationUrl) {
            let contentType = utils.getContentTypeFromMisaUrl(body.taxRegistrationUrl);
            let doc = {
                fileUrl: body.taxRegistrationUrl,
                fileType: contentType,
                docType: 'STCRC'
            }
            documents.push(doc);
        }
        smeMisaService.saveAf2Documents(documents, contractNumber, { req: this.req });
    }

    isDuplicatePerson(newPerson, personArray) {
        return personArray.some(person => person.idNumber === newPerson.idNumber);
    }

    isDuplicateEnterprise(newEnterprise, enterprises) {
        return enterprises.some(enterprise => enterprise.taxCode === newEnterprise.taxCode);
    }
}

class MisaTc2 extends MisaAf2 {
    constructor(req, res) {
        // for MISA TC2
        req.body.partnerCode = PARTNER_CODE.MISA
        req.body.channel = 'MC';
        req.body.disbursementMethod = DISURSEMENT_METHOD.TRANSFER
        req.body.contractType = CONTRACT_TYPE.CREDIT_LINE
        req.body.contract_type = CONTRACT_TYPE.CREDIT_LINE
        req.body.lmsType = CONTRACT_TYPE.CREDIT_LINE

        // baseA2
        super(req, res);
    }

    async convertTc2Body() {
        const newBody = convertBody(this.body, REQUEST_TYPE.MISA_TC2, global.convertCache)
        this.convertedBody = newBody
    }

    async validateTc2(requestPayload) {
        try {
            const schema = Joi.object({
                requestId: Joi.string().required(),
                contractNumber: Joi.string().required(),
                productCode: Joi.string().required(), // for MISA TC1
                loanPurpose: Joi.string().required(), // Mục đích vay vốn
                totalTurnoverNextYear: Joi.string().required(), // Doanh thu dự kiến 12 tháng tiếp theo
                totalCostOverNextYear: Joi.string().required(), // Tổng chi phí dự kiến 12 tháng tiếp theo
                profitBeforeTaxNextYear: Joi.string().required(), // Lợi nhuận trước thuế dự kiến 12 tháng tiếp theo
                capitalNeed: Joi.string().required(), // Tổng nhu cầu vốn lưu động dự kiến (a)
                loanAmount: Joi.string().required(), // Vốn vay EVNFC (b)
                loansOtherFinancialInstitutions: Joi.string().required(),// Vốn vay các TCTD khác (c)
                ownerEquity: Joi.string().allow(null).allow(''), // Vốn chủ sở hữu tham gia vào phương án (d)
                otherCapital: Joi.string().required(), // Vốn huy động khác (e)
                charterCapital: Joi.string().required(), // Vốn điều lệ
                tenor: Joi.string().required(), // Thời hạn vay EVNFC
                tenorPerKunn: Joi.string().required(), // Thời hạn vay mỗi KUNN
                repaymentMethod: Joi.string().required(), // Phương thức trả nợ
                repaymentSources: Joi.string().required(), // Nguồn trả nợ
                taxId: Joi.string().required(), // mã số thuế DN
                registrationNumber: Joi.string().required(), // mã số DN
                companyName: Joi.string().required(), // Tên doanh nghiệp
                addressOnLicense: Joi.string().required(),  // Địa chỉ trên GPKD MISA
                addressOnLicenseDetail: Joi.object({ // Địa chỉ chi tiết trên GPKD MISA
                    provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                    districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                    wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                    detailAddress: Joi.string().required(),
                    newProvinceCode: Joi.string().allow(null).allow(''),
                    newWardCode: Joi.string().allow(null).allow('')
                }).required(),
                // businessRegistrationAddress, //đkkd
                // firstRegistrationDate: Joi.string().required(),  // Ngày cấp chứng nhận ĐKKD lần đầu
                registrationDate: Joi.string().required(), // Ngày cấp chứng nhận ĐKKD
                // registrationCertIssueDate, //Ngày cấp chứng nhận ĐKKD
                smeHeadquartersProvince: Joi.string().required(), // Địa chỉ trụ sở chính
                smeHeadquartersDistrict: Joi.string().required(), // Địa chỉ trụ sở chính
                smeHeadquartersWard: Joi.string().required(), // Địa chỉ trụ sở chính
                smeHeadquartersAddress: Joi.string().required(), // Địa chỉ trụ sở chính
                registrationCertNo: Joi.string().required(), // Số chứng nhận ĐKKD
                registrationCertIssuePlace: Joi.string().required(), // Nơi cấp
                businessType: Joi.string().required(), // Loại hình pháp lý doanh nghiệp
                businessIndustry: Joi.string().required(), // Ngành nghề kinh doanh chính
                conditionalBusinessIndustry: Joi.number().required(), // Ngành nghề kinh doanh có điều kiện
                companyPhoneNumber: Joi.string().required(), // SĐT công ty
                companyEmail : Joi.string().required(), // Email công ty
                businessLicenseUrl: Joi.string().required(), // Ảnh chụp/bản scan giấy phép đăng ký kinh doanh
                charterUrl: Joi.string().required(), // Ảnh chụp/bản scan điều lệ doanh nghiệp
                taxRegistrationUrl: Joi.string().required(), // Ảnh chụp/bản scan đăng ký mã số thuế
                bankCode: Joi.string().required(), // Ngân hàng
                // bankBranchCode,
                bankAccount: Joi.string().required(), // Số tài khoản của khách hàng
                isChange: Joi.number().valid(0,1).required(), // có thay đổi thông tin hay không?
                changedInfo: Joi.object({  // thông tin thay đổi
                    businessLicenseUrl: Joi.string().uri().optional(),
                    companyName: Joi.string().optional(),
                    provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                    districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                    wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                    detailAddress: Joi.string().required(),
                    newProvinceCode: Joi.string().optional().allow(null, ''),
                    newWardCode: Joi.string().optional().allow(null, ''),
                }).unknown(true).optional(),
                financialInformation: Joi.object({
                    vatForms: Joi.array().items( // Tờ khai thuế GTGT 12 tháng gần nhất hoặc các quý gần nhất
                        Joi.object({
                            period: Joi.string().required(),
                            name: Joi.string().required(),
                            docs: Joi.array().required(),
                        }).unknown(true).required()
                    ).required(),
                    inputPartners: Joi.array().items( // Thông tin 3  đối tác đầu vào
                        Joi.object({
                            taxId: Joi.string().required(),
                            companyName: Joi.string().required()
                        }).unknown(true).required()
                    ).required(),
                    outputPartners: Joi.array().items( // Thông tin 3  đối tác đầu ra
                        Joi.object({
                            taxId: Joi.string().required(),
                            companyName: Joi.string().required()
                        }).unknown(true).required()
                    ).required()
                }).unknown(true).required(),
                loanApplicationFile: Joi.object({ // Đề nghị tái cấp vốn
                    fileName: Joi.string().required(),
                    fileType: Joi.string().required(),
                    fileUrl: Joi.string().required(),
                    docType: Joi.string().required()
                }).unknown(true).required(),
                // warehouses, branches // THÔNG TIN CHI NHÁNH/ KHO HÀNG ( nếu có )
                representations: Joi.array().items( // Thông tin người đại diện theo pháp luật
                    Joi.object({
                        fullName: Joi.string().required(), // Họ và tên
                        // position,
                        dob: Joi.string().required(), // Ngày sinh
                        id: Joi.string().required(), // Số CCCD/CC
                        issueDate: Joi.string().required(), // Ngày cấp
                        issuePlace: Joi.string().required(), // Nơi cấp
                        phoneNumber: Joi.string().required(), // Di động
                        email: Joi.string().required(), // Email
                        // managementExperience: Joi.string().required(), // Kinh nghiệm quản lý
                        perAddress: Joi.object({ // Địa chỉ thường trú
                            provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            detail: Joi.string().required(),
                            newProvinceCode: Joi.string().allow(null).allow(''),
                            newWardCode: Joi.string().allow(null).allow('')
                        }).required(),
                        // curAddress, // KHÔNG CÓ - Địa chỉ hiện tại
                        docs: Joi.array().required(), // Quyết định bổ nhiệm của người đại diện theo pháp luật + Ảnh chụp/bản scan mặt trước, mặt sau của CCCD/CC của Người đại diện pháp luật + Ảnh chụp trực tiếp người đại diện pháp luật tại trụ sở chính công ty
                    }).unknown(true).required()
                ).required(),
                managers: Joi.array().items( // Thông tin giám đốc/ tổng giám đốc
                    Joi.object({
                        fullName: Joi.string().required(), // Họ và tên
                        position: Joi.string().required(), // Chức vụ
                        id: Joi.string().required(), // Số CCCD/CC
                        issueDate: Joi.string().required(), // Ngày cấp
                        issuePlace: Joi.string().required(), // Nơi cấp
                        phoneNumber: Joi.string().required(), // Di động
                        email: Joi.string().required(), // Email
                        perAddress: Joi.object({  // Địa chỉ thường trú
                            provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            detail: Joi.string().required(),
                            newProvinceCode: Joi.string().allow(null).allow(''),
                            newWardCode: Joi.string().allow(null).allow('')
                        }).required(),
                        curAddress: Joi.object({
                            provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                            detail: Joi.string().required(),
                            newProvinceCode: Joi.string().allow(null).allow(''),
                            newWardCode: Joi.string().allow(null).allow('')
                        }),
                        docs: Joi.array().required(), // Ảnh chụp/bản scan mặt trước, mặt sau của CCCD/CC của giám đốc/ Tổng giám đốc + Quyết định bổ nhiệm của Giám đốc/ Tổng giám đốc
                    }).unknown(true).required()
                ),
                businessOwner: Joi.object({ // Thông tin chủ doanh nghiệp (là cá nhân / là doanh nghiệp)
                    subject: Joi.string().valid("INDIVIDUAL", "ORGANIZATION").required(), // INDIVIDUAL, ORGANIZATION
                    
                    // Fields for INDIVIDUAL
                    id: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    fullName: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    issueDate: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    issuePlace: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    dob: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    phoneNumber: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    email: Joi.string().email().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    marriedStatus: Joi.string().valid("MARRIED", "SINGLE").when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    perAddress: Joi.object({
                        provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        detail: Joi.string().required(),
                        newProvinceCode: Joi.string().allow(null).allow(''),
                        newWardCode: Joi.string().allow(null).allow('')
                    }).when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    curAddress: Joi.object({
                        provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        detail: Joi.string().required(),
                        newProvinceCode: Joi.string().allow(null).allow(''),
                        newWardCode: Joi.string().allow(null).allow('')
                    }).when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    
                    // Fields for ORGANIZATION
                    taxId: Joi.string().when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    companyName: Joi.string().when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    businessAddress: Joi.object({
                        provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                        detail: Joi.string().required(),
                        newProvinceCode: Joi.string().allow(null).allow(''),
                        newWardCode: Joi.string().allow(null).allow('')
                    }).when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    businessPhoneNumber: Joi.string().when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    businessEmail: Joi.string().email().when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.allow(null).optional() }),
                    
                    // Optional fields
                    position: Joi.optional(),
                    entityType: Joi.string().valid("LEGAL_REPRESENTATIVE", "AUTHORIZED_PERSON").optional(),
                    authorizationDocNo: Joi.string().optional(),
                    
                    // Partner validation
                    partner: Joi.object({
                        fullName: Joi.string().required(),
                        id: Joi.string().required(),
                        docs: Joi.array().required()
                    }).when('subject', { is: 'INDIVIDUAL',
                        then: Joi.when('marriedStatus', { is: 'MARRIED', then: Joi.required(),  otherwise: Joi.allow(null).optional() }), otherwise: Joi.allow(null).optional() }),
                    
                    docs: Joi.array().required(), // Với subject = ORGANIZATION: SBIZ: Ảnh chụp/bản scan giấy phép đăng ký kinh doanh (bắt buộc) + SDALR: Ảnh chụp/bản scan Quyết định bổ nhiệm của người đại diện theo pháp luật + SPALR: Ảnh chụp/bản scan văn bản uỷ quyền của người đại diện theo pháp luật cho người được uỷ quyền. ( Xuất hiện trong trường hợp người đại diện không phải là Đại diện theo pháp luật) 
                                                    // Với subject = INDIVIDUAL: Ảnh chụp/bản scan mặt trước, mặt sau của CCCD/CC của chủ doanh nghiệp 
                }).unknown(true).required(),
                // loại hình doanh nghiệp thuộc: - công ty cổ phần, - công ty tnhh 2 thành viên trở lên, - công ty hơp danh
                shareholders: Joi.object({ // Cổ đông/Thành viên góp vốn
                    members: Joi.array().items(
                        Joi.object({
                            subject: Joi.string().valid("INDIVIDUAL", "ORGANIZATION").required(),

                            // INDIVIDUAL
                            id: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.optional() }),
                            fullName: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.optional() }),
                            issueDate: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.optional() }),
                            issuePlace: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.optional() }),
                            phoneNumber: Joi.string().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.optional() }),
                            email: Joi.string().email().when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.optional() }),
                            perAddress: Joi.object({
                                provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                detail: Joi.string().required(),
                                newProvinceCode: Joi.string().allow(null).allow(''),
                                newWardCode: Joi.string().allow(null).allow('')
                            }).when('subject', { is: 'INDIVIDUAL', then: Joi.required(), otherwise: Joi.optional() }),
                            curAddress: Joi.object({
                                provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                detail: Joi.string().required(),
                                newProvinceCode: Joi.string().allow(null).allow(''),
                                newWardCode: Joi.string().allow(null).allow('')
                            }).optional(),
                            authorization_doc_number: Joi.string().optional(),
                            entity_type: Joi.string().valid("LEGAL_REPRESENTATIVE", "AUTHORIZED_PERSON").optional(),
                            identityType: Joi.string().optional(),
                            capitalContributionRatio: Joi.string().optional(),

                            // ORGANIZATION
                            taxId: Joi.string().when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.optional() }),
                            companyName: Joi.string().when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.optional() }),
                            businessAddress: Joi.object({
                                provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                detail: Joi.string().required(),
                                newProvinceCode: Joi.string().allow(null).allow(''),
                                newWardCode: Joi.string().allow(null).allow('')
                            }).when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.optional() }),
                            businessEmail: Joi.string().email().when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.optional() }),
                            businessPhoneNumber: Joi.string().when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.optional() }),
                            representations: Joi.array().items(
                                Joi.object({
                                    fullName: Joi.string().required(),
                                    id: Joi.string().required(),
                                    issueDate: Joi.string().required(),
                                    issuePlace: Joi.string().required(),
                                    perAddress: Joi.object({
                                        provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                        districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                        wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                        detail: Joi.string().required(),
                                        newProvinceCode: Joi.string().allow(null).allow(''),
                                        newWardCode: Joi.string().allow(null).allow('')
                                    }).required(),
                                    curAddress: Joi.object({
                                        provinceCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                        districtCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                        wardCode: Joi.when('newWardCode', { is: Joi.valid(null, ''), then: Joi.string().required(), otherwise: Joi.optional() }),
                                        detail: Joi.string().required(),
                                        newProvinceCode: Joi.string().allow(null).allow(''),
                                        newWardCode: Joi.string().allow(null).allow('')
                                    }).optional()
                                }).unknown(true).required()
                            ).when('subject', { is: 'ORGANIZATION', then: Joi.required(), otherwise: Joi.optional() })
                        }).unknown(true).required()
                    ).required(),
                    docs: Joi.array().required()
                }).unknown(true),
            }).unknown(true)
            const { error } = schema.validate(requestPayload)
            if (error) {
                console.log(`error: `, JSON.stringify(error))
                return { isValid: false, errorCode: error?.details?.[0]?.context?.key, errorMessage: error?.details?.[0].message }
            }

            // check bao cao tai chinh
            if (!Array.isArray(requestPayload?.financialInformation?.revenues) || requestPayload.financialInformation.revenues.length === 0) {
                return { isValid: false, errorCode: `financialInformation`, errorMessage: `Thiếu file báo cáo tài chính` }
            }
            const latestRevenue = requestPayload.financialInformation.revenues.reduce((latest, current) => {
                return parseInt(current.year) > parseInt(latest.year) ? current : latest;
            }, requestPayload.financialInformation.revenues[0]);
            if (latestRevenue?.financialReportDocs?.length === 0 ||
                !latestRevenue.financialReportDocs.find(doc => doc.docType === 'TDTBCTC') ||
                !latestRevenue.financialReportDocs.find(doc => doc.docType === 'TDTBCTC')?.fileUrl) {
                return { isValid: false, errorCode: `financialInformation`, errorMessage: `Thiếu file báo cáo tài chính` }
            }
            const isExistedTDTBCTC = await smeMisaService.isExistedFile(latestRevenue.financialReportDocs.find(doc => doc.docType === 'TDTBCTC').fileUrl, { req: this.req });
            if (!isExistedTDTBCTC) {
                return { isValid: false, errorCode: `financialInformation`, errorMessage: `File báo cáo tài chính không hợp lệ` }
            }

            // check body product_code
            const validBodyProductCodes = [
                PRODUCT_CODE.MISA.SME_MISA_HM_DIAMOND,
                PRODUCT_CODE.MISA.SME_MISA_HM_GOLD,
                PRODUCT_CODE.MISA.SME_MISA_HM_SILVER,
                PRODUCT_CODE.MISA.SME_MISA_HM_PLATINUM_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_PREMIUM_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_STANDARD_LS,
                PRODUCT_CODE.MISA.SME_MISA_HM_VIP_LS
            ];
            if (!validBodyProductCodes.includes(requestPayload.productCode)) {
                return { isValid: false, errorCode: `productCode`, errorMessage: `Mã sản phẩm hợp đồng tái cấp không thỏa mãn` };
            }

            return { isValid: true };
        } catch (e) {
            console.error(`func | validateTc2 | error: `, e);
            return { isValid: false, errorCode: `server_error`, errorMessage: `server_error` };
        }
    }

    async processTc2() {
        let misaBody = this.body;
        const validateSmeInfoResult = await this.validateTc2(misaBody);
        if (!validateSmeInfoResult.isValid) {
            const errors = [{
                errorCode: validateSmeInfoResult.errorCode,
                errorMessage: validateSmeInfoResult.errorMessage
            }]
            return this.res.status(400).json(new BadRequestResponse(errors));
        }

        const loan = await loanContractRepo.getLoanContractWithSmeInfo({ ...misaBody });
        if (!loanContractService.isRefinance(loan)){
            return this.res.status(400).json(new BadRequestResponse([], `Hợp đồng không phải là hợp đồng tái cấp`));
        }

        if (loan?.status !== STATUS.ELIGIBLE) {
            return this.res.status(400).json(new BadRequestResponse([], `Trạng thái hồ sơ không để submit af2`));
        }

        // One oldContract
        const oldLoanContract = await loanContractRepo.getLoanContractWithSmeInfo({ ...misaBody, contractNumber: loan.old_contract_number });
        if (!oldLoanContract) {
            return this.res.status(400).json(new BadRequestResponse([], `Hợp đồng không phải là hợp đồng tái cấp`));
        }

        misaBody.headquarters = {
            provinceCode: misaBody?.smeHeadquartersProvince,
            districtCode: misaBody?.smeHeadquartersDistrict,
            wardCode: misaBody?.smeHeadquartersWard,
            newProvinceCode: misaBody?.smeHeadquartersNewProvince,
            newWardCode: misaBody?.smeHeadquartersNewWard,
            detail: misaBody?.smeHeadquartersAddress,
        }
        let body = await masterdataService.convertEvfLov({ partnerCode: PARTNER_CODE.MISA, convertObject: misaBody });
        body = {
            ...body,
            smeHeadquartersProvince: body.headquarters.provinceCode,
            smeHeadquartersDistrict: body.headquarters.districtCode,
            smeHeadquartersWard: body.headquarters.wardCode,
            smeHeadquartersAddress: body.headquarters.detail,
            smeHeadquartersNewProvince: body.headquarters.newProvinceCode,
            smeHeadquartersNewWard: body.headquarters.newWardCode,
        }

        body.contractNumber = this.contractNumber;
        this.oldContractNumber = oldLoanContract.contract_number;
        this.body = { ...body };
        await this.convertTc2Body();

        this.convertedBody.status = STATUS.RECEIVEDTC2 //set default value
        this.convertedBody = this.setRepresentation(this.convertedBody);
        this.convertedBody = await this.setManager(this.convertedBody);
        this.body = { ...body }
        const updateFullLoanRs = await Promise.all([
            loanContractRepo.updateLoanContract(this.convertedBody),
            loggingRepo.saveWorkflow(MisaStep.TC2, this.convertedBody.status, this.contractNumber, 'system')
        ])
        if (!updateFullLoanRs[0]) {
            console.error(`${this.contractNumber} | updateLoanContract | error`);
            // still lock old Contract 
            const responseBody = {
                code: ERROR_CODE.INT_SERVER_ERROR,
                message: `${this.contractNumber} | updateLoanContract | error`
            }
            loggingService.saveRequestV2(this.req.poolWrite, this.convertedBody, responseBody, this.convertedBody.contract_number, this.convertedBody.request_id, this.convertedBody.partner_code)
            return this.res.status(500).json(new ServerErrorResponse(null, responseBody.message));
        }

        let businessOwnerWrap = {
            ...body.businessOwner,
            idNumber: body.businessOwner.id,
            contractNumber: this.contractNumber,
            businessProvinceCode: body.businessOwner.businessAddress?.provinceCode,
            businessDistrictCode: body.businessOwner.businessAddress?.districtCode,
            businessWardCode: body.businessOwner.businessAddress?.wardCode,
            businessDetailAddress: body.businessOwner.businessAddress?.detail,
            // new address
            businessNewProvinceCode: body.businessOwner.businessAddress?.newProvinceCode,
            businessNewWardCode: body.businessOwner.businessAddress?.newWardCode,
            perProvinceCode: body.businessOwner.perAddress.provinceCode,
            perDistrictCode: body.businessOwner.perAddress.districtCode,
            perWardCode: body.businessOwner.perAddress.wardCode,
            perDetailAddress: body.businessOwner.perAddress.detail,
            // new address
            perNewProvinceCode: body.businessOwner.perAddress.newProvinceCode,
            perNewWardCode: body.businessOwner.perAddress.newWardCode,
            curProvinceCode: body.businessOwner.curAddress.provinceCode,
            curDistrictCode: body.businessOwner.curAddress.districtCode,
            curWardCode: body.businessOwner.curAddress.wardCode,
            curDetailAddress: body.businessOwner.curAddress.detail,
            // new address
            curNewProvinceCode: body.businessOwner.curAddress.newProvinceCode,
            curNewWardCode: body.businessOwner.curAddress.newWardCode,
            partnerFullName: body?.businessOwner?.partner?.fullName,
            partnerIdNumber: body?.businessOwner?.partner?.id,
            partnerDob: body?.businessOwner?.partner?.dob,
            partnerPhoneNumber: body?.businessOwner?.partner?.phoneNumber,
            partnerPerProvinceCode: body?.businessOwner?.partner?.perAddress?.provinceCode,
            partnerPerDistrictCode: body?.businessOwner?.partner?.perAddress?.districtCode,
            partnerPerWardCode: body?.businessOwner?.partner?.perAddress?.wardCode,
            partnerPerDetailAddress: body?.businessOwner?.partner?.perAddress?.detail,
            // new address
            partnerPerNewProvinceCode: body?.businessOwner?.partner?.perAddress?.newProvinceCode,
            partnerPerNewWardCode: body?.businessOwner?.partner?.perAddress?.newWardCode,
            partnerCurProvinceCode: body?.businessOwner?.partner?.curAddress?.provinceCode,
            partnerCurDistrictCode: body?.businessOwner?.partner?.curAddress?.districtCode,
            partnerCurWardCode: body?.businessOwner?.partner?.curAddress?.wardCode,
            partnerCurDetailAddress: body?.businessOwner?.partner?.curAddress?.detail,
            // new address
            partnerCurNewProvinceCode: body?.businessOwner?.partner?.curAddress?.newProvinceCode,
            partnerCurNewWardCode: body?.businessOwner?.partner?.curAddress?.newWardCode,
            isDeleted: 0
        }
        businessOwnerWrap.marriedStatus = body?.businessOwner?.marriedStatus
            ? body?.businessOwner?.marriedStatus === 'SINGLE' ? 'C' : 'M' : null;
        let loanCustomerData = { ...body, phoneNumber: body.companyPhoneNumber, email: body.companyEmail };
        if (body.isChange == 1) {
            loanCustomerData = {
                ...loanCustomerData,
                companyName: body?.changedInfo?.companyName,
                addressOnLicense: body?.changedInfo?.addressOnLicense,
                provinceOnLicense: body?.changedInfo?.provinceCode,
                districtOnLicense: body?.changedInfo?.districtCode,
                wardOnLicense: body?.changedInfo?.wardCode,
                detailOnLicense: body?.changedInfo?.detailAddress,
                businessLicenseUrl: body?.changedInfo?.businessLicenseUrl,
                //new address
                newProvinceCode: body?.changedInfo?.newProvinceCode,
                newWardCode: body?.changedInfo?.newWardCode,
                oldInfo: JSON.stringify({
                    companyName: body?.companyName,
                    addressOnLicense: body?.addressOnLicense,
                    provinceOnLicense: body?.addressOnLicenseDetail?.provinceCode,
                    districtOnLicense: body?.addressOnLicenseDetail?.districtCode,
                    wardOnLicense: body?.addressOnLicenseDetail?.wardCode,
                    detailOnLicense: body?.addressOnLicenseDetail?.detailAddress,
                    businessLicenseUrl: body?.businessLicenseUrl,
                    //new address
                    newProvinceOnLicense: body?.addressOnLicenseDetail?.newProvinceCode,
                    newWardOnLicense: body?.addressOnLicenseDetail?.newWardCode,
                })
            }
        } else {
            loanCustomerData = {
                ...loanCustomerData,
                provinceOnLicense: body?.addressOnLicenseDetail?.provinceCode,
                districtOnLicense: body?.addressOnLicenseDetail?.districtCode,
                wardOnLicense: body?.addressOnLicenseDetail?.wardCode,
                detailOnLicense: body?.addressOnLicenseDetail?.detailAddress,
                //new address
                newProvinceOnLicense: body?.addressOnLicenseDetail?.newProvinceCode,
                newWardOnLicense: body?.addressOnLicenseDetail?.newWardCode,
            }
        }
        const preparingShareholders = await this.prepareInsertShareholders(body?.shareholders?.members);
        
        // Process warehouses - keep newProvinceCode and newWardCode as provided by user
        let processedWarehouses = body.warehouses;
        
        // Process representations - keep newProvinceCode and newWardCode in perAddress as provided by user
        let processedRepresentations = body.representations;
        
        await Promise.all([
            loanContractRepo.saveBranches(this.contractNumber, body.branches),
            loanContractRepo.insertLoanCustomerManagers(this.contractNumber, body.managers || []),
            sqlHelper.patchUpdate({
                table: 'loan_customer',
                columns: loanCustomerRepo.columns,
                values: sqlHelper.generateValues(utils.convertCamelToSnake(loanCustomerData), loanCustomerRepo.columns),
                conditions: {
                    contract_number: this.contractNumber
                }
            }),
            loanContractRepo.updateLoanCustomerRepresentations(this.contractNumber, body.registrationNumber, processedRepresentations),
            loanContractRepo.saveLoanCustomerWarehouses(this.contractNumber, processedWarehouses),
            sqlHelper.insertData(
                `loan_business_owner`,
                loanBusinessOwnerRepo.columns,
                sqlHelper.generateValues(utils.convertCamelToSnake(businessOwnerWrap), loanBusinessOwnerRepo.columns)
            ),
            loanContractRepo.saveLoanCustomerShareholders(this.contractNumber, preparingShareholders),
            loanContractRepo.insertLoanRevenues(this.contractNumber, body.financialInformation.revenues),
            loanContractRepo.insertLoanVatForms(this.contractNumber, body.financialInformation.vatForms),
            loanContractRepo.insertLoanCustomerPartners(this.contractNumber, PARTNER_TYPE.INT, body.financialInformation.inputPartners),
            loanContractRepo.insertLoanCustomerPartners(this.contractNumber, PARTNER_TYPE.OUT, body.financialInformation.outputPartners)
        ])

        this.saveDocuments(this.contractNumber, body);
        smeMisaService.scanDownloadDocuments(this.contractNumber, { req: this.req });

        this.checkAntiFraud(this.contractNumber);
        
        return this.res.status(200).json(new Response(
                ERROR_CODE.SUCCESS,
                `The application is received`,
                {
                    contractNumber: this.contractNumber,
                    status: STATUS.RECEIVEDTC2
                }
        ));
    }

    checkAntiFraud = async (contractNumber) => {
        try {
            const loan = await loanContractRepo.findOneMisaLoanContract(contractNumber);
            if (!loan?.id) {
                throw Error(`${contractNumber} | checkAntiFraud error: loan not found`);
            }

            let persons = loan.loan_customer_representations.map((rep) => ({
                idNumber: rep.id_number,
                otherIdNumber: '',
                fullName: rep.full_name,
                address: ''
            }));
            
            let eligiblePersons = [];
            let enterprises = [{
                registrationNumber: loan.loan_customer.registration_number,
                taxCode: loan.loan_customer.tax_id,
                companyName: loan.loan_customer.company_name,
                address: loan.loan_customer.address_on_license,
            }];
            if (loan.loan_business_owner.subject === 'INDIVIDUAL') {
                if (!this.isDuplicatePerson({ idNumber: loan.loan_business_owner.id_number }, persons)) {
                    persons.push({
                        idNumber: loan.loan_business_owner.id_number,
                        otherIdNumber: '',
                        fullName: loan.loan_business_owner.full_name,
                        address: ''
                    })
                    eligiblePersons.push({
                        customerName: loan.loan_business_owner.full_name,
                        idNumber: loan.loan_business_owner.id_number,
                        otherIdNumber: '',
                        issueDate: loan.loan_business_owner.issue_date,
                        issuePlace: loan.loan_business_owner.issue_place,
                        dateOfBirth: loan.loan_business_owner.dob,
                        gender: null,
                        phoneNumber: loan.loan_business_owner.phone_number
                    })
                }
                //đã kết hôn
                if (loan.loan_business_owner.married_status === 'M') {
                    // persons.push({
                    //     idNumber: loan.loan_business_owner.partner_id_number,
                    //     otherIdNumber: '',
                    //     fullName: loan.loan_business_owner.partner_full_name,
                    //     address: ''
                    // })
                    eligiblePersons.push({
                        customerName: loan.loan_business_owner.partner_full_name,
                        idNumber: loan.loan_business_owner.partner_id_number,
                        otherIdNumber: '',
                        issueDate: null,
                        issuePlace: null,
                        dateOfBirth: null,
                        gender: null,
                        phoneNumber: null
                    })
                }
            } else {
                enterprises.push({
                    registrationNumber: '',
                    taxCode: loan.loan_business_owner.tax_id,
                    companyName: loan.loan_business_owner.company_name,
                    address: '',
                });
            }
            if (loan.loan_customer_shareholders?.length > 0) {
                const shareholders = loan.loan_customer_shareholders;
                for (const shareholder of shareholders) {
                    if (shareholder.subject === 'INDIVIDUAL') {
                        if (!this.isDuplicatePerson({ idNumber: shareholder.id_number }, persons)) {
                            persons.push({
                                idNumber: shareholder.id_number,
                                otherIdNumber: '',
                                fullName: shareholder.full_name,
                                address: ''
                            })
                            eligiblePersons.push({
                                customerName: shareholder.full_name,
                                idNumber: shareholder.id_number,
                                otherIdNumber: '',
                                issueDate: shareholder.issue_date,
                                issuePlace: shareholder.issue_place,
                                dateOfBirth: null,
                                gender: null,
                                phoneNumber: shareholder.phone_number
                            })
                        }
                    } else {
                        if (!this.isDuplicateEnterprise({ taxCode: shareholder.tax_id }, enterprises)) {
                            enterprises.push({
                                registrationNumber: '',
                                taxCode: shareholder.tax_id,
                                companyName: shareholder.company_name,
                                address: '',
                            });
                        }
                    }
                }
            }

            const eligiblePayload = {
                requestId: uuid.v4(),
                legalRepresentative: {
                    customerName: loan.loan_customer_representations[0].full_name,
                    idNumber: loan.loan_customer_representations[0].id_number,
                    otherIdNumber: '',
                    issueDate: loan.loan_customer_representations[0].issue_date,
                    issuePlace: loan.loan_customer_representations[0].issue_place,
                    dateOfBirth: loan.loan_customer_representations[0].dob,
                    gender: null,
                    phoneNumber: loan.loan_customer_representations[0].phone_number,
                },
                otherPersons: eligiblePersons,
                productType: '',
                registrationNumber: loan.loan_customer.registration_number,
                taxCode: loan.sme_tax_id,
                companyName: loan.loan_customer.company_name,
                registrationDate: loan?.first_registration_date ?? loan.loan_customer?.registration_date,
                productCode: '',
                caseCreationTime: new Date(),
                partnerCode: loan.partner_code,
                channel: loan.channel,
                contractNumber: loan.contract_number,
                companyType: loan.loan_customer.business_type,
                options: {
                    isRenewCreditLine: loanContractService.isRefinance(loan),
                }
            }

            // Check Eligible_SME
            const eligibleResult = await antiFraudService.checkEligibleSmeV2(eligiblePayload)
            if (!eligibleResult?.decision) {
                await this.unlockOldContract(); // for MISA TC2
                throw new Error(`${contractNumber} | TC2 | check eligible error`)
            }
            await loggingRepo.saveWorkflow(MisaStep.CHECK_ELIGIBLE, eligibleResult.decision, contractNumber, 'system');
            if (eligibleResult?.decision && eligibleResult.decision != STATUS.ELIGIBLE) {
                const { decision } = eligibleResult || {};
                await loanContractRepo.updateContractStatus(decision, contractNumber);
                await this.unlockOldContract(); // for MISA TC2
                await smeMisaService.callbackCicResult({
                    step: smeMisaService.CIC_STEP_MISA_CALLBACK.TC2,
                    contractNumber,
                    isPass: false,
                    loanEffectTime: LMS_DATE()
                });
                return;
            }
            
            // Check CIC B11T
            const cicBody = {
                contractNumber: this.contractNumber,
                persons: persons,
                // enterprises: enterprises,
                stepCheck: CIC_STEP_CHECK.TC2
            }
            const cicResult = await antiFraudService.checkCicB11t(cicBody);
            if (!cicResult?.decision) {
                await this.unlockOldContract(); // for MISA TC2
                throw new Error(`${contractNumber} | TC2 | check checkCicB11t error`)
            }
            if (cicResult?.decision && cicResult.decision != STATUS.ELIGIBLE) {
                const { decision } = cicResult || {};
                await loanContractRepo.updateContractStatus(decision, contractNumber);
                await this.unlockOldContract(); // for MISA TC2
                await smeMisaService.callbackCicResult({
                    step: smeMisaService.CIC_STEP_MISA_CALLBACK.TC2,
                    contractNumber,
                    isPass: false,
                    loanEffectTime: cicResult?.nextTimeCanRequest ?? LMS_DATE()
                });
                return;
            }
            
            // Check dedup / create cust crm
            const createCustResult = await crmService.checkDedupEnterpriseV3(contractNumber);
            if (!createCustResult) {
                await this.unlockOldContract(); // for MISA TC2
                throw new Error(`${contractNumber} | TC2 | Create Customer error from CRM`);
            }

            // Check CIC S11A + CIC S10A
            await this.checkCicDetail(contractNumber);
        } catch (e) {
            console.error(`${contractNumber} | checkAntiFraud | error: `, e);
        }
    }

    checkCicDetail = async (contractNumber) => {
        const loan = await loanContractRepo.findOneMisaLoanContract(contractNumber);
        let persons = loan.loan_customer_representations.map((rep) => ({
            idNumber: rep.id_number,
            otherIdNumber: '',
            fullName: rep.full_name,
            address: ''
        }));
        let enterprises = [{
            registrationNumber: loan.loan_customer.registration_number,
            taxCode: loan.loan_customer.tax_id,
            companyName: loan.loan_customer.company_name,
            address: loan.loan_customer.address_on_license,
        }];
        if (loan.loan_business_owner.subject === 'INDIVIDUAL') {
            if (!this.isDuplicatePerson({ idNumber: loan.loan_business_owner.id_number }, persons)) {
                persons.push({
                    idNumber: loan.loan_business_owner.id_number,
                    otherIdNumber: '',
                    fullName: loan.loan_business_owner.full_name,
                    address: ''
                })
            }
            //đã kết hôn
            let businessTypeChecked = [BUSINESS_TYPE["01"], BUSINESS_TYPE["04"]];
            if (businessTypeChecked.includes(loan.business_type) && loan.loan_business_owner.married_status === 'M') {
                if (!loan.loan_business_owner.partner_id_number ||
                    !loan.loan_business_owner.partner_full_name) {
                    throw new Error(`checkCicDetail | ${contractNumber} | Thiếu thông tin vợ/ chồng`)
                }
                persons.push({
                    idNumber: loan.loan_business_owner.partner_id_number,
                    otherIdNumber: '',
                    fullName: loan.loan_business_owner.partner_full_name,
                    address: ''
                })
            }
        } else {
            enterprises.push({
                registrationNumber: '',
                taxCode: loan.loan_business_owner.tax_id,
                companyName: loan.loan_business_owner.company_name,
                address: '',
            });
        }
        if (loan.loan_customer_shareholders?.length > 0) {
            const shareholders = loan.loan_customer_shareholders;
            for (const shareholder of shareholders) {
                if (shareholder.subject === 'INDIVIDUAL') {
                    if (!this.isDuplicatePerson({ idNumber: shareholder.id_number }, persons)) {
                        persons.push({
                            idNumber: shareholder.id_number,
                            otherIdNumber: '',
                            fullName: shareholder.full_name,
                            address: ''
                        })
                    }
                } else {
                    if (!this.isDuplicateEnterprise({ taxCode: shareholder.tax_id }, enterprises)) {
                        enterprises.push({
                            registrationNumber: '',
                            taxCode: shareholder.tax_id,
                            companyName: shareholder.company_name,
                            address: '',
                        });
                    }
                }
            }
        }

        // Check CIC S11A + CIC S10A
        // for MISA TC2
        const cicResult = await antiFraudService.checkCicDetailsSme({
            contractNumber,
            custId: loan.cust_id,
            persons: persons,
            enterprises: enterprises,
            contractType: 'LIMIT',
            taxCode: loan?.sme_tax_id,
            stepCheck: CIC_STEP_CHECK.TC2_DETAIL,
            partnerCode: loan?.partner_code,
        })
        return cicResult
    }

    unlockOldContract = async () => {
        await loanContractRepo.updateContractLockStatus(LOCK_STATUS.ACTIVE, this.oldContractNumber);
    }
}

module.exports = {
    kovA2,
    MisaA2,
    McAppA2,
    SuperAppA2,
    MisaAf2,
    finvA2,
    MisaTc2,
}