const sqlHelper = require("../utils/sqlHelper");
const documentRepo = require('./document.js');
const { DOCUMENT_REFERENCE_TABLE, TABLE } = require('../const/variables-const.js');

// 1. T<PERSON>o hóa đơn mới
async function createInvoice(invoice) {
    const {
        debt_contract_number,
        invoice_order,
        invoice_number,
        invoice_date,
        invoice_amount,
        payment_date,
        note,
        is_invoice_valid,
        purchaser_tax_code
    } = invoice;
        const pool = global.poolWrite;
    const result = await pool.query(
        `INSERT INTO invoices 
        (debt_contract_number, invoice_order, invoice_number, invoice_date, invoice_amount, payment_date, note, is_invoice_valid, purchaser_tax_code)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)
        RETURNING *`,
        [debt_contract_number, invoice_order, invoice_number, invoice_date, invoice_amount, payment_date, note, is_invoice_valid, purchaser_tax_code]
    );
    return result.rows[0];
}

// 2. <PERSON><PERSON><PERSON> t<PERSON> cả hóa đơn theo debt_contract_number
async function getInvoicesByKunn(debt_contract_number) {
    const pool = global.poolWrite;
    const result = await pool.query(
        'SELECT * FROM invoices WHERE debt_contract_number = $1 and is_deleted = 0 ORDER BY payment_date, invoice_order',
        [debt_contract_number]
    );
    return result.rows;
}

async function getInvoicesRelatedToKunn (debtContractNumber) {
    const pool = global.poolWrite;
    const result = await pool.query(
      'SELECT * FROM invoices WHERE debt_contract_number = $1 and is_deleted = 0 ORDER BY payment_date, invoice_order',
      [debt_contract_number]
    );
    return result.rows;
}

// 3. Lấy hóa đơn theo id
async function getInvoiceById(invoice_id) {
    const pool = global.poolWrite;
    const result = await pool.query(
        'SELECT * FROM invoices WHERE id = $1 and is_deleted = 0',
        [invoice_id]
    );
    return result.rows[0];
}

// 4. Update hóa đơn
async function updateInvoice(invoice_id, data) {
    const fields = [];
    const values = [];
    let idx = 1;

    for (const [key, value] of Object.entries(data)) {
        fields.push(`${key} = $${idx++}`);
        values.push(value);
    }
    values.push(invoice_id);
    const sql = `
        UPDATE invoices
        SET ${fields.join(', ')}, updated_at = now()
        WHERE id = $${idx}
        RETURNING *;
    `;
    const pool = global.poolWrite;
    const result = await pool.query(sql, values);
    return result.rows[0];
}

// 5. Xóa hóa đơn
async function deleteInvoice(invoice_id) {
    const pool = global.poolWrite;
    await pool.query('update invoices set is_deleted = 1 WHERE id = $1', [invoice_id]);
    return true;
}
async function deleteInvoiceByNumber(invoiceNumber, kunnId) {
    const pool = global.poolWrite;
    await pool.query('update invoices set is_deleted = 1, is_resubmit = true WHERE debt_contract_number = $1 and invoice_number = $2', [kunnId, invoiceNumber]);
    return true;
}

// 6. Thêm nhiều hóa đơn
async function createMultipleInvoices(invoices) {
    const results = await Promise.all(invoices.map(createInvoice));
    return results;
}

async function createMultipleInvoicesWithDocument(invoices) {
    for (const invoice of invoices) {
        const createdInvoice = await createInvoice(invoice);
        const docIds = invoice.files.map(file => file.doc_id);
        const documents = await documentRepo.getDocumentsByDocIdsWhereNoContract(docIds);
        if (invoice.files && invoice.files.length > 0) {
            let insertPromises = [];
            let updatePromises = [];
            for (const file of invoice.files) {
                const doc = documents.find(doc => doc.doc_id === file.doc_id);
                const document = {
                    invoiceId: createdInvoice.id,
                    debtContractNumber: invoice.debt_contract_number,
                    docType: file.doc_type,
                    docId: file.doc_id,
                    docGroup: file.doc_group,
                    fileKey: doc.file_key,
                    fileName: doc.file_name
                };
                insertPromises.push(insertInvoiceDocument(document));
                updatePromises.push(documentRepo.updateReference({docId: file.doc_id, docType: file.doc_type, referenceId: createdInvoice.id, referenceTable: DOCUMENT_REFERENCE_TABLE.INVOICE}));
            }
            if (insertPromises.length > 0) {
                await Promise.all(insertPromises);
                await Promise.all(updatePromises);
            }
        }
    }
}

async function getInvoiceDocumentByInvoiceId(invoice_id) {
    const pool = global.poolWrite;
    const result = await pool.query(
        'SELECT * FROM invoice_document WHERE invoice_id = $1 and is_deleted = 0',
        [invoice_id]
    );
    return result.rows;
}

async function getInvoiceDocumentByKunn(debt_contract_number) {
    const pool = global.poolWrite;
    const sql = `
        SELECT *
        FROM invoice_document
        WHERE debt_contract_number = $1 AND is_deleted = 0
    `;
    const result = await pool.query(sql, [debt_contract_number]);
    return result.rows;
}

const insertInvoiceDocument = async ({ invoiceId, debtContractNumber, docType, docId, docGroup, fileKey, fileName }) => {
    try {
        const poolWrite = global.poolWrite
        const sql = `insert into invoice_document (invoice_id, doc_type, doc_id, doc_group, file_key, file_name, debt_contract_number ) values ($1,$2,$3,$4,$5,$6,$7)`;
        const rs = await poolWrite.query(sql, [invoiceId, docType, docId, docGroup, fileKey, fileName, debtContractNumber]);
        return rs?.rowCount == 0 ? false : true;
    } catch (err) {
        console.log(err)
        return false
    }
}

const getInvoicesWithDocuments = async (debtContractNumber) => {
    const [invoices, documents] = await Promise.all([
        getInvoicesByKunn(debtContractNumber),
        getInvoiceDocumentByKunn(debtContractNumber)
    ]);
    return invoices.map(invoice => {
        const invoiceDocuments = documents.filter(doc => doc.invoice_id === invoice.id);
        return {
            ...invoice,
            files: invoiceDocuments
        };
    });
};

const getInvoiceWaitingResubmit = async (debtContractNumber) => {
    return await sqlHelper.getData({
        table: TABLE.INVOICES,
        where: {
            debt_contract_number: debtContractNumber,
            is_deleted: 0,
            waiting_resubmit: true
        }
    });
}

async function saveInvoiceDocumentChecked({ invoiceId, docType, docId, status, comment, updatedBy }) {
  try {
    const waitingResubmit = status ? 0 : 1;
    const isSsChecked = 1;
    const isChecked = status ? 1 : 0;
    const poolWrite = global.poolWrite;
    const sql = `
      UPDATE invoice_document
      SET
        waiting_resubmit = $1,
        comment = $2,
        updated_by = $3,
        is_ss_checked = $4,
        is_checked = $5,
        updated_date = now()
      WHERE invoice_id = $6
        AND doc_type = $7
        AND doc_id = $8
        AND is_deleted = 0
      RETURNING *
    `;
    const params = [waitingResubmit, comment, updatedBy, isSsChecked, isChecked, invoiceId, docType, docId];
    const rs = await poolWrite.query(sql, params);
    if (rs.rowCount === 0) {
      common.log(`saveInvoiceDocumentChecked error for doc_id: ${docId}`);
      return false;
    }
    return rs.rows[0];
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function updateDocument(id, data) {
    const fields = [];
    const values = [];
    let idx = 1;

    for (const [key, value] of Object.entries(data)) {
        fields.push(`${key} = $${idx++}`);
        values.push(value);
    }
    values.push(id);
    const sql = `
        UPDATE invoice_document
        SET ${fields.join(', ')}, updated_at = now()
        WHERE id = $${idx}
        RETURNING *;
    `;
    const pool = global.poolWrite;
    const result = await pool.query(sql, values);
    return result.rows[0];
}

module.exports = {
    createInvoice,
    createMultipleInvoices,
    getInvoicesByKunn,
    getInvoiceById,
    updateInvoice,
    deleteInvoice,
    getInvoiceDocumentByInvoiceId,
    insertInvoiceDocument,
    createMultipleInvoicesWithDocument,
    getInvoiceDocumentByKunn,
    getInvoicesWithDocuments,
    deleteInvoiceByNumber,
    getInvoiceWaitingResubmit,
    saveInvoiceDocumentChecked,
    updateDocument
};
