const { convertCamelToSnake } = require("../utils/helper");
const { insertData, generateValues } = require("../utils/sqlHelper");

const columns = [
  "financial_statements_export_id",
  "num_of_first_year",
  "num_of_second_year",
  "code",
  "name",
  "note",
];
const TABLE_NAME = "financial_statement_details";
const LOG_PREFIX = `[DB][financial_statement_details-repo]`;
const save = async (data) => {
  try {
    data = convertCamelToSnake(data);
    return await insertData(TABLE_NAME, columns, generateValues(data, columns));
  } catch (error) {
    console.log(`${LOG_PREFIX} save ${JSON.stringify(data)}, error: ${error}}`);
  }
};

const getRevenue = async ({ contractNumber, }) => {
  try {
    const pool = global.poolWrite;
    const result = await pool.query(
      `select 
        * 
      from 
        financial_statement_details fsd 
      where 
        financial_statements_export_id  in (
          select 
            id 
          from 
            financial_statements_export fse 
          where 
            contract_number = $1 
            and "template" in ('TT200_KQHDKD', 'TT133_HDKD_B02','TT133_HDKD_B02','TT133_HDKD_B02'))
        and code = '01'
      ;`,
      [contractNumber]
    );
    return result.rows[0] ?? {};
  } catch (error) {
    console.log(`${LOG_PREFIX} getRevenue ${JSON.stringify({ contractNumber })}, error: ${error}}`);
    return null;
  }
};

const getFinancialStatementsExport = async ({ contractNumber }) => {
  try {
    const pool = global.poolWrite;
    const result = await pool.query(
      `select * from  financial_statements_export fse  where contract_number = $1 and "template" in ('TT200_KQHDKD', 'TT133_BCTC_B01');`,
      [contractNumber]
    );
    return result.rows[0] ?? {};
  } catch (error) {
    console.log(`${LOG_PREFIX} getRevenue ${JSON.stringify({ contractNumber })}, error: ${error}}`);
    return null;
  }
};

const getRevenueByTaxFinancialReport = async (id, code) => {
  try {
    const pool = global.poolWrite;
    const result = await pool.query(`select  *  from financial_statement_details fsd  where financial_statements_export_id  = $1 and code = $2;`, [id, code]);
    return result.rows[0] ?? {};
  } catch (error) {
    console.log(`${LOG_PREFIX} getRevenue ${JSON.stringify({ contractNumber })}, error: ${error}}`);
    return null;
  }
};

const softRemoveFinancialStatementDetails = async (financialStatementIds) => {
  if (!financialStatementIds || !Array.isArray(financialStatementIds) || financialStatementIds.length === 0) {
    return 0;
  }
  try {
    const poolWrite = global.poolWrite;
    const sql = `
    UPDATE 
      financial_statement_details 
    SET 
      is_deleted = 1 
    WHERE 
      financial_statements_export_id = ANY($1::int[])`;
    const result = await poolWrite.query(sql, [financialStatementIds]);
    return result.rowCount || 0;
  } catch (e) {
    console.error(e);
    return 0;
  }
};

module.exports = {
  save,
  getRevenue,
  getFinancialStatementsExport,
  getRevenueByTaxFinancialReport,
  softRemoveFinancialStatementDetails
};
