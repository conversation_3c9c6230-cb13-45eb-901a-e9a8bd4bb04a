const { loanContractMapping } = require("./param-column-mapping");
const common = require("../utils/common");
const { STATUS } = require("../const/caseStatus");
const { isNullOrEmpty, formatDate, snakeToCamel, convertCamelToSnake } = require("../utils/helper");
const dateHelper = require("../utils/dateHelper");
const moment = require("moment-timezone");
const { PARTNER_CODE } = require("../const/definition");
moment().tz("Asia/Ho_Chi_Minh").format();

const TABLE_NAME = "";

const pgp = require("pg-promise")({
  capSQL: true,
});

function generateSql(loanContractMapping, body) {
  let fieldQuery = "";
  let paramQuery = " values(";
  let params = [];
  let paramIdx = 1;
  for (let key of Object.keys(loanContractMapping)) {
    if (loanContractMapping[key] !== "null" && loanContractMapping[key] !== "" && body[key] !== "" && body[key] != undefined) {
      fieldQuery += loanContractMapping[key] + ",";
      paramQuery += "$" + paramIdx + ",";
      params.push(body[key]);
      paramIdx += 1;
    }
  }
  fieldQuery = fieldQuery.slice(0, -1) + ")";
  paramQuery = paramQuery.slice(0, -1) + ")";
  const mappingQuery = fieldQuery + paramQuery;
  return {
    mappingQuery,
    params,
  };
}

function generateUpdateQuery(loanContractMapping, body) {
  let basicQuery = "update loan_contract set ";
  const params = [];
  let paramIdx = 1;
  for (let key of Object.keys(loanContractMapping)) {
    if (loanContractMapping[key] !== "null" && loanContractMapping[key] !== "" && body.hasOwnProperty(key) && body[key] !== "" && body[key] != undefined) {
      basicQuery += loanContractMapping[key] + "=$" + paramIdx + ",";
      params.push(body[key]);
      paramIdx += 1;
    }
  }
  basicQuery = basicQuery.slice(0, -1);
  const whereQuery = " where contract_number = $" + paramIdx + " RETURNING *";
  const query = basicQuery + whereQuery;
  // console.log('qr',query)
  params.push(body.contract_number);
  return {
    query,
    params,
  };
}

async function insertLoanContract(body, client = null) {
  try {
    const poolWrite = client || global.poolWrite;
    let basicQuery = "insert into loan_contract(";
    const { mappingQuery, params } = generateSql(loanContractMapping, body);
    const insertSql = basicQuery + mappingQuery;
    const insertRs = await poolWrite.query(insertSql, params);
    if (insertRs.rowCount > 0) {
      return true;
    }
    return false;
  } catch (err) {
    common.log(err.message);
    return false;
  }
}

async function updateLoanContract(body) {
  try {
    const poolWrite = global.poolWrite;

    const { query, params } = generateUpdateQuery(loanContractMapping, body);
    const insertRs = await poolWrite.query(query, params);

    if (insertRs.rowCount > 0) {
      return true;
    }
    return false;
  } catch (err) {
    common.log(err);
    return false;
  }
}

async function updateKUStatus(status, kunnNumber) {
  try {
    const poolWrite = global.poolWrite;
    const updateContractSql = "update kunn set status=$1,updated_date=now() where kunn_id = $2";
    const updateRs = await poolWrite.query(updateContractSql, [status, kunnNumber]);
    if (updateRs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log(err, kunnNumber);
    return false;
  }
}

async function updateContractStatus(status, contractNumber) {
  try {
    // console.log(
    //     {status:status,contractNumber:contractNumber}
    // )
    const poolWrite = global.poolWrite;
    const updateContractSql = "update loan_contract set status=$1,updated_date=now() where contract_number = $2";
    const updateRs = await poolWrite.query(updateContractSql, [status, contractNumber]);
    if (updateRs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log("update loan status error ", contractNumber);
    return false;
  }
}
async function updateAndGetContractStatus(status, contractNumber) {
  try {
    // console.log(
    //     {status:status,contractNumber:contractNumber}
    // )
    const poolWrite = global.poolWrite;
    // const updateContractSql = "update loan_contract set status=$1,updated_date=now() where contract_number = $2";
    const updateContractSql = "update loan_contract set status=$1,updated_date=now() where contract_number = $2 RETURNING status";
    const updateRs = await poolWrite.query(updateContractSql, [status, contractNumber]);
    if (updateRs.rowCount == 0) {
      return false;
    }
    return updateRs.rows[0];
  } catch (err) {
    common.log("update loan status error ", contractNumber);
    return false;
  }
}

async function updateActiveContract(contractNumber, status, startDate, endDate) {
  try {
    const poolWrite = global.poolWrite;
    const updateActiveSql = "update loan_contract set status = $1,start_date = $2,end_date = $3,updated_date=now() where contract_number=$4";
    const updateRs = await poolWrite.query(updateActiveSql, [status, startDate, endDate, contractNumber]);
    if (updateRs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    console.log(err);
    common.log(`update loan activated error : ${err.message}`, contractNumber);
    return false;
  }
}

async function updateInactiveContract(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const updateActiveSql = "update loan_contract set status = $1,inactivated_at=now(),updated_date=now() where contract_number=$2";
    const updateRs = await poolWrite.query(updateActiveSql, [STATUS.INACTIVATED, contractNumber]);
    if (updateRs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    console.log(err);
    common.log(`update loan inactivated error : ${err.message}`, contractNumber);
    return false;
  }
}

async function updateContractLockStatus(lockStatus, contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const updateContractSql = "update loan_contract set is_blocked=$1,updated_date=now() where contract_number = $2";
    const updateRs = await poolWrite.query(updateContractSql, [lockStatus, contractNumber]);
    if (updateRs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log("update loan_contract error ", contractNumber);
    return false;
  }
}

async function updateCustId(custId, contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const updateContractSql = "update loan_contract set cust_id=$1,updated_date=now() where contract_number = $2";
    const updateRs = await poolWrite.query(updateContractSql, [custId, contractNumber]);
    if (updateRs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log("update customer id error ", contractNumber);
    return false;
  }
}

async function getLoanContract(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract where contract_number = $1";

    const result = await poolWrite.query(sql, [contractNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("get loan contract info error ", contractNumber);
    return false;
  }
}

async function getLoanContractByIdNumber(idNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract where id_number = $1";
    const result = await poolWrite.query(sql, [idNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("getLoanContractByIdNumber error ", idNumber);
    return false;
  }
}

async function getLoanContractWithSmeInfo(payload) {
  try {
    const poolWrite = global.poolWrite;
    let sql = "select * from loan_contract where 1 = 1";
    let idx = 1;
    const params = [];
    if (payload?.contractNumber) {
      sql += " and contract_number = $" + idx;
      params.push(payload.contractNumber);
      idx++;
    }
    if (payload?.taxId) {
      sql += " and sme_tax_id = $" + idx;
      params.push(payload.taxId);
      idx++;
    }
    if (payload?.channel) {
      sql += " and channel = $" + idx;
      params.push(payload.channel);
      idx++;
    }
    if (payload?.partnerCode) {
      sql += " and partner_code = $" + idx;
      params.push(payload.partnerCode);
      idx++;
    }
    if (payload?.contractType) {
      sql += " and contract_type = $" + idx;
      params.push(payload.contractType);
      idx++;
    }
    sql += " limit 1";
    const result = await poolWrite.query(sql, params);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("getLoanContractByCustomerInfo error ", err);
    return false;
  }
}

async function getLoanContractRefinance(contractNumber, taxId, channel, partnerCode, contractType) {
  try {
    const poolWrite = global.poolWrite;
    let sql = `
        select 
            * 
        from 
            loan_contract lc 
        where 
            contract_number = $1 and sme_tax_id = $2 and status = $3
            and channel = $4 and partner_code = $5 and contract_type = $6
        `;
    const result = await poolWrite.query(sql, [contractNumber, taxId, STATUS.ACTIVATED, channel, partnerCode, contractType]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("getLoanContractRefinance error ", [contractNumber, taxId]);
    return false;
  }
}

async function getLoanContracts(contractNumbers) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract where contract_number = any($1)";
    const result = await poolWrite.query(sql, [contractNumbers]);
    if (result.rowCount == 0) {
      return [];
    }
    return result.rows || [];
  } catch (err) {
    common.log("get loan contract info error ", contractNumber);
    return [];
  }
}

async function getLoanContractByRequestId(requestId) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract where request_id = $1";
    const result = await poolWrite.query(sql, [requestId]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("get loan contract info error ", requestId);
    return false;
  }
}

async function getLoanContractV2(thirdPartyCustId, contractNumber = undefined) {
  try {
    const poolWrite = global.poolWrite;
    let sql = `select * from loan_contract where third_party_cust_id = $1`;
    let params = [thirdPartyCustId];
    if (contractNumber != undefined) {
      sql = `select * from loan_contract where contract_number = $1`;
      params = [contractNumber];
    }
    // console.log(sql,params)
    const result = await poolWrite.query(sql, params);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows;
  } catch (err) {
    common.log("get loan contract info error ", contractNumber);
    return false;
  }
}

async function getLoanContractJoinLoanScore(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract lc join loan_main_score ls on lc.contract_number = ls.contract_number where lc.contract_number = $1;";
    const result = await poolWrite.query(sql, [contractNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("get loan contract join loan_score info error ", contractNumber);
    return false;
  }
}

async function getContractStaus(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select status from loan_contract where contract_number = $1";
    const result = await poolWrite.query(sql, [contractNumber]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0].status;
  } catch (err) {
    common.log("get contract status error ", contractNumber);
    return false;
  }
}

async function getLoanContractJoinMainScore(poolWrtie, contractnumber) {
  try {
    let sql = `SELECT * FROM loan_contract lc left JOIN loan_main_score lms ON lc.contract_number = lms.contract_number WHERE lc.contract_number = $1 limit 1`;
    const data = await poolWrtie.query(sql, [contractnumber]);
    if (data.rowCount == 0) {
      return false;
    }
    return data.rows[0];
  } catch (err) {
    common.log(`get loan contract join main score error : ${err.message}`, contractnumber);
    return false;
  }
}

async function getPartnerCodeAndProductCode(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select partner_code ,product_code from loan_contract where contract_number = $1";
    const data = await poolWrite.query(sql, [contractNumber]);
    if (data.rowCount == 0) {
      return false;
    }
    return data.rows[0];
  } catch (err) {
    common.log("get partner code and product code error ", contractNumber);
    return false;
  }
}

async function getPartnerCode(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select partner_code  from loan_contract where contract_number = $1";
    const data = await poolWrite.query(sql, [contractNumber]);
    if (data.rowCount == 0) {
      return false;
    }
    return data.rows[0].partner_code;
  } catch (err) {
    common.log("get partner code and product code error ", contractNumber);
    return false;
  }
}

async function isValidLmsCallback(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select status from loan_contract where contract_number = $1";
    const data = await poolWrite.query(sql, [contractNumber]);
    if (data.rowCount == 0 || data.rows[0].status !== STATUS.SIGNED_TO_BE_DISBURED) {
      return false;
    }
    return true;
  } catch (err) {
    common.log(`valid contract Number error`, contractNumber);
    return false;
  }
}

async function saveBranchAddress(contractNumber, branchAddressList) {
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "branch_name", "province", "district", "ward", "address", "status_owned", "num_of_staff"], { table: "loan_branch_address" });
  let insertData = [];
  branchAddressList.forEach((branchAddres) => {
    insertData.push({
      contract_number: contractNumber,
      branch_name: branchAddres.name,
      province: branchAddres.province,
      district: branchAddres.district,
      ward: branchAddres.ward,
      address: branchAddres.detail,
      status_owned: branchAddres.statusOwned || "",
      num_of_staff: branchAddres.numOfStaff || 0,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_branch_address: error", contractNumber);
    });
}

async function getBranchAddresses(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_branch_address where contract_number = $1 order by id";
    const data = await poolWrite.query(sql, [contractNumber]);
    if (data.rowCount == 0) {
      return [];
    }
    return data.rows;
  } catch (err) {
    common.log("get branch address error ", contractNumber);
    return [];
  }
}

function saveLoanAcountTrading(contractNumber, loanAcountTradingArray) {
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "platform_name", "store_name"], { table: "loan_account_trading" });
  let insertData = [];
  loanAcountTradingArray.forEach((account) => {
    insertData.push({
      contract_number: contractNumber,
      platform_name: account.platformName,
      store_name: account.storeName,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => console.log(error));
}

async function updateFieldLoanContract(contractNumber, column, value, client = null) {
  try {
    const poolWrite = client || global.poolWrite;
    const updateSql = `update loan_contract set ${column} =$1,updated_date=now() where contract_number = $2`;
    const rs = await poolWrite.query(updateSql, [value, contractNumber]);
    if (rs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log(`update loan contract error : ${err.message}`, contractNumber);
    return false;
  }
}

async function getRemainInfo(contractNumber) {
  try {
    const poolRead = global.poolRead;
    const sql = `
        select 
        lc.cust_full_name, 
        lc.contract_Type,
        lc.bill_day,
        lc.approval_amt, 
        sum(k.with_draw_amount) as loan_amt,
        case when sum(k.with_draw_amount) is not null then lc.approval_amt - sum(k.with_draw_amount) else lc.approval_amt end as remain_amt, 
        lc.approval_date, 
        lc.end_date, 
        lc.approval_int_rate 
        from loan_contract lc left join kunn k 
        on lc.contract_number = k.contract_number 
        where lc.contract_number = $1
        group by lc.cust_full_name,lc.bill_day, lc.approval_amt, lc.approval_amt, lc.approval_date, lc.end_date, lc.approval_int_rate, lc.contract_Type
        `;
    const body = {
      contractNumber: contractNumber,
    };
    const data = await poolRead.query(sql, [contractNumber]);
    const checkAvailableUrl = global.config.basic.lmsMc[config.env] + global.config.data.lms.checkAvailable;
    const availableRs = await common.postApiV2(checkAvailableUrl, body);
    const availableAmount = availableRs?.data?.data?.avalibleAmount;
    const loan_amt = parseFloat(data?.rows[0]?.approval_amt) - parseFloat(availableAmount);
    data.rows[0].approval_amt = parseInt(data.rows[0].approval_amt).toString();
    data.rows[0].loan_amt = parseInt(loan_amt).toString();
    data.rows[0].remain_amt = parseInt(availableAmount).toString();
    if (data.rowCount == 0) {
      return false;
    }
    return data.rows[0];
  } catch (err) {
    common.log("get remaining info error ", contractNumber);
    return false;
  }
}
async function getRemainInfoHanMuc(contractNumber) {
  try {
    const poolRead = global.poolRead;
    const sql = `
            select 
            lc.cust_full_name, 
            lc.contract_Type,
            lc.approval_amt, 
            sum(k.with_draw_amount) as loan_amt,
            case when sum(k.with_draw_amount) is not null then lc.approval_amt - sum(k.with_draw_amount) else lc.approval_amt end as remain_amt, 
            lc.approval_date, 
            lc.end_date, 
            lc.approval_int_rate 
            from loan_contract lc left join kunn k 
            on lc.contract_number = k.contract_number 
            where lc.contract_number = $1 and k.status = 'KKH13'
            group by lc.cust_full_name, lc.approval_amt, lc.approval_amt, lc.approval_date, lc.end_date, lc.approval_int_rate, lc.contract_Type
        `;
    const data = await poolRead.query(sql, [contractNumber]);
    if (data.rowCount == 0) {
      return false;
    }
    return data.rows[0];
  } catch (err) {
    common.log("get remaining info error ", contractNumber);
    return false;
  }
}
async function validSigned(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `select * from loan_esigning le where status ='SIGNED' and contract_number= $1`;
    const rs = await poolWrite.query(sql, [contractNumber]);
    if (rs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function getLastStepResubmit(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select role from loan_manual_decision where contract_number = $1 and result_chk='RESUBMIT' order by created_date desc";
    const rs = await poolWrite.query(sql, [contractNumber]);
    if (rs.rowCount == 0) {
      return false;
    }
    return rs.rows[0].role;
  } catch (err) {
    return false;
  }
}

async function isValidMerchantAccountVTP(merchantAccount) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `select count(*) from loan_contract lc where merchant_account = $1 and partner_code ='VTP' and status != $2 ;`;
    const rs = await poolWrite.query(sql, [merchantAccount, STATUS.CANCELLED]);
    if (parseInt(rs.rows[0].count) == 0) {
      return true;
    }
    return false;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function genContractNumber() {
  const sql = "SELECT nextval('contract_generate_number')";
  const result = await global.poolWrite.query(sql);
  return result.rows[0].nextval;
}

async function checkIsKU(contractNumber) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select 1 from kunn where kunn_id = $1";
    const rs = await poolWrite.query(sql, [contractNumber]);
    if (rs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    return false;
  }
}

async function getLoanContractByPartner(contractNumber, partnerCode) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract where contract_number = $1 and partner_code = $2";
    const result = await poolWrite.query(sql, [contractNumber, partnerCode]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("get loan contract info error ", contractNumber);
    return false;
  }
}

async function getLoanContractByCustId(custId) {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_contract where cust_id = $1";
    const result = await poolWrite.query(sql, [custId]);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("get loan contract info error ", custId);
    return false;
  }
}

async function getLoanContractBySmeInfo(poolRead, payload) {
  let sql = "select * from loan_contract where 1 = 1";
  let idx = 1;
  const params = [];
  if (payload.smePhoneNumber != undefined) {
    sql += " and sme_phone_number = $" + idx;
    params.push(payload.smePhoneNumber);
    idx++;
  }
  if (payload.smeTaxId != undefined) {
    sql += " and sme_tax_id = $" + idx;
    params.push(payload.smeTaxId);
    idx++;
  }
  if (payload.contractType != undefined) {
    sql += " and contract_type = $" + idx;
    params.push(payload.contractType);
    idx++;
  }
  if (payload.fromDate != undefined && payload.toDate != undefined) {
    sql += ` and created_date::date between $${idx}`;
    params.push(payload.fromDate);
    idx++;
    sql += ` and $${idx}`;
    params.push(payload.toDate);
    idx++;
  }
  return await poolRead.query(sql, params);
}

async function checkGenerateContractReview(contractNumber) {
  const loanContractData = await getLoanContract(contractNumber);
  const rootLoanContractData = await getLoanContract(loanContractData?.root_contract_number);
  const rootLoan = {
    approval_amt: rootLoanContractData?.approval_amt,
    approval_tenor: rootLoanContractData?.approval_tenor,
    id_number: rootLoanContractData?.id_number,
    issue_date: isNullOrEmpty(rootLoanContractData?.issue_date) ? dateHelper.formatDate(rootLoanContractData?.issue_date, "DD/MM/YYYY") : undefined,
    issue_place: rootLoanContractData?.issue_place,
    other_id_number: rootLoanContractData?.other_id_number,
    other_issue_date: isNullOrEmpty(rootLoanContractData?.other_issue_date) ? dateHelper.formatDate(rootLoanContractData?.other_issue_date, "DD/MM/YYYY") : undefined,
    other_issue_place: rootLoanContractData?.other_issue_place,
  };
  const reviewLoan = {
    approval_amt: loanContractData?.approval_amt,
    approval_tenor: loanContractData?.approval_tenor,
    id_number: loanContractData?.id_number,
    issue_date: isNullOrEmpty(loanContractData?.issue_date) ? dateHelper.formatDate(loanContractData?.issue_date, "DD/MM/YYYY") : undefined,
    issue_place: loanContractData?.issue_place,
  };

  if (!isNullOrEmpty(rootLoanContractData)) {
    if (rootLoan?.approval_amt === reviewLoan?.approval_amt && rootLoan?.approval_tenor === reviewLoan?.approval_tenor && ((rootLoan?.id_number === reviewLoan?.id_number && rootLoan?.issue_place === reviewLoan?.issue_place && rootLoan?.issue_date === reviewLoan?.issue_date) || (rootLoan?.other_id_number === reviewLoan?.id_number && rootLoan?.other_issue_place === reviewLoan?.issue_place && rootLoan?.other_issue_date === reviewLoan?.issue_date))) {
      return false;
    } else {
      return true;
    }
  }
  return true;
}

const findByPhoneNumberList = async function ({ phoneList }) {
  let params = [];
  try {
    let phoneListStr = phoneList.toString();
    let phoneListParam = `{${phoneListStr}}`;
    let date = new Date();
    date.setFullYear(date.getFullYear() - 1);
    let dateAfterFormat = moment(date).format("YYYY-MM-DD HH:mm:ss");
    const query = ` SELECT  *  FROM  loan_contract lc 
                        where  created_date > $1 and (phone_number1 = any($2)
                                or reference_phone_1 = any($2)
                                or reference_phone_2 = any($2))
                        order by created_date desc;`;
    params = [dateAfterFormat, phoneListParam];
    const result = await global.poolWrite.query(query, params);
    return result?.rowCount > 0 ? result.rows : undefined;
  } catch (error) {
    console.log(`EXCEPTION | function: findByPhoneNumberList | Params: ${JSON.stringify(params)} | error: ${error.message}`);
    console.log(error);
    return undefined;
  }
};

const findByContractList = async function ({ contractList }) {
  let params = [];
  try {
    let contractListStr = contractList.toString();
    let query = `select * from loan_contract where contract_number = ANY($1)`;
    let contractListParam = `{${contractListStr}}`;
    params = [contractListParam];
    const result = await global.poolWrite.query(query, params);
    return result?.rowCount > 0 ? result.rows : undefined;
  } catch (error) {
    console.log(`EXCEPTION | function: findByContractList | Params: ${JSON.stringify(params)} | error: ${error.message}`);
    console.log(error);
    return undefined;
  }
};

async function getAllContractData(contractNumber) {
  //console.log({contractNumber})
  const sql = "select * from loan_contract where contract_number=$1";
  const queryRs = await global.poolRead.query(sql, [contractNumber]);
  const contractData = queryRs.rows[0];
  return contractData;
}

async function getAllDataByContract(contractNumber) {
  //console.log({contractNumber})
  const sql = "select * from loan_contract where contract_number=$1";
  const queryRs = await global.poolRead.query(sql, [contractNumber]);
  const contractData = queryRs.rows[0];
  return contractData;
}

async function updateBillDay(contractNumber, billDay) {
  const currentDate = new Date();
  const sql = "update loan_contract set bill_day = $1,updated_date=$2 where contract_number=$3";
  global.poolRead
    .query(sql, [billDay, currentDate, contractNumber])
    .then()
    .catch((err) => console.log(err));
}

const getCancelEkycLoan = async function () {
  try {
    const query = `
            select lc.phone_number1 from step_check_log scl
            join loan_contract lc on scl.contract_number = lc.contract_number
            where step = 'CHECK_EKYC' and lc.phone_number1 is not null
            and lc.partner_code = $1 and body_output not like '%status":200%' or body_output like '%REJECT_EKYC%'
        `;
    const result = await global.poolRead.query(query, [PARTNER_CODE.SMA]);
    let finalRs = [];
    if (Array.isArray(result?.rows) && result?.rows.length > 0) {
      result.rows.map((x) => finalRs.push(x.phone_number1));
    }
    return finalRs;
  } catch (error) {
    console.log(`EXCEPTION | function: getCancelEkycLoan | error: ${error.message}`);
    console.log(error);
    return false;
  }
};

const getNotYetDecidedLoanByTime = async function (time) {
  try {
    time = parseFloat(time);
    const query = `
            select phone_number1 from loan_contract lc where EXTRACT(EPOCH from now() - lc.updated_date) >= $1
            and status not in ('ACTIVATED','REFUSED','CANCELLED','NOT_ELIGIBLE') and phone_number1 is not null and partner_code = $2
        `;
    const result = await global.poolRead.query(query, [time, PARTNER_CODE.SMA]);
    let finalRs = [];
    if (Array.isArray(result.rows) && result.rows.length > 0) {
      result.rows.map((x) => finalRs.push(x.phone_number1));
    }
    return finalRs;
  } catch (error) {
    console.log(`EXCEPTION | function: getNotYetDecidedLoanByTime | error: ${error.message}`);
    console.log(error);
    return false;
  }
};

const insertLoanCustomerRepresentations = async (contractNumber, registrationNumber, representations) => {
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "registration_number", "full_name", "position", "dob", "id_number", "id_type", "issue_date", "issue_place", "phone_number", "email", "created_by", "management_experience", "per_province_code", "per_district_code", "per_ward_code", "per_detail_address", "per_new_province_code", "per_new_ward_code", "gender"], { table: "loan_customer_representations" });
  let insertData = [];
  representations.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      registration_number: registrationNumber,
      full_name: e.fullName,
      position: e.position,
      dob: e.dob,
      id_number: e.id,
      id_type: e.identityType,
      issue_date: e.issueDate,
      issue_place: e.issuePlace,
      phone_number: e.phoneNumber,
      email: e.email,
      created_by: null,
      management_experience: e?.managementExperience,
      per_province_code: e?.perAddress?.provinceCode,
      per_district_code: e?.perAddress?.districtCode,
      per_ward_code: e?.perAddress?.wardCode,
      per_detail_address: e?.perAddress?.detail,
      per_new_province_code: e?.perAddress?.newProvinceCode,
      per_new_ward_code: e?.perAddress?.newWardCode,
      gender: e.gender,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - saveBranchAddress: error", contractNumber);
    });
};

const insertLoanCustomerManagers = async (contractNumber, managers) => {
  if (!managers || managers.length == 0) {
    return;
  }

  console.log(`managers`, JSON.stringify(managers, null, 2));

  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet([
    "contract_number",
    "full_name",
    "position",
    "dob",
    "id_number",
    "id_type",
    "issue_date",
    "issue_place",
    "phone_number",
    "email",
    "created_by",
    "management_experience",
    "gender",
    "per_province_code",
    "per_district_code",
    "per_ward_code",
    "per_detail_address",
    "per_new_province_code",
    "per_new_ward_code",
    "cur_province_code",
    "cur_district_code",
    "cur_ward_code",
    "cur_detail_address",
    "cur_new_province_code",
    "cur_new_ward_code"
  ], { table: "loan_customer_managers" });
  let insertData = [];
  managers.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      full_name: e.fullName,
      position: e.position,
      dob: e.dob,
      id_number: e.id,
      id_type: e.identityType,
      issue_date: e.issueDate,
      issue_place: e.issuePlace,
      phone_number: e.phoneNumber,
      email: e.email,
      created_by: null,
      management_experience: e.managementExperience,
      per_province_code: e?.perAddress?.provinceCode,
      per_district_code: e?.perAddress?.districtCode,
      per_ward_code: e?.perAddress?.wardCode,
      per_detail_address: e?.perAddress?.detail,
      per_new_province_code: e?.perAddress?.newProvinceCode,
      per_new_ward_code: e?.perAddress?.newWardCode,
      cur_province_code: e?.curAddress?.provinceCode,
      cur_district_code: e?.curAddress?.districtCode,
      cur_ward_code: e?.curAddress?.wardCode,
      cur_detail_address: e?.curAddress?.detail,
      cur_new_province_code: e?.curAddress?.newProvinceCode,
      cur_new_ward_code: e?.curAddress?.newWardCode,
      gender: e.gender,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - insertLoanCustomerManagers: error", contractNumber);
    });
};

const updateLoanCustomerRepresentations = async (contractNumber, registrationNumber, representations) => {
  if (!representations || representations?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["?contract_number", "registration_number", "full_name", "position", "dob:raw", "?id_number", "id_type", "issue_date:raw", "issue_place", "phone_number", "email", "management_experience", "per_province_code", "per_district_code", "per_ward_code", "per_detail_address", "per_new_province_code", "per_new_ward_code", "gender"], { table: "loan_customer_representations" });
  let updateData = [];
  representations.forEach((e) => {
    updateData.push({
      contract_number: contractNumber,
      registration_number: registrationNumber,
      full_name: e.fullName,
      position: e.position,
      dob: `DATE '${new Date(e.dob).toISOString().split("T")[0]}'`,
      id_number: e.id,
      id_type: e.identityType,
      issue_date: `DATE '${new Date(e.issueDate).toISOString().split("T")[0]}'`,
      issue_place: e.issuePlace,
      phone_number: e.phoneNumber,
      email: e.email,
      management_experience: e?.managementExperience,
      per_province_code: e?.perAddress?.provinceCode,
      per_district_code: e?.perAddress?.districtCode,
      per_ward_code: e?.perAddress?.wardCode,
      per_detail_address: e?.perAddress?.detail,
      per_new_province_code: e?.perAddress?.newProvinceCode,
      per_new_ward_code: e?.perAddress?.newWardCode,
      gender: e.gender,
    });
  });
  const query = pgp.helpers.update(updateData, columnSet) + " WHERE v.contract_number = t.contract_number and v.id_number = t.id_number";
  return await poolWrite
    .query(query)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("UPDATE - loan_customer_representations: error", contractNumber);
    });
};

async function saveBranches(contractNumber, branches) {
  if (!branches || branches?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "branch_name", "province", "district", "ward", "address", "status_owned", "num_of_staff", "new_province", "new_ward"], { table: "loan_branch_address" });
  let insertData = [];
  branches.forEach((branchAddres) => {
    insertData.push({
      contract_number: contractNumber,
      branch_name: branchAddres.branchName,
      province: branchAddres.provinceCode,
      district: branchAddres.districtCode,
      ward: branchAddres.wardCode,
      address: branchAddres.detailAddress,
      status_owned: branchAddres.statusOwned || "",
      num_of_staff: branchAddres.numOfStaff || 0,
      new_province: branchAddres.newProvinceCode,
      new_ward: branchAddres.newWardCode,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_branch_address: error", contractNumber);
    });
}

async function saveLoanCustomerWarehouses(contractNumber, warehouses) {
  if (!warehouses || warehouses?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "warehouse_name", "province_code", "district_code", "ward_code", "detail_address", "new_province_code", "new_ward_code"], { table: "loan_customer_warehouses" });
  let insertData = [];
  warehouses.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      warehouse_name: e.warehouseName,
      province_code: e.provinceCode,
      district_code: e.districtCode,
      ward_code: e.wardCode,
      detail_address: e.detailAddress,
      new_province_code: e.newProvinceCode,
      new_ward_code: e.newWardCode,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_customer_warehouses: error", contractNumber);
    });
}

const saveLoanCustomerShareholders = async (contractNumber, shareholders) => {
  if (!shareholders || shareholders?.length === 0) {
    return;
  }
  
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "subject", "company_name", "full_name", "tax_id", "position", "capital_contribution_ratio", "id_number", "id_type", "issue_date", "issue_place", "phone_number", "email", "per_province_code", "per_district_code", "per_ward_code", "per_detail_address", "per_new_province_code", "per_new_ward_code", "cur_province_code", "cur_district_code", "cur_ward_code", "cur_detail_address", "cur_new_province_code", "cur_new_ward_code", "gender", "dob", "business_province_code", "business_district_code", "business_ward_code", "business_detail_address", "business_new_province_code", "business_new_ward_code", "entity_type", "authorization_doc_no", "business_phone_number", "business_email"], { table: "loan_customer_shareholders" });
  let insertData = [];
  shareholders.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      subject: e.subject,
      company_name: e.companyName,
      full_name: e.fullName,
      tax_id: e.taxId,
      position: e.position,
      capital_contribution_ratio: e.capitalContributionRatio,
      id_number: e.id,
      id_type: e.identityType,
      issue_date: e.issueDate,
      issue_place: e.issuePlace,
      phone_number: e.phoneNumber,
      email: e.email,
      business_phone_number: e.businessPhoneNumber,
      business_email: e.businessEmail,
      per_province_code: e.perProvinceCode,
      per_district_code: e.perDistrictCode,
      per_ward_code: e.perWardCode,
      per_detail_address: e.perDetailAddress,
      per_new_province_code: e.perNewProvinceCode,
      per_new_ward_code: e.perNewWardCode,
      cur_province_code: e.curProvinceCode,
      cur_district_code: e.curDistrictCode,
      cur_ward_code: e.curWardCode,
      cur_detail_address: e.curDetailAddress,
      cur_new_province_code: e.curNewProvinceCode,
      cur_new_ward_code: e.curNewWardCode,
      business_province_code: e.businessProvinceCode,
      business_district_code: e.businessDistrictCode,
      business_ward_code: e.businessWardCode,
      business_detail_address: e.businessDetailAddress,
      business_new_province_code: e.businessNewProvinceCode,
      business_new_ward_code: e.businessNewWardCode,
      entity_type: e.entity_type,
      authorization_doc_no: e.authorization_doc_number,
      gender: e.gender,
      dob: e.dob,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_customer_shareholders: error", contractNumber);
    });
};

const insertLoanRevenues = async (contractNumber, revenues, callback) => {
  if (!revenues || revenues?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "year", "net_revenue", "total_assets", "financial_report_type"], { table: "loan_revenues" });
  let insertData = [];
  revenues.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      year: e.year,
      net_revenue: e.netRevenue ? +e.netRevenue : null,
      total_assets: e.totalAssets ? +e.totalAssets : null,
      financial_report_type: e.financialReportType || null,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
  const result = await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_revenues: error", contractNumber);
    });
  if (result?.rowCount > 0) {
    //save docs
    const columnSet = new pgp.helpers.ColumnSet([
      "loan_revenues_id",
      "file_url",
      "file_type",
      "doc_type",
      "evf_file_url",
      "evf_doc_id"
    ], { table: "revenue_documents" });
    let insertData = [];
    revenues.forEach((e) => {
      if (e?.financialReportDocs?.length > 0) {
        const loanRevenue = result?.rows?.filter((entity) => entity.year == e.year);
        e.financialReportDocs.forEach((doc) => {
          insertData.push({
            loan_revenues_id: loanRevenue[0].id,
            file_url: doc.fileUrl,
            file_type: doc.fileType,
            doc_type: doc.docType,
            evf_file_url: doc.evfFileUrl || null,
            evf_doc_id: doc.docId || null
          });
        });
      }
    });
    const query = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
    await poolWrite
      .query(query)
      .then((results) => {
        if (callback) {
          callback(snakeToCamel(results?.rows));
        }
      })
      .catch((error) => {
        console.log(error);
        common.log("INSERT - revenue_documents: error", contractNumber);
      });
    return true;
  }
};

const insertRevenueDocuments = async (loanRevenueId, financialReportDocs, callback) => {
  //save docs
  const columnSet = new pgp.helpers.ColumnSet(["loan_revenues_id", "file_url", "file_type", "doc_type", "evf_file_url"], { table: "revenue_documents" });
  let insertData = [];
  if (financialReportDocs?.length > 0) {
    financialReportDocs.forEach((doc) => {
      insertData.push({
        loan_revenues_id: loanRevenueId,
        file_url: doc.fileUrl,
        file_type: doc.fileType,
        doc_type: doc.docType,
        evf_file_url: doc.evfFileUrl || null,
      });
    });
  }
  const query = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
  await poolWrite
    .query(query)
    .then((results) => {
      if (callback) {
        callback(snakeToCamel(results?.rows));
      }
    })
    .catch((error) => {
      console.log(error);
      common.log("INSERT - revenue_documents: error", contractNumber);
    });
  return true;
};

const getLoanRevenueByPeriod = async (contractNumber, period) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = "select * from loan_revenues where contract_number = $1 and year = $2";
    const data = await poolWrite.query(sql, [contractNumber, period]);
    if (data.rowCount == 0) {
      return false;
    }
    return data.rows[0];
  } catch (err) {
    common.log("get loan revenue info error ", contractNumber);
    return false;
  }
};

const insertLoanVatForms = async (contractNumber, vatForms) => {
  if (!vatForms || vatForms?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "period", "name"], { table: "loan_vat_forms" });
  let insertData = [];
  vatForms.forEach((e) => {
    if (!e.period) {
      return;
    }
    insertData.push({
      contract_number: contractNumber,
      period: e.period,
      name: e.name,
    });
  });
  if (insertData?.length === 0) {
    return;
  }
  const insertQuery = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
  const result = await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_vat_forms: error", contractNumber);
    });
  if (result?.rowCount > 0) {
    //save docs
    const columnSet = new pgp.helpers.ColumnSet(["loan_vat_forms_id", "file_url", "file_type", "doc_type"], { table: "vat_forms_documents" });
    let insertData = [];
    vatForms.forEach((e) => {
      if (e?.docs?.length > 0) {
        const vatForm = result?.rows?.filter((entity) => entity.period == e.period);
        e.docs.forEach((doc) => {
          insertData.push({
            loan_vat_forms_id: vatForm[0].id,
            file_url: doc.fileUrl,
            file_type: doc.fileType,
            doc_type: doc.docType,
          });
        });
      }
    });
    if (insertData?.length === 0) {
      return;
    }
    const query = pgp.helpers.insert(insertData, columnSet) + ` returning *;`;
    await poolWrite
      .query(query)
      .then()
      .catch((error) => {
        console.log(error);
        common.log("INSERT - vat_forms_documents: error", contractNumber);
      });
    return true;
  }
};

/**
 * type of ['IN','OUT']
 */
const insertLoanCustomerPartners = async (contractNumber, type, partners) => {
  if (!partners || partners?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "partner_type", "company_name", "tax_id"], { table: "loan_customer_partners" });
  let insertData = [];
  partners.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      partner_type: type,
      company_name: e.companyName,
      tax_id: e.taxId,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_customer_partners: error", contractNumber);
    });
};

const findOneMisaLoanContract = async (contractNumber) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
        select 
            lc.*,
            COALESCE(lc2.lc2, '{}') AS loan_customer,
            COALESCE(lbo.lbo, '{}') AS loan_business_owner,
            COALESCE(lcr.lcr, '[]') AS loan_customer_representations,
            COALESCE(lcs.lcs, '[]') AS loan_customer_shareholders,
            COALESCE(lcw.lcw, '[]') AS loan_customer_warehouses,
            COALESCE(lcp.lcp, '[]') AS loan_customer_partners  
        from loan_contract lc 
        LEFT JOIN LATERAL (
                SELECT row_to_json(lc2) as lc2 
                FROM loan_customer lc2 
                WHERE lc.contract_number = lc2.contract_number
            ) lc2 ON true
        LEFT JOIN LATERAL (
                SELECT row_to_json(lbo) as lbo 
                FROM loan_business_owner lbo 
                WHERE lc.contract_number = lbo.contract_number
            ) lbo ON true
        left join lateral(
                SELECT json_agg(lcr) AS lcr 
                FROM loan_customer_representations lcr 
                WHERE lc.contract_number = lcr.contract_number
            ) lcr on true
        left join lateral(
                SELECT json_agg(lcs) AS lcs 
                FROM loan_customer_shareholders lcs 
                WHERE lc.contract_number = lcs.contract_number
            ) lcs on true
        left join lateral(
                SELECT json_agg(lcw) AS lcw 
                FROM loan_customer_warehouses lcw 
                WHERE lc.contract_number = lcw.contract_number
            ) lcw on true
        left join lateral(
                SELECT json_agg(lcp) AS lcp 
                FROM loan_customer_partners lcp 
                WHERE lc.contract_number = lcp.contract_number
            ) lcp on true
        where 
            lc.contract_number = $1 
        ;
        `;
    const result = await poolWrite.query(sql, [contractNumber]);
    return result?.rows?.[0] ?? {};
  } catch (e) {
    console.error(e);
    return undefined;
  }
};

const insertForecastCapitalNeeds = async (contractNumber, data) => {
  if (!data || data?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "name", "formula", "formula_interpretation", "year_n", "previous_year", "code"], { table: "forecast_capital_needs" });
  let insertData = [];
  data.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      name: e.name,
      formula: e.formula,
      formula_interpretation: e.formulaInterpretation,
      year_n: e.yearN,
      previous_year: e.previousYear,
      code: e.code,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - forecast_capital_needs: error", contractNumber);
    });
};

const insertLoanScoring = async (contractNumber, data) => {
  if (!data || data?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "code", "name", "score", "target_score", "proportion", "cust_score"], { table: "loan_scoring" });
  let insertData = [];
  data.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      code: e.code,
      name: e.name,
      score: e.score,
      target_score: e.targetScore,
      proportion: e.proportion,
      cust_score: e.custScore,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - loan_scoring: error", contractNumber);
    });
};

const insertFinancialStatements = async (contractNumber, data) => {
  if (!data || data?.length === 0) {
    return;
  }
  const poolWrite = global.poolWrite;
  const columnSet = new pgp.helpers.ColumnSet(["contract_number", "item_code", "item_name", "report_type", "report_code", "value"], { table: "financial_statements" });
  let insertData = [];
  data.forEach((e) => {
    insertData.push({
      contract_number: contractNumber,
      item_code: e.itemCode,
      item_name: e.itemName,
      report_type: e.reportType,
      report_code: e.reportCode,
      value: e.value,
    });
  });
  const insertQuery = pgp.helpers.insert(insertData, columnSet);
  return await poolWrite
    .query(insertQuery)
    .then()
    .catch((error) => {
      console.log(error);
      common.log("INSERT - financial_statements: error", contractNumber);
    });
};

async function deleteLoanCustomerRepresentationByContractNumber({ contract_number }) {
  try {
    let sql = "update loan_customer_representations set is_deleted = 1 where contract_number = $1 and is_deleted = 0";

    let updateRs = await poolWrite.query(sql, [contract_number]);

    if (updateRs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log("delete loan customer representation by contract number error ", err);
    return null;
  }
}

async function deleteLoanCustomerManagerByContractNumber({ contract_number }) {
  try {
    let sql = "update loan_customer_managers set is_deleted = 1 where contract_number = $1 and is_deleted = 0";

    let updateRs = await poolWrite.query(sql, [contract_number]);

    if (updateRs.rowCount == 0) {
      return false;
    }
    return true;
  } catch (err) {
    common.log("delete loan customer manager by contract number error ", err);
    return null;
  }
}

async function getLoanCustomerManagerByContractNumber({ contract_number }) {
  try {
    let sql = "select * from loan_customer_managers where contract_number = $1 and is_deleted = 0 order by id";

    let data = await poolWrite.query(sql, [contract_number]);

    if (data.rowCount == 0) {
      return null;
    }
    return data.rows;
  } catch (err) {
    common.log("get loan customer manager by contract number error ", err);
    return null;
  }
}

async function getLoanManagersDocsByManagerIds({ ids }) {
  try {
    if (!ids || ids.length === 0) {
      return [];
    }
    const sql = `select * from managers_documents where manager_id = any($1)`;
    const data = await global.poolWrite.query(sql, [ids]);
    if (data.rowCount == 0) {
      return [];
    }
    return data.rows;
  } catch (err) {
    common.log("get loan customer manager by ids error ", err);
    return [];
  }
}

const findAllAndCount = async (pagingDto, selectKey = null) => {
  try {
    const filter = pagingDto.getFilterSnake();
    const keyword = pagingDto.getKeyword();
    let where = ` where (is_delete = 0 or is_delete is null)`;
    const params = [];
    let i = 1;
    let searchSql = ``;
    if (keyword) {
      searchSql = pagingDto.getAllowSearch().join(" ilike $" + i + " or ") + ` ilike $${i}`;
      params.push(`%${keyword}%`);
      searchSql = ` and (${searchSql}) `;
      i++;
    }
    where += searchSql;
    for (const key in filter) {
      if (key === "from_date") {
        if (!pagingDto.dateFilterKey) {
          continue;
        }
        where += ` and ${pagingDto.dateFilterKey}::date >= $${i}`;
      } else if (key === "to_date") {
        if (!pagingDto.dateFilterKey) {
          continue;
        }
        where += ` and ${pagingDto.dateFilterKey}::date <= $${i}`;
      } else if (key === "channel") {
        // where += ` and ${key} = ANY($${i})`;
        where += ` and partner_code = ANY($${i})`;
      } else {
        where += ` and ${key} = $${i}`;
      }
      params.push(filter[key]);
      i++;
    }
    let paging = ` LIMIT ${pagingDto.getTake()} OFFSET ${pagingDto.getSkip()}`;
    const [countRs, loansRs] = await Promise.all([poolWrite.query(`select count(id) from loan_contract ${where}`, params), poolWrite.query(`select ${selectKey || '*'} from loan_contract ${where} order by created_date desc ${paging}`, params)]);
    return { count: Number(countRs?.rows?.[0]?.count || 0), rows: loansRs?.rows || [] };
  } catch (error) {
    common.log(`[LOAN-CONTRACT-REPO][findAllPaging]  pagingDto ${JSON.stringify(pagingDto.getFilter())} error ${error}`);
    return [];
  }
};

const findByContractNumber = async function ({ contractNumber, partnerCode }) {
  let params = [contractNumber, partnerCode];
  try {
    const query = `select * from loan_contract where contract_number = $1 and partner_code = $2 order by created_date desc`;
    const result = await global.poolWrite.query(query, params);
    return result?.rowCount > 0 ? result.rows[0] : undefined;
  } catch (error) {
    console.log(`EXCEPTION | function: findByContractNumber | Params: ${JSON.stringify(params)} | error: ${error.message}`);
    console.log(error);
    return undefined;
  }
};

const updateCurrentTask = async (contractNumber, currentTask) => {
  try {
    const poolWrite = global.poolWrite;
    const queryString = "update loan_contract set current_task = $1, updated_date=now() where contract_number = $2 RETURNING *";
    const result = await poolWrite.query(queryString, [currentTask, contractNumber]);

    return result?.rowCount > 0 ? result.rows[0] : undefined;
  } catch (err) {
    common.log("loan_contract | updateCurrentTask error: ", contractNumber);
    return undefined;
  }
}

const findLoan = async (contractNumber) => {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
        select 
            lc.*,
            COALESCE(lc2.lc2, '{}') AS loan_customer,
            COALESCE(lbo.lbo, '{}') AS loan_business_owner,
            COALESCE(lcr.lcr, '[]') AS loan_customer_representations,
            COALESCE(lcs.lcs, '[]') AS loan_customer_shareholders,
            COALESCE(lcw.lcw, '[]') AS loan_customer_warehouses,
            COALESCE(lcp.lcp, '[]') AS loan_customer_partners,
            COALESCE(lcm.lcm, '[]') AS loan_customer_managers
        from loan_contract lc 
        LEFT JOIN LATERAL (
                SELECT row_to_json(lc2) as lc2 
                FROM loan_customer lc2 
                WHERE lc.contract_number = lc2.contract_number
            ) lc2 ON true
        LEFT JOIN LATERAL (
                SELECT row_to_json(lbo) as lbo 
                FROM loan_business_owner lbo 
                WHERE lc.contract_number = lbo.contract_number
            ) lbo ON true
        left join lateral(
                SELECT json_agg(lcr) AS lcr 
                FROM loan_customer_representations lcr 
                WHERE lc.contract_number = lcr.contract_number
            ) lcr on true
        left join lateral(
                SELECT json_agg(lcs) AS lcs 
                FROM loan_customer_shareholders lcs 
                WHERE lc.contract_number = lcs.contract_number
            ) lcs on true
        left join lateral(
                SELECT json_agg(lcw) AS lcw 
                FROM loan_customer_warehouses lcw 
                WHERE lc.contract_number = lcw.contract_number
            ) lcw on true
        left join lateral(
                SELECT json_agg(lcp) AS lcp 
                FROM loan_customer_partners lcp 
                WHERE lc.contract_number = lcp.contract_number
            ) lcp on true
        left join lateral(
                SELECT json_agg(lcm) AS lcm 
                FROM loan_customer_managers lcm 
                WHERE lc.contract_number = lcm.contract_number
            ) lcm on true
        where 
            lc.contract_number = $1 
        ;
        `;
    const result = await poolWrite.query(sql, [contractNumber]);
    return result?.rows?.[0] ?? {};
  } catch (e) {
    console.error(e);
    return undefined;
  }
};

/**
 * Get all loan_contract rows where status = $1, partner_code = $3 and now - approval_date > $2 days
 * @param {string} status
 * @param {number} days
 * @param {string} partnerCode
 * @returns {Promise<Array>}
 */
async function getContractsByStatusAndApprovalDate({ status, days, partnerCode }) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      SELECT * FROM loan_contract
      WHERE status = $1
        AND approval_date IS NOT NULL
        AND now() - approval_date > ($2 * INTERVAL '1 day')
        AND partner_code = $3
    `;
    const result = await poolWrite.query(sql, [status, days, partnerCode]);
    return result.rows || [];
  } catch (err) {
    common.log("getContractsByStatusAndApprovalDate error", err);
    return [];
  }
}

async function updateLoanStatusV2({ status, contractNumber, rejectionReason, cancelledBy, cancelledAt }) {
  try {
    const poolWrite = global.poolWrite;
    const updateContractSql = `
    update 
      loan_contract 
    set 
      status=$1,
      updated_date=now() ,
      rejection_reason = $2,
      cancelled_by = $3, 
      cancelled_at = $4
    where 
      contract_number = $5
    returning *;`;
    const updateRs = await poolWrite.query(updateContractSql,
      [
        status,
        rejectionReason ?? null,
        cancelledBy ?? null,
        cancelledAt ?? null,
        contractNumber
      ]);
    return updateRs?.rowCount > 0 ? updateRs.rows[0] : {};
  } catch (err) {
    common.log("updateLoanStatusV2 error", err);
    return null;
  }
}

const updateLoanApprovalStatus = async ({ status, contractNumber, approvalDate }) => {
  try {
    const poolWrite = global.poolWrite;
    const updateContractSql = `
    update 
      loan_contract 
    set 
      status=$1,
      updated_date=now() ,
      approval_date = $2
    where 
      contract_number = $3
    returning *;`;
    const updateRs = await poolWrite.query(updateContractSql,
      [
        status,
        approvalDate ?? null,
        contractNumber
      ]);
    return updateRs?.rowCount > 0 ? updateRs.rows[0] : {};
  } catch (err) {
    common.log("updateLoanApprovalStatus error", err);
    return null;
  }
}

async function getContractCustomFieldByContractNumber(contractNumber, field) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `select ${field} from loan_contract where contract_number = '${contractNumber}'`;
    const result = await poolWrite.query(sql);
    if (result.rowCount == 0) {
      return false;
    }
    return result.rows[0];
  } catch (err) {
    common.log("get customer information error ", contractNumber);
    return false;
  }
}

async function getContractLimitExpiredByDate(endDate, partnerCodes) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      SELECT * FROM loan_contract 
      WHERE status = 'ACTIVATED' 
        AND contract_type = 'CREDITLINE' 
        AND partner_code = ANY($1)
        AND end_date <= $2
    `;
    const result = await poolWrite.query(sql, [partnerCodes, endDate]);
    return result.rows || [];
  } catch (err) {
    common.log("getContractLimitExpiredByDate error", err);
    return [];
  }
}

const getProcessingFinvLoans = async function ({ idNumber, status = [] }) {
  let params = [idNumber, status];
  try {
    const query = `
      select * 
      from loan_contract 
      where id_number = $1 
      and status <> ALL($2)
      order by created_date desc
    `;
    const result = await global.poolWrite.query(query, params);
    return result?.rowCount > 0 ? result.rows : [];
  } catch (error) {
    console.log(
      `EXCEPTION | function: getProcessingFinvLoans | Params: ${JSON.stringify(params)} | error: ${error.message}`
    );
    return [];
  }
};

async function getContractsEsigningByStatusAndCreatedDate({ days, partnerCode }) {
  try {
    const poolWrite = global.poolWrite;
    const sql = `
      SELECT lc.* FROM loan_contract lc
      inner join loan_esigning le on le.contract_number = lc.contract_number 
      WHERE lc.status = 'SIGNING_IN_PROGRESS'
        AND now() - le.created_date > ($1 * INTERVAL '1 day')
        AND lc.partner_code = $2
    `;
    const result = await poolWrite.query(sql, [days, partnerCode]);
    return result.rows || [];
  } catch (err) {
    common.log("getContractsEsigningByStatusAndApprovalDate error", err);
    return [];
  }
}

module.exports = {
  getAllContractData,
  insertLoanContract,
  updateLoanContract,
  updateContractStatus,
  updateCustId,
  updateActiveContract,
  updateInactiveContract,
  updateFieldLoanContract,
  getLoanContract,
  getLoanContractJoinLoanScore,
  getContractStaus,
  getLoanContractJoinMainScore,
  getPartnerCodeAndProductCode,
  getPartnerCode,
  isValidLmsCallback,
  saveBranchAddress,
  saveLoanAcountTrading,
  getLoanContractV2,
  getRemainInfo,
  validSigned,
  getLastStepResubmit,
  isValidMerchantAccountVTP,
  genContractNumber,
  updateKUStatus,
  getLoanContractByRequestId,
  getRemainInfoHanMuc,
  getLoanContractByPartner,
  getLoanContractByCustId,
  getLoanContractBySmeInfo,
  checkGenerateContractReview,
  findByPhoneNumberList,
  findByContractList,
  updateBillDay,
  getCancelEkycLoan,
  getNotYetDecidedLoanByTime,
  insertLoanCustomerRepresentations,
  saveBranches,
  saveLoanCustomerWarehouses,
  saveLoanCustomerShareholders,
  updateLoanCustomerRepresentations,
  insertLoanRevenues,
  insertRevenueDocuments,
  insertLoanVatForms,
  insertLoanCustomerPartners,
  findOneMisaLoanContract,
  insertForecastCapitalNeeds,
  insertLoanScoring,
  insertFinancialStatements,
  insertLoanCustomerManagers,
  deleteLoanCustomerRepresentationByContractNumber,
  deleteLoanCustomerManagerByContractNumber,
  getLoanContracts,
  getLoanContractByIdNumber,
  getLoanContractRefinance,
  getLoanContractWithSmeInfo,
  getLoanRevenueByPeriod,
  getLoanCustomerManagerByContractNumber,
  findAllAndCount,
  findByContractNumber,
  updateAndGetContractStatus,
  updateCurrentTask,
  findLoan,
  getBranchAddresses,
  getLoanManagersDocsByManagerIds,
  getContractsByStatusAndApprovalDate,
  updateLoanStatusV2,
  updateLoanApprovalStatus,
  updateContractLockStatus,
  getContractCustomFieldByContractNumber,
  getContractLimitExpiredByDate,
  getProcessingFinvLoans,
  getContractsEsigningByStatusAndCreatedDate
};
