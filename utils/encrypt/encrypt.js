const jose = require("jose");
const fs = require("fs");
const openpgp = require("openpgp");
const path = require("path");

const signJws = async (privateKey, data, algorithm = "ES256") => {
  // Mã hóa
  privateKey = Buffer.from(privateKey, "base64").toString("utf-8");
  const ecPrivateKey = await jose.importPKCS8(privateKey, algorithm);
  const jws = await new jose.FlattenedSign(new TextEncoder().encode(data))
    .setProtectedHeader({ alg: "ES256" })
    .sign(ecPrivateKey);

  return jws;
};

const verifyJws = async (publicKey, encrypted, algorithm = "ES256") => {
  // Giai mã
  publicKey = Buffer.from(publicKey, "base64").toString("utf-8");
  const decoder = new TextDecoder();
  const ecPublicKey = await jose.importSPKI(publicKey, algorithm);
  const { payload, protectedHeader } = await jose.flattenedVerify(
    encrypted,
    ecPublicKey
  );

  const verifySigned = decoder.decode(payload);
  return {
    protectedHeader,
    verifySigned,
  };
};

const encryptJwe = async (
  publicKey,
  data,
  alg = "RSA-OAEP-256",
  enc = "A256CBC-HS512"
) => {
  publicKey = Buffer.from(publicKey, "base64").toString("utf-8");

  const rsaPublicKey = await jose.importSPKI(publicKey, alg);
  const jwe = await new jose.FlattenedEncrypt(new TextEncoder().encode(data))
    .setProtectedHeader({ cty: "JWT", alg, enc })
    .encrypt(rsaPublicKey);
  return jwe;
};

const decryptJwe = async (
  privateKey,
  encryptedData,
  alg = "RSA-OAEP-256",
  enc = "A256CBC-HS512"
) => {
  privateKey = Buffer.from(privateKey, "base64").toString("utf-8");
  const rsaPrivateKey = await jose.importPKCS8(privateKey, alg);
  const { plaintext, protectedHeader, additionalAuthenticatedData } =
    await jose.flattenedDecrypt(encryptedData, rsaPrivateKey);
  const decoder = new TextDecoder();
  return {
    plaintext: decoder.decode(plaintext),
    protectedHeader: protectedHeader,
    additionalAuthenticatedData: decoder.decode(additionalAuthenticatedData),
  };
};

const decryptDataMisa = async (
  encrypted,
  { evnRsaPrivateKey, misaEcPublicKey } = {}
) => {
  try {
    evnRsaPrivateKey = evnRsaPrivateKey ?? global.env.EVN_RSA_PRIVATE_KEY;
    const jweRaw = encrypted.split(".");
    const jwe = {
      ciphertext: jweRaw[3],
      iv: jweRaw[2],
      tag: jweRaw[4],
      encrypted_key: jweRaw[1],
      // aad: "RVZORkM",
      protected: jweRaw[0],
    };
    const decrypted = await decryptJwe(evnRsaPrivateKey, jwe);
    let plaintext = decrypted.plaintext;
    //verify sign
    misaEcPublicKey = misaEcPublicKey ?? global.env.MISA_EC_PUBLIC_KEY;
    plaintext = plaintext.split(".");
    const jws = {
      signature: plaintext[2],
      payload: plaintext[1],
      protected: plaintext[0],
    };
    const verified = await verifyJws(misaEcPublicKey, jws);
    const verifySigned = verified.verifySigned;
    console.log(`data:`, verifySigned);
    return JSON.parse(verifySigned);
  } catch (error) {
    console.log(`[decryptDataMisa] Error ${error}`);
    throw error;
  }
};

const encryptDataMisa = async (
  data,
  { evnEcPrivateKey, misaRsaPublicKey } = {}
) => {
  if (typeof data !== "string") {
    data = JSON.stringify(data);
  }
  //sign
  evnEcPrivateKey = evnEcPrivateKey ?? global.env.EVN_EC_PRIVATE_KEY;
  const signed = await signJws(evnEcPrivateKey, data);
  const signedText = `${signed.protected}.${signed.payload}.${signed.signature}`;
  //encrypt
  misaRsaPublicKey = misaRsaPublicKey ?? global.env.MISA_RSA_PUBLIC_KEY;
  const encrypted = await encryptJwe(misaRsaPublicKey, signedText);
  const encryptedText = `${encrypted.protected}.${encrypted.encrypted_key}.${encrypted.iv}.${encrypted.ciphertext}.${encrypted.tag}`;
  return encryptedText;
};

const decryptFileMisa = async (data, evnPgpPrivateKey,passphrase) => {
  try {
    evnPgpPrivateKey =
      evnPgpPrivateKey ??
      Buffer.from(global.env.EVN_PGP_PRIVATE_KEY, "base64").toString("utf-8");
    // const decodedData = Buffer.from(data, "base64").toString();
    const decodedData = data;
    passphrase = passphrase ?? global.env.EVN_PGP_PASSPHRASE;
    const decrypted = await decryptPgp(
      decodedData,
      evnPgpPrivateKey,
      passphrase
    );
    return decrypted;
  } catch (error) {
    console.log(`[decryptFileMisa] Error ${error}`);
    throw error;
  }
};

const encryptFileMisa = async (data, misaPgpPublicKey) => {
  try {
    misaPgpPublicKey =
      misaPgpPublicKey ??
      Buffer.from(global.env.MISA_PGP_PUBLIC_KEY, "base64").toString("utf-8");
    const encrypted = await encryptPgp(data, misaPgpPublicKey);
    return encrypted;
  } catch (error) {
    console.log(`[encryptFileMisa] Error ${error}`);
    throw error;
  }
};

const encryptPgp = async (data, publicKeyArmored, format = "base64") => {
  const publicKey = await openpgp.readKey({ armoredKey: publicKeyArmored });
  let payloadData = { text: data };
  if (Buffer.isBuffer(data)) {
    payloadData = { binary: data };
  }
  const encrypted = await openpgp.encrypt({
    message: await openpgp.createMessage(payloadData), // input as Message object
    encryptionKeys: publicKey,
  });
  return encrypted;
};

const decryptPgp = async (encrypted, privateKeyArmored, passphrase) => {
  const privateKey = await openpgp.decryptKey({
    privateKey: await openpgp.readPrivateKey({ armoredKey: privateKeyArmored }),
    passphrase,
  });
  let message;
  if (Buffer.isBuffer(encrypted)) {
    // message = await openpgp.readMessage({
    //   binaryMessage: encrypted, // parse armored message
    // });
    encrypted = encrypted.toString();
  }
  // console.log(`encrypted: ${encrypted}`);
  try {
    message = await openpgp.readMessage({
      armoredMessage: encrypted, // parse armored message
    });
  } catch (error) {
    console.log(`error : ${error}`);
  }

  const { data: decrypted } = await openpgp.decrypt({
    message,
    decryptionKeys: privateKey,
    format: "binary",
  });
  return decrypted;
};

const decryptPgpTest = async (encrypted, privateKeyArmored, passphrase) => {
  const privateKey = await openpgp.decryptKey({
    privateKey: await openpgp.readPrivateKey({ armoredKey: privateKeyArmored }),
    passphrase,
  });
  console.log("here3");
  let message;
  if (Buffer.isBuffer(encrypted)) {
    // message = await openpgp.readMessage({
    //   binaryMessage: encrypted, // parse armored message
    // });
    encrypted = encrypted.toString();
  }
  console.log("here4");

  // console.log(`encrypted: ${encrypted}`);
  try {
    message = await openpgp.readMessage({
      armoredMessage: encrypted, // parse armored message
    });
    console.log("here5");
  } catch (error) {
    console.log("here6");
    console.log(`error : ${error}`);
  }

  const { data: decrypted } = await openpgp.decrypt({
    message,
    decryptionKeys: privateKey,
    format: "binary",
  });
  return decrypted;
};


const decryptFileMisaTest = async (data, evnPgpPrivateKey,passphrase) => {
  try {
    console.log("here1");
    evnPgpPrivateKey =
      evnPgpPrivateKey ??
      Buffer.from(global.env.EVN_PGP_PRIVATE_KEY, "base64").toString("utf-8");
    // const decodedData = Buffer.from(data, "base64").toString();
    const decodedData = data;
    passphrase = passphrase ?? global.env.EVN_PGP_PASSPHRASE;
    console.log("here2");
    const decrypted = await decryptPgpTest(
      decodedData,
      evnPgpPrivateKey,
      passphrase
    );
    console.log("here7");
    return decrypted;
  } catch (error) {
    console.log(`[decryptFileMisa] Error ${error}`);
    throw error;
  }
};

const decryptDataFinv = async (
  encrypted,
  { finvEcPublicKey } = {}
) => {
  try {
    let plaintext = encrypted;
    //verify sign
    finvEcPublicKey = finvEcPublicKey ?? global.env.FINV_EC_PUBLIC_KEY;
    plaintext = plaintext.split(".");
    const jws = {
      signature: plaintext[2],
      payload: plaintext[1],
      protected: plaintext[0],
    };
    const verified = await verifyJws(finvEcPublicKey, jws);
    const verifySigned = verified.verifySigned;
    console.log(`data:`, verifySigned);
    return JSON.parse(verifySigned);
  } catch (error) {
    console.log(`[decryptDataMisa] Error ${error}`);
    throw error;
  }
};

const encryptDataFinv = async (
  data,
  { evnEcPrivateKey } = {}
) => {
  if (typeof data !== "string") {
    data = JSON.stringify(data);
  }
  //sign
  evnEcPrivateKey = evnEcPrivateKey ?? global.env.EVN_EC_PRIVATE_KEY;
  const signed = await signJws(evnEcPrivateKey, data);
  const signedText = `${signed.protected}.${signed.payload}.${signed.signature}`;
  //encrypt
  return signedText;
};

module.exports = {
  signJws,
  verifyJws,
  encryptJwe,
  decryptJwe,
  decryptDataMisa,
  encryptDataMisa,
  decryptFileMisa,
  encryptFileMisa,
  decryptFileMisaTest,
  encryptDataFinv,
  decryptDataFinv
};
