const utils = require("../utils/helper")
const smsService = require("../utils/smsService")
//const callbackService = require("../utils/callbackService")
const callbackService = require("../services/callback-service")
const { STATUS, KUNN_STATUS, CALLBACK_STAUS, WORKFLOW_STAGE } = require("../const/caseStatus")
const kunnRepo  = require("../repositories/kunn-repo")
const { PARTNER_CODE, CONTRACT_TYPE } = require("../const/definition")
const { validVatDoc } = require("../repositories/document")
const loanContractRepo = require("../repositories/loan-contract-repo")
const moment = require("moment")
const { sendNotification } = require("../services/notification-service")
const { serviceEndpoint } = require("../const/config")
const { callbackKunnCancelApi } = require("../apis/misa-api")
const { saveWorkflow } = require("../repositories/logging-repo")
const actionAuditService = require("../services/action-audit")
const { CASE_STATUS } = require("../const/code-const")

async function activeContract(req,res) {
    try {
        const config = req.config
        const poolRead = req.poolRead
        // const poolWrite = req.poolWrite
        const {contractNumber, kunnNumber, disbursementDate}  = req.body;        

        const isValidContract = await utils.validContractnumber(poolRead,contractNumber)
        if(!isValidContract) {
            return res.status(200).json({
                code : 0,
                msg : "invalid contract number"
            })
        }
    
        const validKU = await utils.getKUNNNumber(poolRead,contractNumber)
        if(!validKU.includes(kunnNumber)) {
            return res.status(200).json({
                code : 0,
                msg : "invalid KUNN number"
            })   
        }
        const kunnData = await kunnRepo.getKunnData(kunnNumber);
        // const addMonths = (dateString, months) => {
        //     return moment(dateString).add(months, 'months');
        // }
          
         // let startDate = new Date()
        // let startDate2 = new Date()
        // let endDate = addMonths(startDate2,kunnData.tenor)
        // await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.ACTIVATED)
        await kunnRepo.update(kunnNumber, {
            status: KUNN_STATUS.ACTIVATED,
            disbursement_date: disbursementDate || null
        })
        actionAuditService.saveCaseHistoryActionAudit(kunnNumber, CASE_STATUS.ACTIVATED.STEP_CODE, CASE_STATUS.ACTIVATED.ACTION.ACTIVATED, kunnNumber)
        // await Promise.all([kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.ACTIVATED),
        //     kunnRepo.updateEffectiveDate(kunnNumber,startDate,endDate)])

        const partnerCode = kunnData?.partner_code;
        if(partnerCode==PARTNER_CODE.MISA){
            const isVAT = await validVatDoc(kunnNumber);
            if(isVAT>0){
                try {
                  //update
                  const endDate = moment()
                    .add(Number(kunnData.tenor), "months")
                    .format("yyyy-MM-DD");
                  const startDate = moment().format("yyyy-MM-DD");
                  await kunnRepo.update(kunnNumber, {
                    start_date: startDate,
                    end_date: endDate,
                    date_approval: startDate,
                    status: KUNN_STATUS.ACTIVATED_WITH_DIS_DOCS
                  });
                } catch (error) {
                  console.log(
                    `[CALLBACK][callbackActiveKunnV2] update kunn ${this.kunnNumber} error ${error}`
                  );
                }
            }
            await saveWorkflow(WORKFLOW_STAGE.ACTIVATE, STATUS.ACTIVATED,contractNumber, 'system',kunnNumber);
        }
        if (partnerCode==PARTNER_CODE.SMA || partnerCode==PARTNER_CODE.SMASYNC) callbackService.callbackPartner(kunnNumber,partnerCode,CALLBACK_STAUS.ACTIVE,'','')
        else callbackService.callbackPartner(contractNumber,partnerCode,CALLBACK_STAUS.ACTIVE_KUNN,undefined,"disbursement-request",kunnNumber)
        const smsUrl = config.data.smsService.sendSMS
        let msg = config.data.smsService.ActivateKUMsg
        msg = msg.replace("kunnNumber", kunnNumber)
        msg = msg.replace("contractNumber", contractNumber)
        const phoneNumberRs = await utils.getPhoneNumber(poolRead, contractNumber)
        if (phoneNumberRs !== undefined && partnerCode != PARTNER_CODE.MISA) {
            smsService.sendSMS(contractNumber,msg, smsUrl, phoneNumberRs)
        }
        //send noti appMC
        if (partnerCode == PARTNER_CODE.MCAPP) {
            const bodyNoti = {
                phoneNumber: phoneNumberRs,
                title: 'Khế ước đã được giải ngân!',
                message: `Hợp đồng khế ước ${kunnNumber} của bạn đã được giải ngân`, 
                value: {
                    contractNumber: contractNumber,
                    kuNumber: kunnNumber
                }
            };
            const endPoint = serviceEndpoint.NOTIFICATION.KUNN_ACTIVE;
            sendNotification( bodyNoti, endPoint, config );    
        }
        
        return res.status(200).json({
            code : 0,
            msg : "active contract success"
        })   
    }
    catch(error ) {
        console.log(error)
        return res.status(200).json({
            code : -1,
            msg : "server error"
        })
    }
}

async function deactiveContract(req,res) {
    try {
        // const config = req.config
        const poolRead = req.poolRead
        // const poolWrite = req.poolWrite
        const contractNumber = req.body.contractNumber
        const kunnNumber = req.body.kunnNumber
        

        const isValidContract = await utils.validContractnumber(poolRead,contractNumber)
        if(!isValidContract) {
            return res.status(200).json({
                code : 0,
                msg : "invalid contract number"
            })
        }
        const validKU = await utils.getKUNNNumber(poolRead,contractNumber)
        if(!validKU.includes(kunnNumber)) {
            return res.status(200).json({
                code : 0,
                msg : "invalid KUNN number"
            })   
        }
        const kunnData = await kunnRepo.getKunnData(kunnNumber)
        if(kunnData?.partner_code == PARTNER_CODE.SMA || kunnData?.partner_code == PARTNER_CODE.SMASYNC)
            callbackService.callbackPartner(kunnNumber, kunnData?.partner_code, CALLBACK_STAUS.TERMINATED)
        await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.TERMINATED)
        actionAuditService.saveCaseHistoryActionAudit(kunnNumber, CASE_STATUS.TERMINATED.STEP_CODE, CASE_STATUS.TERMINATED.ACTION.TERMINATED, kunnNumber);
        return res.status(200).json({
            code : 0,
            msg : "deactive contract success"
        })   
    }
    catch(error ) {
        console.log(error)
        return res.status(200).json({
            code : -1,
            msg : "server error"
        })
    }
}

async function cancelContract(req,res) {
    try {
        // const config = req.config
        const poolRead = req.poolRead
        // const poolWrite = req.poolWrite
        const contractNumber = req.body.contractNumber
        const kunnNumber = req.body.kunnNumber
        

        const isValidContract = await utils.validContractnumber(poolRead,contractNumber)
        if(!isValidContract) {
            return res.status(200).json({
                code : 0,
                msg : "invalid contract number"
            })
        }

        const kunn = await kunnRepo.getKunnData(kunnNumber);
            if(!kunn?.kunn_id) {
            return res.status(200).json({
                code : 0,
                msg : "invalid KUNN number"
            })   
        }
        
        const result = await kunnRepo.updateKUStatus(kunnNumber,KUNN_STATUS.CANCELLED)

        if(kunn.partner_code === PARTNER_CODE.MISA)
        {
            callbackKunnCancelApi(kunnNumber,{
                contractNumber: kunn.contract_number,
                debtContractNumber: kunnNumber,
                debtContractStatus: 10
            });
        }

        if (result == true) {
            return res.status(200).json({
                code: 0,
                msg: "Update contract success !"
            });
        } else {
            return res.status(200).json({
                code: -1,
                msg: "Update contract dibursed fail !"
            });
        }
        
    }
    catch(error ) {
        console.log(error)
        return res.status(200).json({
            code : -1,
            msg : "server error"
        })
    }
}

const updatePayment = async (req, res) => {
  try {
    const { kunnNumber, contractNumber } = req.body;
    console.log(
      `[MISA][KUNN]updatePayment ${JSON.stringify(req.body ?? "{}")}`
    );
    const kunn = utils.snakeToCamel(await kunnRepo.getKunnData(kunnNumber));
    await callbackService.callbackPartner(
      contractNumber,
      kunn.partnerCode,
      CALLBACK_STAUS.UPDATE_PAYMENT,
      undefined,
      "update-payment",
      kunnNumber
    );
    return res.status(200).json({
      code: 0,
      msg: "update contract dibursed success",
    });
  } catch (error) {
    console.log(
      `[MISA][KUNN]updatePayment ${JSON.stringify(
        req.body ?? "{}"
      )} error ${error}`
    );

    return res.status(500).json({
      code: -1,
      msg: "server error",
    });
  }
};

const updateReceivedPayment=async (req, res) => {
    try {
      const { kunnNumber, contractNumber } = req.body;
      const kunn = utils.snakeToCamel(await kunnRepo.getKunnData(kunnNumber));
      await callbackService.callbackPartner(
        contractNumber,
        kunn.partnerCode,
        CALLBACK_STAUS.RECEIVED_PAYMENT,
        undefined,
        "received-payment",
        kunnNumber,
        req.body
      );
      return res.status(200).json({
        code: 0,
        msg: "update contract dibursed success",
      });
    } catch (error) {
      console.log(
        `[MISA][KUNN]updateReceivedPayment ${JSON.stringify(
          req.body ?? "{}"
        )} error ${error}`
      );
  
      return res.status(500).json({
        code: -1,
        msg: "server error",
      });
    }
  };
  

module.exports = {
    activeContract,
    deactiveContract,
    cancelContract,
    updatePayment,
    updateReceivedPayment
}