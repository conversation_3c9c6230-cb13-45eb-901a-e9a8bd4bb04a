const BizziLoanLender = require("./bizzi-loan-lender");

class BizziLimitLoanLender extends BizziLoanLender {
  constructor({ referenceNumber, data, partnerCode, table, url, changeRequest, changeRequestDetails, groupType }) {
    super({
      referenceNumber,
      data,
      partnerCode,
      table,
      url,
      changeRequest,
      changeRequestDetails,
      groupType,
    });
    this.table = table || "loan_contract";
  }
}

module.exports = BizziLimitLoanLender;
