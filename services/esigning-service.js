const { getData, updateData } = require("../utils/sqlHelper");
const { STATUS, KUNN_STATUS } = require("../const/caseStatus");
const documentRepo = require("../repositories/document");
const loanContractRepo = require("../repositories/loan-contract-repo");
const esigningService = require("../utils/esigningService");
const { PARTNER_CODE } = require("../const/definition");
const { TABLE, SIGNED_TYPE, KUNN_TEMPLATE_DOCTYPE_SIGNED_WITH_PARTNER } = require("../const/variables-const");
const actionAuditService = require("../services/action-audit")
const { CASE_STATUS } = require("../const/code-const")

const fillEvfSignature = async ({contractNumber,docTypes}) => {
  try {
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan?.id) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | fillEvfSignature | loan not found`);
      return false;
    }
    // const docTypes = ['BTTHDTD', 'BTTQDCV', 'BTTCNKPT'];
    for (const docType of docTypes) {
      let documents = await documentRepo.getByDocType(contractNumber, docType);
      if (documents) {
        for (const document of documents) {
          const latest = await documentRepo.getLatestDocumentHistory(document.contract_number, document.doc_type);
          if (latest && latest.status === STATUS.COMPLETED_SIGN) {
            // skip signing if the document is already signed
            continue;
          }
          await esigningService.signEvf(document, loan.partner_code);
        }
      }
    }
    //xử lý trigger ký đúng và đủ file để đi tiếp workflow
    await loanContractRepo.updateContractStatus(STATUS.COMPLETED_SIGN, contractNumber);
    return true;
  } catch (e) {
    console.error(e);
    return false;
  }
}

const fillEvfSignatureKunn = async ({contractNumber, debtContractNumber}) => {
  try {
    const docTypes = ['BTTKUNN', 'BTTXNCN', 'BTTTTCN'];
    for (const docType of docTypes) {
      let documents = await getData({
        table: TABLE.LOAN_CONTRACT_DOCUMENT,
        where: {
          kunn_contract_number: debtContractNumber,
          doc_type: docType,
          is_deleted: 0,
          is_checked: 1,
          signed_type: SIGNED_TYPE.PARTNER_SIGNED
        }
      });
      if (documents) {
        for (const document of documents) {
          // const latest = await documentRepo.getLatestDocumentHistory(document.contract_number, document.doc_type);
          const latest = await getData({
            table: TABLE.LOAN_CONTRACT_DOCUMENT,
            where: {
              kunn_contract_number: debtContractNumber,
              doc_type: document.doc_type,
              is_deleted: 0,
              signed_type: SIGNED_TYPE.EVF_SIGNED
            }
          })?.[0];
          if (latest && latest.status === KUNN_STATUS.EVF_SIGNED) {
            // skip signing if the document is already signed
            continue;
          }
          await esigningService.signEvf(document, PARTNER_CODE.BIZZ);
        }
      }
    }
    await updateData({
      table: TABLE.KUNN,
      columns: ['status', 'updated_date'],
      values: [KUNN_STATUS.EVF_SIGNED, new Date()],
      conditions: {
        contract_number: contractNumber,
        kunn_id: debtContractNumber
      }
    })
    actionAuditService.saveCaseHistoryActionAudit(debtContractNumber, CASE_STATUS.SIGNING_IN_PROGRESS.STEP_CODE, CASE_STATUS.SIGNING_IN_PROGRESS.ACTION.EVF_SIGNED, debtContractNumber);
    return true;
  } catch (e) {
    console.error(e);
    return false;
  }
}

const fillEvfSignatureKunnV2 = async ({contractNumber, debtContractNumber, partnerCode}) => {
  try {
    const docTypes = KUNN_TEMPLATE_DOCTYPE_SIGNED_WITH_PARTNER[partnerCode];
    for (const docType of docTypes) {
      let documents = await getData({
        table: TABLE.LOAN_CONTRACT_DOCUMENT,
        where: {
          kunn_contract_number: debtContractNumber,
          doc_type: docType,
          is_deleted: 0,
          is_checked: 1,
          signed_type: SIGNED_TYPE.PARTNER_SIGNED
        }
      });
      if (documents) {
        for (const document of documents) {
          const latest = await getData({
            table: TABLE.LOAN_CONTRACT_DOCUMENT,
            where: {
              kunn_contract_number: debtContractNumber,
              doc_type: document.doc_type,
              is_deleted: 0,
              signed_type: SIGNED_TYPE.EVF_SIGNED
            }
          })?.[0];
          if (latest && latest.signed_type === KUNN_STATUS.EVF_SIGNED) {
            // skip signing if the document is already signed
            continue;
          }
          await esigningService.signEvf(document, partnerCode);
        }
      }
    }
    await updateData({
      table: TABLE.KUNN,
      columns: ['status', 'updated_date'],
      values: [KUNN_STATUS.EVF_SIGNED, new Date()],
      conditions: {
        contract_number: contractNumber,
        kunn_id: debtContractNumber
      }
    })
    actionAuditService.saveCaseHistoryActionAudit(debtContractNumber, CASE_STATUS.SIGNING_IN_PROGRESS.STEP_CODE, CASE_STATUS.SIGNING_IN_PROGRESS.ACTION.EVF_SIGNED, debtContractNumber);
    return true;
  } catch (e) {
    console.error(e);
    return false;
  }
};

const fillEvfSignatureFinv = async ({contractNumber}) => {
  try {
    await esigningService.customerSignEc(contractNumber);
    await loanContractRepo.updateContractStatus(STATUS.COMPLETED_SIGN, contractNumber);
    return true;
  } catch (e) {
    console.error(e);
    return false;
  }
}

module.exports = {
  fillEvfSignature,
  fillEvfSignatureKunn,
  fillEvfSignatureFinv,
  fillEvfSignatureKunnV2,
};
