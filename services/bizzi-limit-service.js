const AF1Model = require("../A1_BASE/af1-bizzi-limit");
const AF2BizziModel = require("../A2_BASE/af2-bizzi-limit");
const AF3BizziModel = require("../A3_BASE/af3-bizzi-limit");
const { saveStepLog } = require("../repositories/logging-repo");
const loanContractRepo = require("../repositories/loan-contract-repo");
const loanCustomerRepo = require("../repositories/loan-customer-repo");
const s3Service = require("../upload_document/s3-service");
const documentRepo = require("../repositories/document");
const sqlHelper = require("../utils/sqlHelper");
const { PARTNER_CODE, SERVICE_NAME, LIST_PARTNER_CODE, request_type, REQUEST_TYPE, TASK_FLOW, DOCUMENT_REFERENCE_TABLE } = require("../const/definition");
const { LENDER_REFERENCE_TYPE, KUNN_TEMPLATE_DOCTYPE, LENDER_REQUEST_TYPE, AF3_TEMPLATE_DOCTYPE, AF3_TEMPLATE_DOCTYPE_SIGNED, REF_TABLE, LENDER_CONFIG_GROUP_TYPE, KUNN_TEMPLATE_DOCTYPE_SIGNED, BIZZ_MODEL_DATA_TYPE, OWNER_ID, REFUND_STATUS, REFUND_REQUEST_STATUS, REFUND_ACTIONS, KUNN_DOCS_BIZZ } = require("../const/variables-const");
const { STATUS, CALLBACK_STAUS, KUNN_STATUS } = require("../const/caseStatus");
const { callbackPartner } = require("./callback-service");
const { checkAllAF3DocumentsExist } = require("./creditlimit-service");
const { getValueByCodeType, getValueCodeMasterdataV2, getValueCodeByCodeType } = require("../utils/masterdataService");
const { BadRequestResponse, SuccessResponse, BadRequestResponseV2, ServerErrorResponse } = require("../base/response");
const excelHelper = require("../utils/excel-helper");
const esigningService = require("../utils/esigningService");
const loanAf1Repo = require("../repositories/loans-af1-repo");
const loanRatingRepo = require("../repositories/loan-rating-repo");
const BizziLoanLender = require("./bizzi-limit-loan-lender");
// const BizziLoanLender = require("./bizzi-loan-lender");
const { find, findOne } = require("../utils/sqlHelper");
const lenderChangeRequestRepo = require("../repositories/lender-change-request-repo");
const { LENDER_CHANGE_REQUEST_STATUS } = require("../const/status-const");
const loanShareholdersRepo = require("../repositories/loan-customer-shareholders-repo");
const loanRepresentationsRepo = require("../repositories/loan-customer-representations-repo");
const loanBusinessOwnerRepo = require("../repositories/loan-business-owner-repo");
const lenderColumnConfigRepo = require("../repositories/lender-collumn-config-repo");
const { goNextStep } = require("./workflow-continue");
const loanRevenuesRepo = require("../repositories/loan-revenues-repo");
const loanVatFormsRepo = require("../repositories/loan-vat-forms-repo");
const refundRepo = require("../repositories/refund-repo");
const lmsService = require("../utils/lmsService");
const kunnRepo = require("../repositories/kunn-repo");
const financialStatementsExportRepo = require("../repositories/financial-statements-export-repo");
const financialStatementDetailsRepo = require("../repositories/financial_statement_details-repo");

const uuid = require("uuid");
const _ = require("lodash");

const af2PartnerMapping = {
  sme_name: "company_name",
  total_turnover_next_year: "total_turn_over_next_year",
};

const mappingUiFilter = (pagingDto) => {
  const mapping = {
    "representative.identityCard": "sme_representation_id",
  };
  for (let key in pagingDto.filter) {
    if (mapping[key]) {
      pagingDto.filter[mapping[key]] = pagingDto.filter[key];
      delete pagingDto.filter[key];
    }
  }
};

async function getPresignedDocumentUrl(req, res) {
  let { file_name, doc_type } = req.body;

  const document = await sqlHelper.findOne({
    table: "loan_contract_document",
    whereCondition: {
      file_name: file_name,
      doc_type: doc_type,
    },
  });

  if (Object.keys(document).length === 0) {
    throw new BadRequestResponse([], "file_name or doc_type not found");
  }

  req.body.file_key = document.file_key;
  req.body.doc_type = document.doc_type;
  req.body.doc_id = document.doc_id;

  const result = await s3Service.genPresignedDownloadUrlForSme(req, res);
  return result;
}

async function getPresignedUploadDocumentUrl(req, res) {
  let { file_name } = req.body;
  const isValidFile = s3Service.validateFileExtension(file_name);
  if (!isValidFile) {
    throw new BadRequestResponse([], "Invalid file type");
  }

  const result = await s3Service.genPresignedUploadUrlForSme(req, res);

  return result;
}

async function getMultiplePresignedDocumentUrl(req, res) {
  let { doc_type } = req.body;
  let { contract_number } = req.params;

  let listdoc = await documentRepo.findByContractAndTypes(contract_number, doc_type);
  let result = null;

  if (listdoc.length === 0) {
    throw new BadRequestResponse([], "listdoc by doc_type not found");
  }

  let loan = await loanContractRepo.findByContractNumber({
    contractNumber: contract_number,
    partnerCode: PARTNER_CODE.BZHM,
  });

  if (!loan?.id) {
    throw new BadRequestResponse(`Loan not found for contract_number: ${contract_number}`);
  }

  //loan.status == STATUS.RESUBMIT_A3 || loan.status == KUNN_STATUS.RESUBMIT
  if (loan.status == STATUS.RESUBMIT_A3) {
    let change_request = await lenderChangeRequestRepo.getLatestLenderChangeRequest({
      request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_AF3_PROCESS,
      reference_code: contract_number,
      reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT,
      status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
    });

    if (change_request && change_request.request_body) {
      const doctypes_resubmit = change_request?.request_body?.info?.map((entry) => {
        if (entry.type === "file") return entry.key;
      });

      const origin_doc_by_resubmit = await documentRepo.findOriginContractByDocType(contract_number, doctypes_resubmit);
      if (origin_doc_by_resubmit?.length > 0) {
        const lookup = new Map(origin_doc_by_resubmit.map((doc) => [doc.doc_type, doc]));
        //check nếu doc_type nào có resubmit thì lấy loan_contract_doc ban đầu lúc chưa ký
        let all_docs = listdoc.map((doc) => {
          if (lookup.has(doc.doc_type)) {
            return lookup.get(doc.doc_type);
          } else {
            return doc;
          }
        });

        result = await s3Service.genMultiplePresignedDownloadUrlForSme(all_docs);
      }
    }
  }

  if (!result) {
    result = await s3Service.genMultiplePresignedDownloadUrlForSme(listdoc);
  }

  if (!result) {
    throw new BadRequestResponse([], "can not generate presigned URL for documents");
  }

  const responseBody = {
    contract_number: contract_number,
    files: result,
  };

  return new SuccessResponse(responseBody);
}

async function getKUUNPresignedDocumentUrl(req, res) {
  let { doc_type } = req.body;
  let { contract_number, debt_contract_number } = req.params;

  if (doc_type.length == 0) {
    doc_type = KUNN_TEMPLATE_DOCTYPE_SIGNED;
  }

  const listdoc = await documentRepo.getDocumentsByContractAndKunnAndDocTypes(contract_number, debt_contract_number, doc_type);

  if (listdoc.length === 0) {
    throw new BadRequestResponse([], "listdoc by doc_type not found");
  }

  const result = await s3Service.genMultiplePresignedDownloadUrlForSme(listdoc);
  if (!result) {
    throw new BadRequestResponse([], "can not generate presigned URL for documents");
  }

  const responseBody = {
    contract_number: contract_number,
    files: result,
  };

  return new SuccessResponse(responseBody);
}

async function getOriginDocumentByDocType(contract_number) {
  const doc_types = [...KUNN_TEMPLATE_DOCTYPE, ...AF3_TEMPLATE_DOCTYPE, ...KUNN_DOCS_BIZZ];
  const result = await documentRepo.findOriginContractByDocType(contract_number, doc_types);

  return new SuccessResponse(result);
}

async function getLoanRequest(req, res) {
  const paging = req.paging;
  mappingUiFilter(paging);
  paging.setAllowFilter(["contract_number", "status", "registration_number", "created_date", "from_date", "to_date", "channel"]);
  paging.setAllowSearch(["contract_number", "status", "registration_number"]);
  paging.setDateFilterKey("created_date");
  paging.addFilter({ channel: req.query?.channel || PARTNER_CODE.BZHM });
  let selectKey = ["id", "request_id", "partner_code", "contract_number", "status", "registration_number", "tax_id", "sme_name", "created_date", "channel", "updated_date", "request_amt", "request_int_rate"];

  const data = await loanContractRepo.findAllAndCount(paging, selectKey);
  return new SuccessResponse(data);
}

async function handleDocumentsGenerated(req, res) {
  const { contract_number } = req.body;

  if (!contract_number) {
    throw new BadRequestResponse("Missing contract_number");
  }

  const loan = await sqlHelper.findOne({
    table: "loan_contract",
    whereCondition: {
      contract_number: contract_number,
    },
  });

  if (!loan?.id) {
    throw new BadRequestResponse(`Loan not found for contract_number: ${contract_number}`);
  }

  let completed = false;
  if (loan) {
    completed = await checkAllAF3DocumentsExist(contract_number);
  }

  if (completed && loan.current_task === TASK_FLOW.BIZZ_LIMIT_GENERATE_TEMPLATE && loan.status === STATUS.SIGNING_IN_PROGRESS) {
    console.log(`${contract_number} go next step afther check BIZZ_LIMIT_GENERATE_TEMPLATE`);

    //callback to biszi partner
    goNextStep(contract_number);
  } else {
    //log step attempt to generate documents
    console.log(`${contract_number} check documents not generated enough for callback to bizzi partner`);
  }

  return new SuccessResponse();
}

const exportLoanRequest = async (req, res) => {
  const paging = req.paging;
  mappingUiFilter(paging);
  paging.setAllowFilter(["contract_number", "status", "registration_number", "created_date", "from_date", "to_date"]);
  paging.setAllowSearch(["contract_number", "status", "registration_number"]);
  paging.setDateFilterKey("created_date");

  const selects = "contract_number, status,sme_tax_id,sme_name, registration_number, created_date, updated_date";
  const selectKey = selects.split(",").map((item) => item.trim());

  const data = await loanContractRepo.findAllAndCount(paging, selects);

  //export to excel or csv
  const buffer = await excelHelper.jsonToExcelBuffer(data.rows, selectKey);

  return buffer;
};

async function getMasterdata(req, res) {
  const { code_type } = req.query;

  const masterdata = await getValueByCodeType(req, code_type);
  if (!masterdata) {
    throw new BadRequestResponse([], "Masterdata not found for code_type: " + code_type);
  }

  return new SuccessResponse(masterdata, "get masterdata success");
}

async function af1Submit(req, res) {
  return new AF1Model(req, res).process();
}

async function af2Submit(req, res) {
  return new AF2BizziModel(req, res).process();
}
async function af3Submit(req, res) {
  return new AF3BizziModel(req, res).process();
}

async function af2ReSubmit(req, res) {
  const payload = req.body;
  const { contract_number } = payload;
  const af2FormData = await getLoanAf2FormData(contract_number);
  mergeIds(af2FormData, payload);
  const changeRequests = await lenderChangeRequestRepo.getChangeRequestDetailByContract({
    contract_number,
    shareholderIds: payload.shareholders?.members?.map((x) => x.id),
    repIds: payload.representations?.map((x) => x.id),
    managerIds: payload.managers?.map((x) => x.id),
    revenueIds: payload.financial_information?.revenues?.map((x) => x.id),
    vatFormIds: payload.financial_information?.vat_forms?.map((x) => x.id),
  });
  const lenderColumnConfig = await lenderColumnConfigRepo.getLenderColumnConfig({ partnerCode: PARTNER_CODE.BZHM });
  let clearFinancialInfo = false;
  for (const element of changeRequests) {
    if (element.status) continue;
    let config = lenderColumnConfig.find((item) => item.key === element.key && item.table_name?.toUpperCase() === element.ref_table);
    if (element.ref_table === "LOAN_CONTRACT") {
      if (config?.type === "file") {
        // form[element.key] ??= {
        //   value: JSON.parse(element.old_value),
        //   comment: element.comment,
        // };
      } else {
        element.new_value = payload[af2PartnerMapping[element.key] ?? element.key];
      }
    } else if (element.ref_table === DOCUMENT_REFERENCE_TABLE.LOAN_CUSTOMER_SHAREHOLDERS) {
      if (payload.shareholders?.members) {
        const shareholder = payload.shareholders.members.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          let docs = shareholder.docs?.filter((doc) => doc.doc_type === element.key);
          for (const doc of docs) {
            await documentRepo.updateLoanContractNumberKunn({ docId: doc.doc_id, docType: doc.doc_type, contractNumber: contract_number });
          }
          element.new_value = docs;
        } else {
          element.new_value = shareholder[element.key];
        }
      }
    } else if (element.ref_table === DOCUMENT_REFERENCE_TABLE.LOAN_CUSTOMER_REPRESENTATIONS) {
      if (payload.representations) {
        const shareholder = payload.representations.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          let docs = shareholder.docs?.filter((doc) => doc.doc_type === element.key);
          for (const doc of docs) {
            await documentRepo.updateLoanContractNumberKunn({ docId: doc.doc_id, docType: doc.doc_type, contractNumber: contract_number });
          }
          element.new_value = docs;
        } else {
          element.new_value = shareholder[element.key];
        }
      }
    } else if (element.ref_table === DOCUMENT_REFERENCE_TABLE.LOAN_CUSTOMER_MANAGERS) {
      if (payload.managers) {
        const manager = payload.managers.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          let docs = manager.docs?.filter((doc) => doc.doc_type === element.key);
          for (const doc of docs) {
            await documentRepo.updateLoanContractNumberKunn({ docId: doc.doc_id, docType: doc.doc_type, contractNumber: contract_number });
          }
          element.new_value = docs;
        } else {
          element.new_value = manager[element.key];
        }
      }
    } else if (element.ref_table === DOCUMENT_REFERENCE_TABLE.LOAN_VAT_FORMS) {
      clearFinancialInfo = true;
      if (payload.financial_information?.vat_forms) {
        const vatForm = payload.financial_information.vat_forms.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          let docs = vatForm.docs?.filter((doc) => doc.doc_type === element.key);
          for (const doc of docs) {
            await documentRepo.updateLoanContractNumberKunn({ docId: doc.doc_id, docType: doc.doc_type, contractNumber: contract_number });
          }
          element.new_value = docs;
        } else {
          element.new_value = manager[element.key];
        }
      }
    } else if (element.ref_table === DOCUMENT_REFERENCE_TABLE.LOAN_REVENUES) {
      clearFinancialInfo = true;
      if (payload.financial_information?.revenues) {
        const revenue = payload.financial_information.revenues.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          let docs = revenue.docs?.filter((doc) => doc.doc_type === element.key);
          for (const doc of docs) {
            await documentRepo.updateLoanContractNumberKunn({ docId: doc.doc_id, docType: doc.doc_type, contractNumber: contract_number });
          }
          element.new_value = docs;
        } else {
          element.new_value = manager[element.key];
        }
      }
    }
    element.is_change = element.new_value != element.old_value;
  }
  await lenderChangeRequestRepo.updateChangeRequestDetails(changeRequests);
  let checkFull = true;
  for (const element of changeRequests) {
    if (!element.status && !element.new_value) {
      checkFull = false;
      break;
    }
  }
  if (checkFull) {
    // auto approve change request
    const loan = await loanContractRepo.getLoanContract(contract_number);
    const loanLenderModel = await BizziLoanLender.init({
      referenceNumber: contract_number,
      data: loan,
      partnerCode: loan.partner_code,
      table: "loan_contract",
    });
    let request = await lenderChangeRequestRepo.getLatestLenderChangeRequest({
      request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_PROCESS,
      reference_code: contract_number,
      reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT,
      status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
    });
    if (!request) {
      throw new BadRequestResponse([], "Lender change request not found");
    }
    if (clearFinancialInfo) {
      await removeAllFinanceDataByContractNumber(contract_number);
    }
    await lenderChangeRequestRepo.updateStatusOfListChangeRequest([request.id], LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE);
    await loanLenderModel.commitChangeRequest(
      {
        comment: "Partner resubmit",
        createdBy: "system",
      },
      LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ
    );
    goNextStep(contract_number);
    return new SuccessResponse({
      message: "Resumit successfully",
    });
  } else {
    return new SuccessResponse({
      message: "Resumit successfully, but not all fields are filled",
    });
  }
}

async function af2Approve(req, res) {
  const { contract_number } = req.body;
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan.status || loan.status !== STATUS.IN_MANUAL_REVIEW_A2) {
    throw new BadRequestResponse([], "Contract is not IN_MANUAL_REVIEW_A2 status");
  }
  goNextStep(contract_number);
  return new SuccessResponse();
}

async function af2Reject(req, res) {
  throw new BadRequestResponse("AF2 reject endpoint is not implemented yet.");
}

async function manualWorkflow(req, res) {
  try {
    const { contract_number } = req.body;
    if (!contract_number) {
      throw new BadRequestResponse("Missing contract_number");
    }
    let loan = await loanContractRepo.findByContractNumber({
      contractNumber: contract_number,
      partnerCode: PARTNER_CODE.BZHM,
    });
    if (!loan) {
      throw new BadRequestResponse("Loan not found");
    }
    goNextStep(contract_number);
  } catch (error) {
    console.error("Error in manualWorkflow:", error);
  }
}

const getLoanRequestDetailsAf1 = async (req, res) => {
  try {
    const { contract_number } = req.query;
    if (!contract_number) {
      throw new BadRequestResponseV2("Missing contract_number");
    }

    const loanAf1 = await loanAf1Repo.getLoanAf1(contract_number);
    const status = await loanContractRepo.getContractStaus(contract_number);
    if (!loanAf1 || !status) {
      throw new BadRequestResponseV2("Loan request af1 or loan contract not found for contract_number: " + contract_number);
    }
    loanAf1["status"] = status;

    return new SuccessResponse(loanAf1, "Loan request af1 details retrieved successfully");
  } catch (error) {
    console.error("Error in getLoanRequestDetailsAf1:", error);
    throw error;
  }
};

async function getLoanRequestDetailsAf2(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  //sua lay nhung docs can thiet
  // const documents = await documentRepo.findByContractAndTypes(contractNumber, ["SBIZ", "SPCB", "SCR", "STCRC", "SLOCH", "SDACA", "SPCHQ", "SMRCO"]);

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2,
  });

  // thong tin tab nhu cau vay von
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getLoanRequestDetailsAF3(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract_document",
    groupType: "AF3",
  });
  // thong tin tab nhu cau vay von
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getLoanRepresentations(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;

  let representations = await find({
    table: "loan_customer_representations",
    whereCondition: {
      contract_number: contractNumber,
    },
  });
  const profession = (await getValueCodeByCodeType("PROFESSION")) || [];
  representations = representations.map((item) => {
    return {
      ...item,
      position: `${profession.find((p) => p.code === item.position)?.value || ""}`,
      key: `parent_${item?.id}`,
    };
  });
  const representationLenderModel = await BizziLoanLender.init({
    referenceNumber: contractNumber,
    data: representations,
    partnerCode,
    table: "loan_customer_representations",
  });
  const columns = await representationLenderModel.columnConfig;
  const data = representationLenderModel.extendData();
  return new SuccessResponse({ representations: data, columns: columns });
}

async function getLoanRepresentationDetail(contractNumber, id) {
  if (!id || !contractNumber) {
    throw new BadRequestResponseV2([], "id and contractNumber are required");
  }
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;

  const representation = await findOne({
    table: "loan_customer_representations",
    whereCondition: {
      id: id,
    },
  });
  if (!representation) {
    throw new BadRequestResponseV2([], "Representation not found for id: " + id);
  }

  const representationLenderModel = await BizziLoanLender.init({
    referenceNumber: id,
    data: representation,
    partnerCode,
    table: "loan_customer_representations",
  });

  const data = await representationLenderModel.bindingData({isSubInfo: true});

  return new SuccessResponse({
    info: data,
  });
}

async function getLoanCustomerManager(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  //return managers
  const managers = await find({
    table: "loan_customer_managers",
    whereCondition: {
      contract_number: contractNumber,
    },
  });

  return new SuccessResponse(managers);
}

async function getLoanCustomerManagerDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;

  const manager = await findOne({
    table: "loan_customer_managers",
    whereCondition: {
      contract_number: contractNumber,
    },
  });
  if (!manager) {
    throw new BadRequestResponseV2([], "Manager not found for id: " + id);
  }

  const managerLenderModel = await BizziLoanLender.init({
    referenceNumber: manager.id,
    data: manager,
    partnerCode,
    table: "loan_customer_managers",
  });

  const data = await managerLenderModel.bindingData({ isSubInfo: true });

  return new SuccessResponse({
    id: manager.id,
    info: data,
  });
}

/**
 * lay chu doanh nghiep va co dong
 */

const getLoanCustomerShareholders = async (contractNumber) => {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  let shareholders = await find({
    table: "loan_customer_shareholders",
    whereCondition: {
      contract_number: contractNumber,
    },
  });
    shareholders = shareholders.map((item) => ({
    ...item,
    key: `parent_${item?.id}`,
  }));
  //lender
  const shareholderLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: shareholders,
    partnerCode: loan.partner_code,
    table: "loan_customer_shareholders",
  });
  const columns = await shareholderLenderModel.columnConfig;

  return new SuccessResponse({
    shareholders: shareholderLenderModel.extendData(),
    columns: columns,
  });
};

/**
 * lay chu doanh nghiep va co dong
 */

const getLoanCustomerShareholdersDetails = async (contractNumber, id) => {
  if (!contractNumber || !id) {
    throw new BadRequestResponseV2([], "contractNumber and id are required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;

  const shareholder = await findOne({
    table: "loan_customer_shareholders",
    whereCondition: {
      contract_number: contractNumber,
      id: id,
    },
  });

  const shareholderLenderModel = await BizziLoanLender.init({
    referenceNumber: id,
    data: shareholder,
    partnerCode,
    table: "loan_customer_shareholders",
    groupType: shareholder.subject,
  });

  const data = await shareholderLenderModel.bindingData({isSubInfo: true});
  return new SuccessResponse({
    info: data,
  });
};

const getLoanCustomerPartners = async (contractNumber) => {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  let partners = await find({
    table: "loan_customer_partners",
    whereCondition: {
      contract_number: contractNumber,
    },
  });
  partners = partners.map((item) => ({
    ...item,
    key: `parent_${item?.id}`,
  }));

  const partnerLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: partners,
    partnerCode: loan.partner_code,
    table: "loan_customer_partners",
  });

  const columns = await partnerLenderModel.columnConfig;
  return new SuccessResponse({
    partners: partnerLenderModel.extendData(),
    columns: columns,
  });
};

//detail of partners
const getLoanCustomerPartnersDetails = async (contractNumber, id) => {
  if (!contractNumber || !id) {
    throw new BadRequestResponseV2([], "contractNumber and id are required");
  }
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;
  const partner = await findOne({
    table: "loan_customer_partners",
    whereCondition: {
      contract_number: contractNumber,
      id: id,
    },
  });
  if (!partner) {
    throw new BadRequestResponseV2([], "Partner not found for id: " + id);
  }
  const partnerLenderModel = await BizziLoanLender.init({
    referenceNumber: id,
    data: partner,
    partnerCode,
    table: "loan_customer_partners",
    // docs: documents
  });

  const data = await partnerLenderModel.bindingData({ isSubInfo: true });
  return new SuccessResponse({
    info: data,
  });
};

const processAf3Data = async (req, res) => {
  const body = req.body;
  const contract_number = body.contract_number;

  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const partnerCode = loan.partner_code;

  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract_document",
    groupType: "AF3",
  });

  const result = await loanLenderModel.af3process(body);
  if (result?.resubmit) {
    callbackPartner(contract_number, PARTNER_CODE.BZHM, STATUS.RESUBMIT_A3, null, null, null, { resubmitData: getResubmitData(contract_number, STATUS.RESUBMIT_A3) });
    return new SuccessResponse("Process data to resubmit. successfully");
  } else {
    await loanLenderModel.commitAF3ChangeRequest({ comment: body.comment || "Auto approve change request", createdBy: body.createdBy || "system" }, LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ);
    goNextStep(contract_number);
    return new SuccessResponse("Process data successfully");
  }
};

const processParentSubInfo = async (infos, contract_number) => {
  const parents = (Array.isArray(infos) ? infos : []).filter((element) => element?.key === `parent_${element?.id}` && !element?.status);
  if (parents.length === 0) return;
  await Promise.all(
    parents.map(async (element) => {
      let tempDetail;
      switch (element.table?.toUpperCase()) {
        case LENDER_REFERENCE_TYPE.REPRESENTATION:
          tempDetail = await getLoanRepresentationDetail(contract_number, element.id);
          break;
        case LENDER_REFERENCE_TYPE.SHAREHOLDER:
          tempDetail = await getLoanCustomerShareholdersDetails(contract_number, element.id);
          break;
        case LENDER_REFERENCE_TYPE.PARTNER:
          tempDetail = await getLoanCustomerPartnersDetails(contract_number, element.id);
          break;
        case LENDER_REFERENCE_TYPE.OWNER:
          tempDetail = await getLoanCustomerOwnersDetails(contract_number, element.id);
          break;
      }
      const _subInfo = tempDetail?.data?.info;
      if (Array.isArray(_subInfo) && _subInfo.length > 0) {
        await processSubInfo({
          contract_number,
          sub_type: element.table?.toUpperCase(),
          reference_id: element.id,
          info: _subInfo.map((item) => ({ ...item, status: false })),
        });
      }
    })
  );
};

const processData = async (req, res) => {
  const body = req.body;
  const contract_number = body.contract_number;
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  if (loan.status != STATUS.IN_MANUAL_REVIEW_A2) {
    throw new BadRequestResponseV2([], "Loan is not in manual review status");
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
  });
  await processParentSubInfo(body?.info, contract_number);
  const result = await loanLenderModel.process(body);

  if (result.resubmit) {
    const payload = await getLoanAf2FormData(loan.contract_number);
    const resubmitAf2FormData = await getResubmitAf2FormData(loan.contract_number, payload);
    // callback to bizzi partner
    callbackPartner(loan.contract_number, partnerCode, STATUS.RESUBMIT_A2, null, null, null, {
      resubmitData: resubmitAf2FormData,
    });
    return new SuccessResponse(resubmitAf2FormData, "Process data successfully");
    // return new SuccessResponse({ payload, resubmitAf2FormData }, "Process data successfully");
  } else {
    //go next step
    await loanLenderModel.commitChangeRequest(
      {
        comment: body.comment || "Auto approve change request",
        createdBy: body.createdBy || "system",
      },
      LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ
    );
    goNextStep(contract_number);
  }
  return new SuccessResponse(result, "Process data successfully");
};

const processSubInfo = async ({ contract_number, sub_type, reference_id, info, request_id }) => {
  if (!contract_number || !sub_type || !reference_id || !info) {
    throw new BadRequestResponseV2([], `contract_number, sub_type, reference_id and info are required`);
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const partnerCode = loan.partner_code;
  let table = "";
  let dto = null;
  switch (sub_type) {
    case LENDER_REFERENCE_TYPE.REPRESENTATION:
      table = LENDER_REFERENCE_TYPE.REPRESENTATION.toLowerCase();
      dto = await findOne({
        table: "loan_customer_representations",
        whereCondition: {
          id: reference_id,
        },
      });
      break;
    case LENDER_REFERENCE_TYPE.MANAGER:
      table = LENDER_REFERENCE_TYPE.MANAGER.toLowerCase();
      dto = await findOne({
        table: "loan_customer_managers",
        whereCondition: {
          id: reference_id,
        },
      });
      break;
    case LENDER_REFERENCE_TYPE.SHAREHOLDER:
      table = LENDER_REFERENCE_TYPE.SHAREHOLDER.toLowerCase();
      dto = await findOne({
        table: "loan_customer_shareholders",
        whereCondition: {
          id: reference_id,
        },
      });
      break;
    case LENDER_REFERENCE_TYPE.PARTNER:
      table = LENDER_REFERENCE_TYPE.PARTNER.toLowerCase();
      dto = await findOne({
        table: "loan_customer_partners",
        whereCondition: {
          id: reference_id,
        },
      });
      break;
    case LENDER_REFERENCE_TYPE.OWNER:
      table = LENDER_REFERENCE_TYPE.OWNER.toLowerCase();
      dto = await findOne({
        table: "loan_business_owner",
        whereCondition: {
          id: reference_id,
        },
      });
      break;
    case LENDER_REFERENCE_TYPE.WAREHOUSE:
      table = LENDER_REFERENCE_TYPE.WAREHOUSE.toLowerCase();
      dto = await findOne({
        table: "loan_customer_warehouses",
        whereCondition: {
          id: reference_id,
        },
      });
      break;
    case LENDER_REFERENCE_TYPE.BRANCH:
      table = LENDER_REFERENCE_TYPE.BRANCH.toLowerCase();
      dto = await findOne({
        table: "loan_branch_address",
        whereCondition: {
          id: reference_id,
        },
      });
      break;
    default:
      throw new BadRequestResponseV2(
        [],
        `sub_type ${sub_type} is not supported 
        [${LENDER_REFERENCE_TYPE.REPRESENTATION}, ${LENDER_REFERENCE_TYPE.MANAGER}, 
        ${LENDER_REFERENCE_TYPE.SHAREHOLDER}, ${LENDER_REFERENCE_TYPE.PARTNER}, ${LENDER_REFERENCE_TYPE.OWNER},
        ${LENDER_REFERENCE_TYPE.WAREHOUSE}, ${LENDER_REFERENCE_TYPE.BRANCH}]`
      );
  }
  const lenderModel = await BizziLoanLender.init({
    referenceNumber: reference_id,
    data: dto,
    partnerCode,
    table: table,
  });
  await lenderModel.process({
    contractNumber: contract_number,
    requestId: request_id || null,
    requestType: LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS,
    referenceCode: reference_id,
    referenceType: sub_type,
    info: info,
  });
  return new SuccessResponse("Process sub info successfully");
};

const approveChangeRequest = async (req, res) => {
  const body = req.body;
  const { contract_number } = body;
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (loan.status !== STATUS.WAITING_APPROVE_CHANGE_REQUEST && loan.status !== STATUS.IN_MANUAL_REVIEW_A2) {
    throw new BadRequestResponseV2([], "Loan is not in waiting approve change request status");
  }

  //parse data to loan contract
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode: loan.partner_code,
    table: "loan_contract",
  });
  await loanLenderModel.commitChangeRequest(body, LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ);
  //di tiếp workflow
  goNextStep(contract_number);
  return new SuccessResponse({}, "Change request approved successfully");
};

const rejectChangeRequest = async (req, res) => {
  const body = req.body;
  const { contract_number } = body;
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (loan.status !== STATUS.WAITING_APPROVE_CHANGE_REQUEST && loan.status !== STATUS.IN_MANUAL_REVIEW_A2) {
    throw new BadRequestResponseV2([], "Loan is not in waiting approve change request status");
  }

  //parse data to loan contract
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode: loan.partner_code,
    table: "loan_contract",
  });
  await loanLenderModel.commitChangeRequest(body, LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_CHANGE_REQ);
  return new SuccessResponse({}, "Change request rejected successfully");
};

const approveAF3ChangeRequest = async (req, res) => {
  const body = req.body;
  const { contract_number } = body;
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (loan.status !== STATUS.WAITING_APPROVE_AF3_CHANGE_REQUEST && loan.status !== STATUS.IN_MANUAL_PROCESS_A3) {
    throw new BadRequestResponseV2([], "Loan is not in waiting approve change request or MANUAL_PROCESS_A3 status");
  }

  //parse data to loan contract
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode: loan.partner_code,
    table: "loan_contract",
  });

  await loanLenderModel.commitAF3ChangeRequest(body, LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ);

  goNextStep(contract_number);
  return new SuccessResponse({}, "Change request approved successfully");
};

const rejectAF3ChangeRequest = async (req, res) => {
  const body = req.body;
  const { contract_number } = body;
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (loan.status !== STATUS.WAITING_APPROVE_AF3_CHANGE_REQUEST && loan.status !== STATUS.IN_MANUAL_PROCESS_A3) {
    throw new BadRequestResponseV2([], "Loan is not in waiting approve change request status");
  }

  //parse data to loan contract
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode: loan.partner_code,
    table: "loan_contract",
  });
  await loanLenderModel.commitAF3ChangeRequest(body, LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_AF3_CHANGE_REQ);
  return new SuccessResponse({}, "Change request rejected successfully");
};

const getLoanChangeRequestHistory = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const history = await find({
    table: "lender_change_request",
    whereCondition: {
      reference_code: loan.contract_number,
      // reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT,
    },
    orderBy: {
      id: "DESC",
    },
  });
  const result = history.map((item) => ({
    id: item.id,
    status: item.status,
    created_date: item.created_at,
    action_by: item.action_by,
    created_by: item.created_by,
    comment: item.comment,
    updated_at: item.updated_at,
    request_type: item.request_type,
    reference_code: item.reference_code,
    reference_type: item.reference_type,
    action: mappingHistoryAction(item.request_type),
  }));
  return new SuccessResponse(result, "Get change request history successfully");
};

const mappingHistoryAction = (request_type) => {
  switch (request_type) {
    case LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ:
      return "Duyệt hồ sơ AF2";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_CHANGE_REQ:
      return "Từ chối hồ sơ AF2";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS:
      return "Xử lý thông tin bổ sung cho đối tượng phụ thuộc";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_PROCESS:
      return "Yêu cầu chỉnh sửa AF2";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ:
      return "Duyệt hồ sơ AF3";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_AF3_CHANGE_REQ:
      return "Từ chối hồ sơ AF3";
    case LENDER_REQUEST_TYPE.LENDER_LOAN_RESUBMIT_AF3_CHANGE_REQ:
      return "Yêu cầu chỉnh sửa AF3";
    default:
      return "unknown";
  }
};

const getLoanRequestRelated = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const selectKey = ["id", "request_id", "partner_code", "contract_number", "status", "registration_number", "tax_id", "sme_name", "created_date", "channel", "updated_date"];

  const relatedLoans = await find({
    table: "loan_contract",
    whereCondition: {
      // sme_tax_id: loan.sme_tax_id,
      cust_id:loan.cust_id
    },
    select: selectKey,
  });
  return new SuccessResponse(relatedLoans.filter((item) => item.contract_number !== contract_number));
};

const getLoanRequestFinanceModel = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const modelData = await findOne({
    table: "loan_rating",
    whereCondition: {
      contract_number: contract_number,
    },
  });
  const rating = JSON.parse(modelData?.detail_data || "{}");
  return new SuccessResponse(await mappingModelData(rating));
};

const mappingModelData = async (modelData) => {
  if (!modelData) {
    return {};
  }
  const customer_rank = [
    {
      rank: modelData.rank ,
      score: modelData.avg_score ,
    },
  ];
  const model_info = [
    {
      key: "avg_score",
      name: "Tổng điểm khách hàng",
      value: modelData.avg_score ?? "",
    },
    {
      key: "rank",
      name: "Hạng khách hàng",
      value: modelData.rank ?? "",
    },
    {
      key: "max_limit",
      name: "Hạn mức tối đa theo hạng khách hàng",
      value: modelData.max_limit ?? "",
    },
    {
      key: "evf_shortfall",
      name: "Vốn cần EVNFC tài trợ dự phóng năm N",
      value: modelData.evf_shortfall ?? "",
    },
    {
      key: "request_limit",
      name: "Nhu cầu vốn khách hàng khai báo",
      value: modelData.request_limit ?? "",
    },
    {
      key: "max_limit_by_revenue",
      name: "Hạn mức cho vay tối đa theo cấu hình sản phẩm ",
      value: modelData.max_limit_by_revenue ?? "",
    },
    {
      key: "equity_lt_debt_lt_assets",
      name: "Vốn chủ sở hữu + Nợ dài hạn - Tài sản dài hạn",
      value: modelData.equity_lt_debt_lt_assets ?? "",
    },
    {
      key: "product_code",
      name: "Phân khúc khách hàng",
      value: modelData.product_code ?? "",
    },
    {
      key: "approved_limit",
      name: "Hạn mức phê duyệt",
      value: modelData.approved_limit ?? "",
    },
    {
      key: "approved_interest_rate",
      name: "Lãi suất",
      value: modelData.approved_interest_rate ?? "",
    },
  ];
  const finance_metric_details = await mappingFinanceMetric(modelData.finance_metric_details);
  const finance_metric = [
    {
      name: "Tổng chỉ tiêu tài chính",
      ratio: 70,
      score: modelData.total_financial_indicators || null,
    },
  ];
  const non_finance_metric_details = await mappingFinanceMetric(modelData.non_finance_metric_details);

  const non_finance_metric = [
    {
      name: "Tổng chỉ tiêu phi tài chính",
      ratio: 30,
      score: modelData.total_non_financial_indicators || null,
    },
  ];

  return {
    customer_rank: customer_rank,
    model_info: model_info,
    finance_metric_details: finance_metric_details,
    finance_metric: finance_metric,
    non_finance_metric_details: non_finance_metric_details,
    non_finance_metric: non_finance_metric,
  };
};

const mappingFinanceMetric = async (metricDetails) => {
  const result_metric_details = [];
  for (const key in metricDetails) {
    const { name, type, masterdata, order } = mappingMetricName(key);
    let metric_score = metricDetails[key].metric_score;
    if (masterdata) {
      const masterdataValue = await getValueCodeMasterdataV2(metric_score, masterdata);
      metric_score = masterdataValue?.nameVn || metric_score;
    } else if (type === BIZZ_MODEL_DATA_TYPE.percent) {
      metric_score = Number(metric_score || 0) * 100;
    }
    result_metric_details.push({
      code: key,
      score: metricDetails[key].score,
      name: name,
      weight_score: metricDetails[key].weight_score,
      ratio: Number(metricDetails[key].ratio || 0) * 100,
      metric_score: metric_score,
      type: type,
      order: order || 99,
    });
  }

  //order by order field asc, if not have order field, put to the end
  result_metric_details.sort((a, b) => a.order - b.order);
  return result_metric_details;
};

const mappingMetricName = (metricCode) => {
  const metricDetails = {
    TC01: { name: "Khả năng thanh toán hiện hành", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:1 },
    TC02: { name: "Khả năng thanh toán nhanh", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:2 },
    TC03: { name: "Vòng quay hàng tồn kho", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:3 },
    TC04: { name: "Vòng quay vốn lưu động", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:4 },
    TC05: { name: "Hệ số nợ vay trên tổng tài sản", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:5 },
    TC06: { name: "Hệ số nợ vay trên vốn chủ sở hữu", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:6 },
    TC07: {
      name: "Tỷ suất sinh lợi nhuận sau thuế trên doanh thu (ROS)",
      type: BIZZ_MODEL_DATA_TYPE.number,
      masterdata: null,
      order:7
    },
    TC08: {
      name: "Tỷ suất sinh lợi nhuận trên vốn chủ sở hữu (ROE)",
      type: BIZZ_MODEL_DATA_TYPE.number,
      masterdata: null,
      order:8
    },
    TC09: { name: "Tỷ suất sinh lợi của tài sản (ROA)", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:9 },
    TC10: { name: "Tỷ lệ tăng trưởng doanh thu", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:10 },
    TC11: { name: "Tỷ lệ tăng trưởng lợi nhuận", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:11 },
    PTC01: { name: "Lưu chuyển tiền tệ (VND)", type: BIZZ_MODEL_DATA_TYPE.currency, masterdata: null, order:12 },
    PTC02: {
      name: "Kinh nghiệm quản lý của người đại diện pháp luật",
      type: BIZZ_MODEL_DATA_TYPE.string,
      masterdata: "MANAGEMENT_EXPERIENCE",
      order:1
    },
    PTC03: { name: "Lịch sử tín dụng", type: BIZZ_MODEL_DATA_TYPE.string, masterdata: null, order:2 },
    PTC04: { name: "Quy mô vốn kinh doanh (VND)", type: BIZZ_MODEL_DATA_TYPE.currency, masterdata: null, order:3 },
    PTC05: { name: "Quy mô lao động (người)", type: BIZZ_MODEL_DATA_TYPE.number, masterdata: null, order:4 },
    PTC06: { name: "Ngành nghề kinh doanh có điều kiện", type: BIZZ_MODEL_DATA_TYPE.string, masterdata: null, order:5 },
    PTC07: { name: "Thời gian hoạt động (tháng)", type: BIZZ_MODEL_DATA_TYPE.month, masterdata: null, order:6 },
    PTC08: {
      name: "Triển vọng ngành (theo tỷ lệ tăng trưởng doanh thu)",
      type: BIZZ_MODEL_DATA_TYPE.percent,
      masterdata: null,
      order:7
    },
    PTC09: { name: "Thời gian sử dụng phần mềm (tháng)", type: BIZZ_MODEL_DATA_TYPE.month, masterdata: null, order:8 },
  };

  return metricDetails[metricCode] || "Unknown Metric";
};

async function getLoanAf2FormData(contract_number) {
  const loan = await loanContractRepo.getLoanContract(contract_number);
  // Build AF2 form data from loan object
  const shareholders = await loanShareholdersRepo.findByContractNumber(contract_number);
  const docs = await documentRepo.getDocumentByContractNumber(contract_number);
  const managers = await loanContractRepo.getLoanCustomerManagerByContractNumber({ contract_number });
  const branches = await loanContractRepo.getBranchAddresses(contract_number);
  const representations = await loanRepresentationsRepo.getRepresentationsByCustomer(contract_number);
  const businessOwner = (await loanBusinessOwnerRepo.findByContractNumber(contract_number))?.[0];
  const af2FormData = {
    request_id: loan.request_id,
    contract_number: loan.contract_number,
    tax_id: loan.sme_tax_id,
    partner_code: loan.partner_code,
    loan_purpose: loan.loan_purpose,
    total_turn_over_next_year: loan.total_turnover_next_year,
    total_cost_over_next_year: loan.total_cost_over_next_year,
    profit_before_tax_next_year: loan.profit_before_tax_next_year,
    capital_need: loan.capital_need,
    loan_amount: loan.loan_amount,
    loans_other_financial_institutions: loan.loans_other_financial_institutions,
    owner_equity: loan.owner_equity,
    other_capital: loan.other_capital,
    tenor: loan.tenor,
    tenor_per_kunn: loan.tenor_per_kunn,
    repayment_method: loan.repayment_method,
    repayment_sources: loan.repayment_sources,
    registration_number: loan.registration_number,
    company_name: loan.sme_name,
    address_on_license: loan.address_on_license,
    registration_cert_issue_date: loan.registration_cert_issue_date,
    sme_headquarters_province: loan.sme_headquarters_province,
    sme_headquarters_ward: loan.sme_headquarters_ward,
    sme_hHeadquarters_address: loan.sme_hHeadquarters_address,
    registration_cert_issue_place: loan.registration_cert_issue_place,
    charter_capital: loan.charter_capital,
    business_type: loan.business_type,
    business_industry: loan.business_industry,
    conditional_business_industry: loan.conditional_business_industry,
    loan_purpose_detail: loan.loan_purpose_detail,
    number_of_staffs: loan.number_of_staffs,
    company_phone_number: loan.company_phone_number,
    company_email: loan.company_email,
    company_website: loan.company_website,
    branches: branches?.map((branch) => ({
      branch_name: branch.branch_name,
      province_code: branch.province,
      ward_code: branch.ward,
      detail: branch.address,
      docs: [],
    })),
    warehouses: [],
    other_docs: [],
    representations:
      representations?.map((rep) => ({
        full_name: rep.full_name,
        position: rep.position,
        dob: rep.dob,
        id: rep.id,
        issue_date: rep.issue_date,
        issue_place: rep.issue_place,
        phone_number: rep.phone_number,
        email: rep.email,
        per_address: {
          province_code: rep.per_province_code,
          ward_code: rep.per_ward_code,
          detail: rep.per_detail_address,
        },
        docs: docs
          .filter((doc) => doc.reference_table == "loan_customer_representations" && doc.reference_id === String(rep.id))
          .map((doc) => ({
            doc_id: doc.doc_id,
            doc_type: doc.doc_type,
          })),
      })) || [],
    bank_code: loan.bank_code,
    bank_branch_code: loan.bank_branch_code,
    bank_account: loan.bank_account,
    business_owner: !businessOwner
      ? null
      : {
          subject: businessOwner?.subject,
          tax_id: businessOwner?.tax_id,
          company_name: businessOwner?.companyName,
          married_status: businessOwner?.marriedStatus,
          partner: businessOwner?.partner,
          full_name: businessOwner?.fullName, // Note: original field is "full_nName"
          position: businessOwner?.position,
          dob: businessOwner?.dob,
          id: businessOwner?.id,
          issue_date: businessOwner?.issueDate,
          issue_place: businessOwner?.issuePlace,
          phone_number: businessOwner?.phoneNumber,
          email: businessOwner?.email,
          per_address: {
            province_code: businessOwner?.perProvinceCode,
            ward_code: businessOwner?.perWardCode,
            detail: businessOwner?.perDetailAddress,
          },
          cur_address: {
            province_code: businessOwner?.curProvinceCode,
            ward_code: businessOwner?.curWardCode,
            detail: businessOwner?.curDetailAddress,
          },
          docs: docs
            .filter((doc) => doc.reference_table == "loan_business_owner" && doc.reference_id === String(businessOwner.id))
            .map((doc) => ({
              doc_id: doc.doc_id,
              doc_type: doc.doc_type,
            })),
        },
    shareholders: {
      members:
        shareholders?.map((sh) => ({
          subject: sh.subject,
          full_name: sh.fullName,
          identity_type: sh.identityType,
          id: sh.id,
          issue_date: sh.issueDate,
          issue_place: sh.issuePlace,
          capital_contribution_ratio: sh.capitalContributionRatio,
          phone_number: sh.phoneNumber,
          email: sh.email,
          per_address: {
            province_code: sh.perProvinceCode,
            ward_code: sh.perWardCode,
            detail: sh.perDetailAddress,
          },
          cur_address: {
            province_code: sh.curProvinceCode,
            ward_code: sh.curWardCode,
            detail: sh.curDetailAddress,
          },
          tax_id: sh.taxId,
          company_name: sh.companyName,
          representations: sh.representations,
          docs: docs
            .filter((doc) => doc.reference_table == "loan_shareholders" && doc.reference_id === String(sh.id))
            .map((doc) => ({
              doc_id: doc.doc_id,
              doc_type: doc.doc_type,
            })),
        })) ?? [],
    },
    last_3_month_sales_anchor: loan.last_3_month_sales_anchor,
    anchor_contract_number: loan.anchor_contract_number,
    anchor_contract_number_date: loan.anchor_contract_number_date,
    managers:
      managers?.map((mgr) => ({
        full_name: mgr.full_name,
        identity_type: mgr.id_type,
        id: mgr.id,
        dob: mgr.dob,
        position: mgr.position,
        issue_date: mgr.issue_date,
        issue_place: mgr.issue_place,
        capital_contribution_ratio: mgr.capital_contribution_ratio,
        phone_number: mgr.phone_number,
        email: mgr.email,
        management_experience: mgr.management_experience,
        per_address: {
          province_code: mgr.per_province_code,
          ward_code: mgr.per_ward_code,
          detail: mgr.per_detail_address,
        },
        cur_address: {
          province_code: mgr.cur_province_code,
          ward_code: mgr.cur_ward_code,
          detail: mgr.cur_detail_address,
        },
        tax_id: mgr.tax_id,
        company_name: mgr.company_name,
        representations: mgr.representations,
        docs: docs
          .filter((doc) => doc.reference_table == "loan_managers" && doc.reference_id === String(mgr.id))
          .map((doc) => ({
            doc_id: doc.doc_id,
            doc_type: doc.doc_type,
            bundleNameVi: doc.bundleNameVi,
          })),
      })) ?? [],
  };
  let revenueDocsRaw = await loanRevenuesRepo.findRevenueDocuments(contract_number);
  // Group revenueDocs by year field
  let revenueDocs = revenueDocsRaw.reduce((acc, doc) => {
    if (!doc.year) return acc;

    let yearDoc = acc.find((x) => x.year === doc.year);
    if (!yearDoc) {
      acc.push({
        id: doc.id,
        year: doc.year,
        financial_report_docs: [
          {
            doc_id: doc.evf_doc_id,
            doc_type: doc.doc_type,
          },
        ],
      });
    } else {
      yearDoc.financial_report_docs.push({
        doc_id: doc.doc_id,
        doc_type: doc.doc_type,
      });
    }
    return acc;
  }, []);
  let loanVatForms = await loanVatFormsRepo.findByContractNumberWithDocs(contract_number);
  af2FormData.financial_information = {
    revenues: revenueDocs,
    vat_forms: loanVatForms?.map((item) => ({
      id: item.id,
      period: item.period,
      name: item.name,
      docs: [
        {
          doc_id: item.file_url?.match(/[?&]docId=([^&]+)/)?.[1],
          doc_type: item.doc_type,
        },
      ],
    })),
  };
  return af2FormData;
}

async function getResubmitAf2FormData(contract_number, payload) {
  const changeRequests = await lenderChangeRequestRepo.getChangeRequestDetailByContract({
    contract_number,
    shareholderIds: payload.shareholders?.members?.map((x) => x.id),
    repIds: payload.representations?.map((x) => x.id),
    managerIds: payload.managers?.map((x) => x.id),
    revenueIds: payload.financial_information?.revenues?.map((x) => x.id),
    vatFormIds: payload.financial_information?.vat_forms?.map((x) => x.id),
  });
  const lenderColumnConfig = await lenderColumnConfigRepo.getLenderColumnConfig({ partnerCode: PARTNER_CODE.BZHM });
  const form = {};
  for (const element of changeRequests) {
    if (element.status) continue;
    let config = lenderColumnConfig.find((item) => item.key === element.key && item.table_name?.toUpperCase() === element.ref_table);
    if (element.ref_table === "LOAN_CONTRACT") {
      if (config?.type === "file") {
        // form[element.key] ??= {
        //   value: JSON.parse(element.old_value),
        //   comment: element.comment,
        // };
      } else if (config) {
        form[af2PartnerMapping[element.key] ?? element.key] ??= {
          value: element.old_value,
          comment: element.comment,
        };
      }
    } else if (element.ref_table === REF_TABLE.SHAREHOLDER) {
      form.shareholders ??= { members: payload.shareholders?.members?.map((x) => ({ id: x.id, docs: x.docs })) || [] };
      if (form.shareholders?.members) {
        const shareholder = form.shareholders.members.find((item) => String(item.id) == element.reference_code);
        if (config?.type === "file") {
          let doc = shareholder.docs?.find((doc) => doc.doc_type === element.key);
          if (doc) doc.comment = element.comment;
        } else if (config) {
          shareholder[element.key] ??= {
            value: element.old_value,
            comment: element.comment,
          };
        }
      }
    } else if (element.ref_table === REF_TABLE.REPRESENTATION) {
      form.representations ??= payload.representations?.map((x) => ({ id: x.id, docs: x.docs }));
      if (form.representations) {
        const shareholder = form.representations.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          let doc = shareholder.docs?.find((doc) => doc.doc_type === element.key);
          if (doc) doc.comment = element.comment;
        } else if (config) {
          shareholder[element.key] ??= {
            value: element.old_value,
            comment: element.comment,
          };
        }
      }
    } else if (element.ref_table === REF_TABLE.MANAGER) {
      form.managers ??= payload.managers?.map((x) => ({ id: x.id, docs: x.docs }));
      if (form.managers) {
        const manager = form.managers.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          let doc = manager.docs?.find((doc) => doc.doc_type === element.key);
          if (doc) doc.comment = element.comment;
        } else if (config) {
          manager[element.key] ??= {
            value: element.old_value,
            comment: element.comment,
          };
        }
      }
    } else if (element.ref_table === REF_TABLE.REVENUE) {
      form.financial_information ??= { ...payload.financial_information };
      if (form.financial_information?.revenues) {
        const revenue = form.financial_information.revenues.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          revenue?.financial_report_docs?.forEach((doc) => {
            doc.comment = element.comment;
          });
        } else if (config) {
          revenue[element.key] ??= {
            value: element.old_value,
            comment: element.comment,
          };
        }
      }
    } else if (element.ref_table === REF_TABLE.VAT_FORM) {
      form.financial_information ??= { ...payload.financial_information };
      if (form.financial_information?.vat_forms) {
        const vatForm = form.financial_information.vat_forms.find((item) => item.id == element.reference_code);
        if (config?.type === "file") {
          vatForm?.docs?.forEach((doc) => {
            doc.comment = element.comment;
          });
        } else if (config) {
          vatForm[element.key] ??= {
            value: element.old_value,
            comment: element.comment,
          };
        }
      }
    }
  }
  return form;
}

function mergeIds(srcObj, destObj) {
  if (!srcObj || !destObj) return;
  if (Array.isArray(destObj) && Array.isArray(srcObj)) {
    for (let i = 0; i < destObj.length; i++) {
      mergeIds(srcObj[i], destObj[i]);
    }
  } else {
    if (srcObj.id) {
      destObj.id = srcObj.id;
    }
    for (const key in destObj) {
      if (typeof destObj[key] === "object" && typeof srcObj[key] === "object") {
        mergeIds(srcObj[key], destObj[key]);
      }
    }
  }
}

const getLoanCustomerOwners = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  let owners = await find({
    select: ["id", "contract_number", "subject", "full_name", "email", "id_number", "company_name, tax_id"],
    table: "loan_business_owner",
    whereCondition: {
      contract_number: contract_number,
    },
  });

  owners = owners.map((item) => ({
    ...item,
    key: `parent_${item?.id}`,
  }));

  const ownerLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: owners,
    partnerCode: loan.partner_code,
    table: "loan_business_owner",
  });
  const columns = await ownerLenderModel.columnConfig;
  return new SuccessResponse({
    owners: ownerLenderModel.extendData(),
    columns: columns,
  });
};

const getLoanCustomerOwnersDetails = async (contract_number, id) => {
  if (!contract_number || !id) {
    throw new BadRequestResponseV2([], "contract_number and id are required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const partnerCode = loan.partner_code;
  const owner = await findOne({
    table: "loan_business_owner",
    whereCondition: {
      contract_number: contract_number,
      id: id,
    },
  });
  if (!owner) {
    throw new BadRequestResponseV2([], "Owner not found for id: " + id);
  }

  const ownerLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: owner,
    partnerCode,
    table: "loan_business_owner",
    groupType: owner.subject,
    // docs: documents
  });
  const data = await ownerLenderModel.bindingData({ isSubInfo: true });
  return new SuccessResponse({
    info: data,
  });
};

async function getLoanRequestCommonDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  //sua lay nhung docs can thiet
  // const documents = await documentRepo.findByContractAndTypes(contractNumber, ["SBIZ", "SPCB", "SCR", "STCRC", "SLOCH", "SDACA", "SPCHQ", "SMRCO"]);

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_COMMON,
  });
  // thong tin tab nhu cau vay von
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getCustomerInfoDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "id, partner_code, cust_full_name, birth_date, gender, id_number, id_issue_dt, id_issue_place, phone_number1, phone_number2, phone_number3, email, married_status, new_province_per, new_commune_per";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_CUST_INFO,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getPermanentAddress(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "id, partner_code, new_province_per, new_commune_per, per_new_province_code, per_new_ward_code";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_PERMANENT_ADDRESS,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getCurrentAddress(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "id, partner_code, new_province_cur, new_commune_cur, cur_new_province_code, cur_new_ward_code";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_CURRENT_ADDRESS,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getSpouseDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const loanCustomer = await loanCustomerRepo.getLoanCustomer(contractNumber);
  if (!loanCustomer) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, "partner_code");
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loanCustomer.contract_number,
    data: loanCustomer,
    partnerCode: loan.partner_code,
    table: "loan_customer",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_SPOUSE,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getReferenceDetails1(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "id, partner_code, reference_type_1, reference_name_1, reference_phone_1, reference_id_number_1";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_REFERENCE1,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getReferenceDetails2(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "id, partner_code, reference_type_2, reference_name_2, reference_phone_2, reference_id_number_2";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_REFERENCE2,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getProposalDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "id, partner_code, loan_purpose, request_tenor, repayment_method, repayment_sources, income_date, total_turnover_next_year, cost_next_year, pre_tax_profit_next_year, capital_need, request_amt, owner_equity";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_LOAN_PROPOSAL,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getBusinessOperations(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "id, partner_code, business_data";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: JSON.parse(loan?.business_data) || {},
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_BUSINESS_OPERATIONS,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

async function getBusinessesInfoDetails(contractNumber) {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }

  const fieldQuery = "id, partner_code, sme_name, registration_number, sector_industry, conditional_business_industry, sme_phone_number, company_website, sme_headquarters_new_province, sme_headquarters_new_ward";
  const loan = await loanContractRepo.getContractCustomFieldByContractNumber(contractNumber, fieldQuery);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }

  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_BUSINESSES_INFO,
  });
  const loanModel = await loanLenderModel.bindingData();

  return new SuccessResponse({
    info: loanModel,
  });
}

const getLoanRequestFinanceInfoDetails = async (contractNumber) => {
  if (!contractNumber) {
    throw new BadRequestResponseV2([], "contractNumber is required");
  }
  const loan = await loanContractRepo.getLoanContract(contractNumber);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contractNumber);
  }
  const partnerCode = loan.partner_code;
  const loanLenderModel = await BizziLoanLender.init({
    referenceNumber: loan.contract_number,
    data: loan,
    partnerCode,
    table: "loan_contract",
    groupType: LENDER_CONFIG_GROUP_TYPE.AF2_FINANCE_INFO,
  });
  // thong tin tab nhu cau vay von
  const loanModel = await loanLenderModel.bindingData();
  return new SuccessResponse({
    info: loanModel,
  });
};

const getContractStatus = async (req, res) => {
  try {
    const { contract_number } = req.params;
    let files = [];
    let resubmit_data = [];

    if (!contract_number) {
      throw new BadRequestResponse("Missing contract_number");
    }
    let loan = await loanContractRepo.findByContractNumber({
      contractNumber: contract_number,
      partnerCode: PARTNER_CODE.BZHM,
    });

    if (!loan) {
      throw new BadRequestResponse("Loan not found");
    }

    switch (loan.status) {
      case STATUS.APPROVED:
        break;
      case STATUS.RESUBMIT_A2:
        resubmit_data = await getResubmitData(contract_number, STATUS.RESUBMIT_A2);
        break;
      case STATUS.RESUBMIT_A3:
        files = await getResubmitData(contract_number, STATUS.RESUBMIT_A3);
        break;
      default:
        break;
    }

    return new SuccessResponse({
      contract_number: loan.contract_number,
      files,
      resubmit_data,
      status: mappingStatus(loan.status),
    });
  } catch (error) {
    console.error(error);
    if (error instanceof BadRequestResponse) {
      throw error;
    }
    throw new ServerErrorResponse("Error fetching contract status");
  }
};

const getResubmitData = async (contract_number, status) => {
  let result = "";

  if (status == STATUS.RESUBMIT_A2) {
    const payload = await getLoanAf2FormData(contract_number);
    result = await getResubmitAf2FormData(contract_number, payload);
  } else if (status == STATUS.RESUBMIT_A3) {
    let change_request = await lenderChangeRequestRepo.getLatestLenderChangeRequest({
      request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_AF3_PROCESS,
      reference_code: contract_number,
      reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT,
      status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
    });

    if (change_request && change_request.request_body) {
      const doc_types = change_request?.request_body?.info?.map((entry) => {
        if (entry.type === "file") return entry.key;
      });
      const docs = await documentRepo.findByContractAndTypes(contract_number, doc_types);
      result = await s3Service.genMultiplePresigned(docs);
    }
  }

  return result;
};

const mappingStatus = (status) => {
  switch (status) {
    case STATUS.APPROVED:
    case STATUS.SIGNING_IN_PROGRESS:
    case STATUS.WAITING_CIC_RESULT:
      return "A2_PROCESSING";
    default:
      return status;
  }
};

const getLoanRequestRelatedDocs = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const documents = await find({
    table: "loan_contract_document",
    whereCondition: {
      contract_number: contract_number,
      owner_id: OWNER_ID.EVF,
      is_deleted: 0,
    },
    select: ["id", "doc_type", "file_key", "doc_id", "file_name", "url", "creation_time as created_date"],
    orderBy: {
      created_date: "DESC",
    },
  });
  return new SuccessResponse(documents || []);
};

const handleCallbackPartner = async (contract_number) => {
  let callbackStatus = STATUS.WAITING_CUSTOMER_SIGNATURE;

  const docs = await documentRepo.findByContractAndTypes(contract_number, AF3_TEMPLATE_DOCTYPE_SIGNED);
  // const result = await s3Service.genMultiplePresigned(docs);

  callbackPartner(contract_number, PARTNER_CODE.BZHM, callbackStatus, undefined, undefined, null, { docs });
};

const deleteDocument = async (contract_number, doc_id) => {
  if (!doc_id) {
    throw new BadRequestResponseV2([], "doc_id is required");
  }
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  await documentRepo.softDeleteByContractNumberAndDocId(contract_number, doc_id);
  return new SuccessResponse({}, "Delete document successfully");
};

async function saveUploadDocument({ contract_number, files }) {
  if (!contract_number || !files || files.length === 0) {
    throw new BadRequestResponseV2([], "contract_number and files are required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found");
  }
  let promises = [];

  //check all files exist in S3
  const fileKeys = files.map((file) => file.file_key);
  const fileExists = await s3Service.checkS3FilesExist(fileKeys);
  if (!fileExists || typeof fileExists === "string") {
    throw new BadRequestResponseV2([fileExists], "Some files do not exist in S3");
  }

  let promiseDocType = [];
  promiseDocType = files.map((file) => {
    return getValueCodeMasterdataV2(file.doc_type, "DOCUMENT");
  });
  const results = await Promise.all(promiseDocType);

  // Check if any results are null or undefined
  for (let i = 0; i < results.length; i++) {
    if (!results[i]) {
      throw new BadRequestResponseV2([], `Invalid document type: ${files[i].doc_type}`);
    }
  }

  for (const file of files) {
    if (!file.file_key || !file.doc_type) {
      throw new BadRequestResponseV2([], "file_key and doc_type are required for each file");
    }
    //check if file already exists in loan contract documents
    const existingDoc = await documentRepo.findByContractDocTypeFileKey({ contract_number: contract_number, doc_type: file.doc_type, file_key: file.file_key });
    if (existingDoc) {
      throw new BadRequestResponseV2([file.file_key], `Document with type ${file.doc_type} and file key ${file.file_key} already exists for contract_number ${contract_number}`);
    }

    // Extract file name from file_key
    let fileKey = file.file_key;
    let fileName = fileKey ? fileKey.split("/").pop() : null;
    let document = {
      contract_number: contract_number,
      doc_type: file.doc_type,
      doc_id: uuid.v4(),
      owner_id: OWNER_ID.EVF,
      file_key: fileKey,
      file_name: fileName,
      request_id: uuid.v4(),
    };
    promises.push(documentRepo.insertLoanContractDocument(document));
  }
  const data = await Promise.all(promises);
  if (data.length === 0) {
    throw new BadRequestResponseV2([], "Failed to save documents");
  }
  const savedFiles = data.map((doc) => ({
    docType: doc.doc_type,
    fileKey: doc.file_key,
    fileName: doc.file_name,
    docId: doc.doc_id,
  }));
  return new SuccessResponse({ savedFiles }, "Save document successfully");
}

async function createRefund(payload) {
  const refund = await refundRepo.insert(payload);
  return new SuccessResponse(refund, "Refund created successfully");
}

async function findRefunds({ contract_number, cust_id, page, size, status, start_date, end_date }) {
  if (end_date) {
    end_date = new Date(end_date);
    end_date.setHours(23, 59, 59, 999);
  }
  const refunds = await refundRepo.search({
    contractNumber: contract_number,
    custId: cust_id,
    page: +page || 1,
    size: +size || 10,
    startDate: start_date ? new Date(start_date) : null,
    endDate: end_date,
    status,
  });
  return new SuccessResponse(refunds, "Refunds retrieved successfully");
}

async function getBankInfoByRefund(id) {
  const refund = await refundRepo.findById(id);
  if (!refund) {
    throw new BadRequestResponseV2([], "Refund not found");
  }
  const kunnData = await kunnRepo.getKunnData(refund.contract_number);
  if (!kunnData) {
    throw new BadRequestResponseV2([], "KUNN data not found for contract_number: " + refund.contract_number);
  }
  return new SuccessResponse(
    {
      beneficiary_name: kunnData.beneficiary_name,
      bank_code: kunnData.bank_code,
      bank_account: kunnData.bank_account,
      bank_branch_code: kunnData.bank_branch_code,
    },
    "Bank data retrieved successfully"
  );
}

async function getHistoryByRefund(id) {
  const refund = await refundRepo.findById(id);
  if (!refund) {
    throw new BadRequestResponseV2([], "Refund not found");
  }
  let output = [];
  if (refund.action === REFUND_ACTIONS.TRANSFER) {
    output = [
      {
        contract_number: refund.contract_number,
        amount: refund.amount,
        start_date: refund.start_date,
        end_date: refund.end_date,
        lpi: refund.lpi,
        int_amount: refund.int_amount,
        fee_amount: refund.fee_amount,
        paid_amount: refund.paid_amount,
        invoice_value: refund.invoice_value,
        refund_value: refund.refund_value,
        action: refund.message,
        status: REFUND_REQUEST_STATUS.TRANSFERRED,
        action_date: refund.action_date,
      },
    ];
  } else {
    const requests = await refundRepo.searchRequest({ refundId: refund.id });
    output = requests?.items?.map((request) => ({
      contract_number: refund.contract_number,
      amount: refund.amount,
      start_date: refund.start_date,
      end_date: refund.end_date,
      lpi: refund.lpi,
      int_amount: refund.int_amount,
      fee_amount: refund.fee_amount,
      paid_amount: refund.paid_amount,
      invoice_value: refund.invoice_value,
      refund_value: refund.refund_value,
      action: "Refund",
      status: request.status,
      action_date: request.action_date,
      bank_account: request.bank_account,
      bank_code: request.bank_code,
      bank_name: request.bank_name,
      branch_code: request.branch_code,
      transfer_bank_code: request.transfer_bank_code,
      transfer_account: request.transfer_account,
      transfer_bank_name: request.transfer_bank_name,
      beneficiary_name: request.beneficiary_name,
      note: request.note,
      reason: request.reason,
    }));
  }
  return new SuccessResponse(output, "Bank data retrieved successfully");
}

async function getListKunnByRefund(id) {
  const refund = await refundRepo.findById(id);
  if (!refund) {
    throw new BadRequestResponseV2([], "Refund not found");
  }
  const kunnList = await kunnRepo.searchActiveKunnByCustId(refund.cust_id);
  return new SuccessResponse(
    kunnList
      ?.filter((x) => x.kunn_id !== refund.contract_number)
      ?.map((x) => ({
        contract_number: x.kunn_id,
        amount: x.with_draw_amount,
        start_date: x.start_date,
        end_date: x.end_date,
      })),
    "KUNN list retrieved successfully"
  );
}

async function transferCase(payload) {
  const { refund_id, to_kunn } = payload;
  const refund = await refundRepo.findById(refund_id);
  if (!refund) {
    throw new BadRequestResponseV2([], "Refund not found");
  }
  if (refund.status !== REFUND_STATUS.NEW) {
    throw new BadRequestResponseV2([], "Refund status invalid");
  }
  console.log("Transferring refund:", refund);
  const result = await lmsService.transferCase({
    debtAckContractNumber: refund.contract_number,
    toDebtAckContractNumber: to_kunn,
    amt: refund.refund_value,
  });
  console.log("Transfer result:", result);
  if (result?.code == 0) {
    await refundRepo.update(refund_id, { status: REFUND_STATUS.SUCCESS, action: REFUND_ACTIONS.TRANSFER, message: `Transfer to ${to_kunn}` });
    return new SuccessResponse({ result }, "Refund transferred successfully");
  } else {
    throw new BadRequestResponseV2({ result }, "Failed to transfer refund");
  }
}

async function findRefundRequests({ contract_number, cust_id, page, size, status, start_date, end_date }) {
  const refundRequests = await refundRepo.searchRequest({
    contractNumber: contract_number,
    custId: cust_id,
    page: +page || 1,
    size: +size || 10,
    startDate: start_date ? new Date(start_date) : null,
    endDate: end_date ? new Date(end_date) : null,
    status,
  });
  return new SuccessResponse(refundRequests, "Refund requests retrieved successfully");
}

async function approveRefund(payload) {
  const { request_id } = payload;
  const refundRequest = await refundRepo.findRequestById(request_id);
  const refund = await refundRepo.findById(refundRequest.refund_id);
  if (!refund || !refundRequest) {
    throw new BadRequestResponseV2([], "Refund request not found");
  }
  if (refundRequest.status !== REFUND_REQUEST_STATUS.NEW) {
    throw new BadRequestResponseV2([], "Refund request status invalid");
  }
  console.log("Refunding money for request:", refund);
  const result = await lmsService.refundMoney({
    debtAckContractNumber: refund.contract_number,
    amt: refund.refund_value,
    beneficiaryName: refundRequest.beneficiary_name,
    bankCode: refundRequest.bank_code,
    bankName: refundRequest.bank_name,
    bankAccount: refundRequest.bank_account,
    branchCode: refundRequest.branch_code,
    transferBankCode: refundRequest.transfer_bank_code,
    transferAccount: refundRequest.transfer_account,
    note: refundRequest.note,
  });
  console.log("Refund result:", result);
  if (result?.code == 0) {
    await refundRepo.update(refund.id, { status: REFUND_STATUS.SUCCESS });
    await refundRepo.approveRequest(refundRequest.id);
    return new SuccessResponse({ result }, "Refund request approved successfully");
  } else {
    throw new BadRequestResponseV2({ result }, "Failed to approve refund request");
  }
}

async function rejectRefund(payload) {
  const { request_id, reason } = payload;
  const refundRequest = await refundRepo.findRequestById(request_id);
  const refund = await refundRepo.findById(refundRequest.refund_id);
  if (!refundRequest || !refund) {
    throw new BadRequestResponseV2([], "Refund request not found");
  }
  if (refundRequest.status !== REFUND_REQUEST_STATUS.NEW) {
    throw new BadRequestResponseV2([], "Refund request status invalid");
  }
  console.log("Rejecting refund request:", refundRequest);
  await refundRepo.rejectRequest(refundRequest.id, reason);
  return new SuccessResponse({}, "Refund request rejected successfully");
}

async function refundMoney(payload) {
  const { refund_id, beneficiary_name, bank_code, bank_account, bank_name, branch_code, transfer_bank_code, transfer_bank_name, transfer_account, note } = payload;
  const refund = await refundRepo.findById(refund_id);
  if (!refund) {
    throw new BadRequestResponseV2([], "Refund not found");
  }
  if (refund.status !== REFUND_STATUS.NEW) {
    throw new BadRequestResponseV2([], "Refund status invalid");
  }
  const request = await refundRepo.createRefundRequest({
    refund_id: refund.id,
    beneficiary_name,
    bank_code,
    bank_account,
    bank_name,
    branch_code,
    transfer_bank_code,
    transfer_account,
    note,
    transfer_bank_name,
  });
  await refundRepo.update(refund.id, { status: REFUND_STATUS.WAITING_REFUND, action: REFUND_ACTIONS.REFUND, message: "Refund" });
  return new SuccessResponse({ result: request }, "Refund request created successfully");
}

/**
 *
 * Remove all related data from loan_revenues, revenue_documents, financial_statements_export, and financial_statement_details by contract_number
 * @param {string} contract_number
 */
async function removeAllFinanceDataByContractNumber(contract_number) {
  try {
    if (!contract_number) {
      console.log(`${new Date().toISOString()} - removeAllFinanceDataByContractNumber: contract_number is required`);
      return false;
    }

    // 1. Find all loan_revenues by contract_number
    const loanRevenues = await find({
      table: "loan_revenues",
      whereCondition: { contract_number },
    });

    if (!loanRevenues || loanRevenues.length === 0) {
      console.log(`${new Date().toISOString()} - removeAllFinanceDataByContractNumber: No loan_revenues found for contract_number ${contract_number}`);
      return true;
    }

    // 2. Collect all loan_revenues ids
    const loanRevenueIds = loanRevenues.map((lr) => lr.id);

    // 3. Find all revenue_documents by loan_revenues_id
    const revenueDocuments = await loanRevenuesRepo.findRevenueDocumentsByLoanRevenueIds(loanRevenueIds);

    // 4. Collect all revenue_documents ids
    const revenueDocumentIds = revenueDocuments.map((rd) => rd.id);

    // 5. Find all financial_statements_export by revenu_documents_id
    const financialStatements = await financialStatementsExportRepo.findFinancialStatementsExportByRevenueDocumentIds(revenueDocumentIds);

    // 6. Collect all financial_statements_export ids
    const financialStatementIds = financialStatements.map((fse) => fse.id);

    // 7. Delete from financial_statement_details
    if (financialStatementIds.length > 0) {
      await financialStatementDetailsRepo.softRemoveFinancialStatementDetails(financialStatementIds);
    }

    // 8. Delete from financial_statements_export
    if (financialStatementIds.length > 0) {
      await financialStatementsExportRepo.softRemoveFinancialStatementsExport(financialStatementIds);
    }

    // 9. Delete from revenue_documents
    if (revenueDocumentIds.length > 0) {
      await loanRevenuesRepo.softRemoveRevenueDocuments(revenueDocumentIds);
    }

    // 10. Delete from loan_revenues
    await loanRevenuesRepo.softRemoveLoanRevenues(loanRevenueIds);

    return true;
  } catch (error) {
    console.error("Error in removeAllFinanceDataByContractNumber:", error);
    return false;
  }
}

async function handleRefundCallback(payload) {
  const { contract_number, is_success } = payload;
  const refundRequest = await refundRepo.findActiveRequestByKunn(contract_number);
  if (!refundRequest) {
    throw new BadRequestResponseV2([], "Refund request not found");
  }
  await refundRepo.updateRequestDisbursementStatus(refundRequest.id, is_success ? "SUCCESS" : "FAILED");
  if (is_success) {
    await refundRepo.update(refundRequest.refund_id, { status: REFUND_STATUS.SUCCESS });
  }
  return new SuccessResponse({ result: refundRequest }, "Refund request updated successfully");
}

const getLoanRequestDetails = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  return new SuccessResponse({
    request_id: loan.request_id,
    contract_number: loan.contract_number,
    status: loan.status,
    partner_code: loan.partner_code,
    tax_code: loan.tax_code,
    created_date: loan.created_date,
    updated_date: loan.updated_date,
  });
};

const getLoanBranches = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const branches = await find({
    table: "loan_branch_address",
    whereCondition: {
      contract_number: contract_number,
    },
    select: ["id", "branch_name", "province", "ward", "address", "new_province", "new_ward"],
    orderBy: {
      created_date: "DESC",
    },
  });
  const partnerCode = loan.partner_code;
  const branchLenderModel = await BizziLoanLender.init({
    referenceNumber: contract_number,
    data: branches,
    partnerCode,
    table: "loan_branch_address",
  });
  const columns = await branchLenderModel.columnConfig;
  const data = branchLenderModel.extendData();
  return new SuccessResponse({ branches: data, columns: columns });
};

const getLoanBranchDetails = async (contract_number, id) => {
  if (!contract_number || !id) {
    throw new BadRequestResponseV2([], "contract_number and id are required");
  }

  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }

  const branch = await findOne({
    table: "loan_branch_address",
    whereCondition: {
      contract_number: contract_number,
      id: id,
    },
  });
  if (!branch) {
    throw new BadRequestResponseV2([], "Branch not found for id: " + id);
  }
  const partnerCode = loan.partner_code;

  const branchLenderModel = await BizziLoanLender.init({
    referenceNumber: id,
    data: branch,
    partnerCode,
    table: "loan_branch_address",
  });

  const data = await branchLenderModel.bindingData({ isSubInfo: true });

  return new SuccessResponse({
    info: data,
  });
};

const getLoanWarehouses = async (contract_number) => {
  if (!contract_number) {
    throw new BadRequestResponseV2([], "contract_number is required");
  }
  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }
  const warehouses = await find({
    table: "loan_customer_warehouses",
    whereCondition: {
      contract_number: contract_number,
    },
    select: ["id", "warehouse_name", "province_code", "ward_code", "detail_address", "new_province_code", "new_ward_code"],
    orderBy: {
      created_date: "DESC",
    },
  });
  const partnerCode = loan.partner_code;
  const warehouseLenderModel = await BizziLoanLender.init({
    referenceNumber: contract_number,
    data: warehouses,
    partnerCode,
    table: "loan_customer_warehouses",
  });
  const columns = await warehouseLenderModel.columnConfig;
  const data = warehouseLenderModel.extendData();
  return new SuccessResponse({ warehouses: data, columns: columns });
};

const getLoanWarehouseDetails = async (contract_number, id) => {
  if (!contract_number || !id) {
    throw new BadRequestResponseV2([], "contract_number and id are required");
  }

  const loan = await loanContractRepo.getLoanContract(contract_number);
  if (!loan) {
    throw new BadRequestResponseV2([], "Loan not found for contract_number: " + contract_number);
  }

  const warehouse = await findOne({
    table: "loan_customer_warehouses",
    whereCondition: {
      contract_number: contract_number,
      id: id,
    },
  });
  if (!warehouse) {
    throw new BadRequestResponseV2([], "Warehouse not found for id: " + id);
  }
  const partnerCode = loan.partner_code;

  const warehouseLenderModel = await BizziLoanLender.init({
    referenceNumber: id,
    data: warehouse,
    partnerCode,
    table: "loan_customer_warehouses",
  });

  const data = await warehouseLenderModel.bindingData({ isSubInfo: true });

  return new SuccessResponse({
    info: data,
  });
};

module.exports = {
  af1Submit,
  af2Submit,
  af2ReSubmit,
  af2Approve,
  af2Reject,
  af3Submit,
  handleCallbackPartner,
  getLoanRequest,
  getPresignedDocumentUrl,
  getPresignedUploadDocumentUrl,
  getMasterdata,
  getMultiplePresignedDocumentUrl,
  getKUUNPresignedDocumentUrl,
  manualWorkflow,
  exportLoanRequest,
  getLoanRequestDetailsAf1,
  getLoanRequestDetailsAf2,
  getLoanRequestDetailsAF3,
  getLoanRequestDetails,
  getLoanRepresentations,
  getLoanRepresentationDetail,
  getLoanCustomerManagerDetails,
  getLoanCustomerShareholders,
  getLoanCustomerShareholdersDetails,
  getLoanCustomerManager,
  getLoanCustomerPartners,
  getLoanCustomerPartnersDetails,
  processData,
  processAf3Data,
  handleDocumentsGenerated,
  approveChangeRequest,
  approveAF3ChangeRequest,
  rejectAF3ChangeRequest,
  processSubInfo,
  rejectChangeRequest,
  getLoanChangeRequestHistory,
  getLoanRequestRelated,
  getLoanRequestFinanceModel,
  getLoanCustomerOwners,
  getLoanCustomerOwnersDetails,
  getLoanRequestCommonDetails,
  getLoanRequestFinanceInfoDetails,
  getContractStatus,
  getLoanRequestRelatedDocs,
  deleteDocument,
  saveUploadDocument,
  createRefund,
  findRefunds,
  transferCase,
  refundMoney,
  getBankInfoByRefund,
  getListKunnByRefund,
  approveRefund,
  rejectRefund,
  findRefundRequests,
  getHistoryByRefund,
  getCustomerInfoDetails,
  getPermanentAddress,
  getCurrentAddress,
  getSpouseDetails,
  getBusinessesInfoDetails,
  handleRefundCallback,
  getReferenceDetails1,
  getReferenceDetails2,
  getProposalDetails,
  getBusinessOperations,
  getOriginDocumentByDocType,
  getLoanBranches,
  getLoanBranchDetails,
  getLoanWarehouses,
  getLoanWarehouseDetails,
};
