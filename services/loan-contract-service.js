const { STATUS, CALLBACK_STAUS } = require('../const/caseStatus');
const { LOCK_STATUS, PARTNER_CODE } = require('../const/definition');
const loanContractRepo = require('../repositories/loan-contract-repo');
const dateHelper = require('../utils/dateHelper');
const logginRepo = require('../repositories/logging-repo');
const lmsApi = require('../apis/lms-api');
const crmService = require('../utils/crmService');
const callbackService = require("../services/callback-service")


async function setExpiredContract(endDate, partnerCodes) {
    if(!endDate) {
        endDate = dateHelper.LMS_DATE();
    }
    if(!partnerCodes || !Array.isArray(partnerCodes) || partnerCodes.length === 0) {
        throw new Error('Partner codes must be provided as a non-empty array');
    }
    const loans = await loanContractRepo.getContractLimitExpiredByDate(endDate, partnerCodes);
    const promise = loans.map(loan => loanContractRepo.updateLoanStatusV2({status: STATUS.EXPIRED, contractNumber: loan.contract_number }));
    const promiseStepLog = loans.map(loan => logginRepo.saveStepLog(loan.contract_number, 'LOS', 'SET_EXPIRED', { contractNumber: loan.contract_number, status: STATUS.EXPIRED, endDate}, { success: true }));
    const promiseLms = loans.map(loan => lmsApi.lockLimitApi(loan.contract_number));
    const promiseCrm = loans.map(loan => crmService.removeContract(global.config,loan.contract_number));
    const callbackTask = (loans || []).filter(e => e.partner_code == PARTNER_CODE.MISA).map(loan => {
        return callbackService.callbackPartner(
                loan.contract_number,
                loan.partner_code,
                CALLBACK_STAUS.EXPIRED_LIMIT,
                undefined,
                CALLBACK_STAUS.EXPIRED_LIMIT,
                null
              );
    });
    try {
        await Promise.all(callbackTask);
    } catch (error) {
        console.error(`Error in callbackTask list contracts expired: ${(loans || []).map(e=> e.contract_number).join(',')}`, error.message);
    }
    await Promise.all(promise);
    await Promise.all(promiseStepLog);
    await Promise.all(promiseLms);
    await Promise.all(promiseCrm);
    
}

function isRefinance(loanContract) {
  return loanContract?.old_contract_number != null;
}

function isContractLockActive(loanContract) {
  const isBlocked = loanContract?.is_blocked ?? loanContract?.isBlocked;
  return isBlocked === LOCK_STATUS.ACTIVE;
}

module.exports = {
    isRefinance,
    setExpiredContract,
    isContractLockActive,
};