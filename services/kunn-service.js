const { getText } = require("number-to-text-vietnamese");
const productService = require("../utils/productService");
const documentRepo = require("../repositories/document");
const kunnRepo = require("../repositories/kunn-repo");
const loanContractRepo = require("../repositories/loan-contract-repo");
const uuid = require("uuid");
const loanEsigningRepo = require("../repositories/loan-esigning");
const path = require("path");
const moment = require("moment");
const invoiceRepo = require("../repositories/invoice-repo");
const { getValueCodeMasterdataV2, getFullAddressNew, getValueCode_v3} = require("../utils/masterdataService");
const { KUNN_STATUS } = require("../const/caseStatus");
const loggingRepo = require("../repositories/logging-repo");
const sqlHelper = require("../utils/sqlHelper");
const { convertNumber } = require("../utils/helper");
const { numberToString } = require("../utils/numberToString");
const { calculateInvoicePaymentDateBizz } = require("./lms-service");
const { DATE_FORMAT, PARTNER_CODE, DOC_TYPE} = require("../const/definition");
const { formatDate, toVnDateFormat, getDate, getMonth, getYear, VN_FORMAT_DATE } = require("../utils/dateHelper");
const loanCustomerRepo = require("../repositories/loan-customer-repo");

const documentsSupplementation = async (req, res) => {
  try {
    const poolWrite = global.poolWrite;
    let { listDocCollecting, kunnNumber } = req.body;
    const kunn = await kunnRepo.getKunnData(kunnNumber);
    if (!kunn?.kunn_id) {
      return res.status(500).json({
        message: `không tìm thấy kunn ${kunnNumber}`,
        code: "NOT_FOUND",
      });
    }
    const { kunn_id, kunn_code } = kunn;
    const bundleInfo = await productService.getBundle(global.config, kunn_code, undefined, true);
    listDocCollecting = productService.mapBundleGroup(listDocCollecting, bundleInfo.data);
    const updateResult = await documentRepo.saveUploadedDocumentKunn(poolWrite, kunn_id, listDocCollecting);
    if (!updateResult) {
      return res.status(500).json({
        message: "Bổ sung chứng từ không thành công",
        code: "SERVER_ERROR",
      });
    }
    return res.status(200).json({
      message: "Bổ sung chứng từ thành công",
      code: "SUCCESS",
      data: {
        kunnNumber: kunnNumber,
      },
    });
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      message: "Đã có lỗi xảy ra",
      code: "SERVER_ERROR",
    });
  }
};

const supplementSignedFile = async (req, res) => {
  try {
    let { kunnNumber } = req.body;
    const kunn = await kunnRepo.getKunnData(kunnNumber);
    if (!kunn?.kunn_id) {
      return res.status(500).json({
        message: `không tìm thấy kunn ${kunnNumber}`,
        code: "NOT_FOUND",
      });
    }
    const { kunn_id } = kunn;
    const [lctKu, loanEsigning] = await Promise.all([
      documentRepo.findOne({
        whereCondition: {
          kunn_contract_number: kunn_id,
          doc_type: "LCTKU",
        },
        orderBy: {
          creation_time: "desc",
        },
      }),
      loanEsigningRepo.findOneV2({
        whereCondition: {
          contract_number: kunn_id,
        },
        orderBy: {
          created_date: "desc",
        },
      }),
    ]);

    if (!lctKu?.id || !lctKu?.doc_group || !loanEsigning?.contract_signed_path) {
      return res.status(400).json({
        message: "Bổ sung chứng từ không thành công",
        code: "NOT FOUND",
      });
    }
    const docGroup = lctKu?.doc_group;
    const { contract_signed_path } = loanEsigning;
    const insertResult = await documentRepo.insertSingleDocumentV2(null, "LCTKU", uuid.v4(), docGroup, getFileKeyFromUrl(contract_signed_path), getFileNameFromUrl(contract_signed_path), "KU", contract_signed_path, kunn_id);

    if (!insertResult) {
      return res.status(500).json({
        message: "Bổ sung chứng từ không thành công",
        code: "SERVER_ERROR",
      });
    }
    return res.status(200).json({
      message: "Bổ sung chứng từ thành công",
      code: "SUCCESS",
      data: {
        kunnNumber: kunnNumber,
      },
    });
  } catch (err) {
    console.log(err);
    return res.status(500).json({
      message: "Đã có lỗi xảy ra",
      code: "SERVER_ERROR",
    });
  }
};

function getFileKeyFromUrl(fileUrl) {
  if (!fileUrl) {
    return fileUrl;
  }
  const url = new URL(fileUrl);
  const relativePath = url.pathname.substring(1); // Loại bỏ ký tự '/' ở đầu
  return relativePath;
}

function getFileNameFromUrl(fileUrl) {
  const parsedUrl = new URL(fileUrl);
  const pathname = parsedUrl.pathname;
  const fileName = path.basename(pathname);
  return fileName;
}

async function getDataForTemplateBTTKUNN(kunnId, documentNos) {
  // Giấy đề nghị ứng trước kiêm KUNN BM03
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new Error(`KUNN data not found for ID: ${kunnId}`);
  }
  const contractNumber = kunnData.contract_number;
  const [loanContract, invoices, bankInfo, headQuarterAddress, loanPurpose, productInfo, loanDocs, kunns] = await Promise.all([
    loanContractRepo.getAllContractData(contractNumber),
    invoiceRepo.getInvoicesByKunn(kunnId),
    getValueCodeMasterdataV2(kunnData.bank_code, "BANK"),
    getFullAddressNew(kunnData.sme_headquarters_province, kunnData.sme_headquarters_ward, kunnData.sme_headquarters_address),
    getValueCodeMasterdataV2(kunnData.loan_purpose, "LOAN_PURPOSE"),
    productService.getProductInfo(global.config, kunnData.kunn_code),
    documentRepo.getByDocType(contractNumber, "BTTCNKPT"),
    kunnRepo.getKunnByContractNumber(contractNumber)
  ]);
  if (!loanContract) {
    throw new Error(`Loan contract not found for KUNN ID: ${kunnId}`);
  }
  const listKunn = kunns?.rows || [];
  const docType = "BTTKUNN";
  const filePath = "./template/BM03_GIAY_DE_NGHI_UNG_TRUOC_KIEM_KUNN_BTT.docx";
  const uniqueStr = uuid.v4().replace(/-/g, "").slice(0, 8);
  const fileName = `${moment().format("YYYYMMDD")}_${uniqueStr}_${kunnData.kunn_id}_${docType}.pdf`;
  const taskName = "GEN_BTT_CONTRACT_FILE";
  const now = moment();
  const currentDate = now.format(DATE_FORMAT.DDMMYYYY);

  const totalFactoredAmount = Math.round(kunnData.with_draw_amount * Number(productInfo?.advanceRate) / 100);
  const extendInvoicePaymentDate = productInfo?.factoringTermDays;
  const extendMaxEndDate = productInfo?.maxDurationDay;
  const minPaymentDate = calculateInvoicePaymentDateBizz(invoices, extendInvoicePaymentDate, extendMaxEndDate);
  const limitApprovalAmount = convertNumber(loanContract.approval_amt);
  const limitApprovalAmountText = numberToString(loanContract.approval_amt);
  const contractSignDate = formatDate(loanContract.approval_date, DATE_FORMAT.DDMMYYYY);
  const intRate = productInfo?.productVar?.[0]?.intRate ? Number(productInfo.productVar[0].intRate).toFixed(2) : '';
  const withDrawAmount = convertNumber(kunnData.with_draw_amount);
  const withDrawAmountText = numberToString(kunnData.with_draw_amount);
  const remainingFactoringLimit = convertNumber(kunnData.available_amount - kunnData.with_draw_amount);
  const remainingFactoringLimitText = numberToString(kunnData.available_amount - kunnData.with_draw_amount);

  const approvalDate = moment(loanContract.approval_date);
  const approvalDay = approvalDate.date();
  const approvalMonth = approvalDate.month() + 1;
  const approvalYear = approvalDate.year();

  const bttCNKPTFile = loanDocs?.[0];
  const totalSumFactoredAmount = listKunn.filter(k => k.status === KUNN_STATUS.ACTIVATED && k.partner_code === PARTNER_CODE.BIZZ).reduce((sum, k) => sum + k.with_draw_amount, 0);
  const feeBtt = productInfo?.fee?.[0]?.feeDetail?.[0]?.calculaDetail?.[0]?.value ? Number(productInfo.fee[0].feeDetail[0].calculaDetail[0].value).toFixed(2) : '';

  return {
    task_name: taskName,
    body: {
      file_tem_path: filePath,
      contract_number: contractNumber,
      file_name: fileName,
      doc_type: docType,
      contract_data: {
        day: now.date(),
        month: now.month() + 1,
        year: now.year(),
        kunn_contract_number: kunnId,
        contract_number: contractNumber,
        contract_sign_date: contractSignDate,
        bttcnkpt_doc_id: bttCNKPTFile?.document_no || '',
        bttcnkpt_doc_date: formatDate(bttCNKPTFile?.creation_time, DATE_FORMAT.DDMMYYYY) || '',
        bttxncn_doc_id: documentNos?.BTTXNCN,
        sme_name: loanContract.sme_name,
        sme_headquarters_address: headQuarterAddress,
        registration_cert_no: loanContract.registration_cert_no,
        registration_cert_issue_date: loanContract.registration_cert_issue_date,
        registration_cert_issue_place: loanContract.registration_cert_issue_place,
        sme_phone: loanContract.sme_phone_number,
        sme_fax: "",
        legal_representative_name: loanContract.sme_representation_name,
        legal_representative_positions: loanContract.sme_representation_position,
        invoices: invoices.map(invoice => ({
          invoice_order: invoice.invoice_order,
          invoice_date: invoice.invoice_date,
          invoice_number: invoice.invoice_number,
          total_invoice: invoice.invoice_amount,
          invoice_payment_date: invoice.payment_date,
          invoice_note: invoice.note,
        })),
        total_invoices: invoices.reduce((sum, invoice) => sum + invoice.invoice_amount, 0),
        loan_purpose: loanPurpose?.nameVn,
        approved_factoring_limit: limitApprovalAmount,
        approved_factoring_limit_text: limitApprovalAmountText,
        requested_factoring_amount: withDrawAmount,
        requested_factoring_amount_text: withDrawAmountText,
        total_factored_amount: totalFactoredAmount,
        total_factored_amount_text: numberToString(totalFactoredAmount),
        remaining_factoring_limit: remainingFactoringLimit,
        remaining_factoring_limit_text: remainingFactoringLimitText,
        factoring_term: productInfo?.factoringTermDays,
        factoring_start_date: currentDate,
        factoring_end_date: formatDate(minPaymentDate, DATE_FORMAT.DDMMYYYY),
        advance_due_date: "",
        factoring_interest_rate: intRate,
        sme_account_holder: kunnData.beneficiary_name,
        sme_account_number: kunnData.bank_account,
        sme_bank_name: bankInfo?.nameVn,
        factoring_fee: feeBtt,

        evf_approval_reason: '',
        evf_approval_factoring_limit: limitApprovalAmount,
        evf_approval_factoring_limit_text: limitApprovalAmountText,
        evf_approval_advance_amount: withDrawAmount,
        evf_approval_advance_amount_text: withDrawAmountText,
        evf_approval_total_factored_amount: convertNumber(totalSumFactoredAmount),
        evf_approval_total_factored_amount_text: numberToString(totalSumFactoredAmount),
        evf_approval_remaining_factoring_limit: remainingFactoringLimit,
        evf_approval_remaining_factoring_limit_text: remainingFactoringLimitText,
        evf_approval_factoring_term: productInfo?.factoringTermDays,
        evf_approval_factoring_start_date: currentDate,
        evf_approval_factoring_end_date: formatDate(minPaymentDate, DATE_FORMAT.DDMMYYYY),
        evf_approval_in_term_interest_rate: intRate,
        evf_approval_in_term_interest_start_date: currentDate,
        evf_approval_in_term_interest_end_date: formatDate(minPaymentDate, DATE_FORMAT.DDMMYYYY),
        evf_approval_factoring_fee: feeBtt,
        evf_approval_other_factoring_contract_number: contractNumber,
        evf_approval_other_factoring_contract_date: contractSignDate,
        approval_day: approvalDay,
        approval_month: approvalMonth,
        approval_year: approvalYear,
      }
    },
  };
}

async function getDataForTemplateBTTXNCN(kunnId, documentNos) {
  // Thông báo Xác nhận công nợ và chỉ dẫn thanh toán BM06
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new Error(`KUNN data not found for ID: ${kunnId}`);
  }
  const contractNumber = kunnData.contract_number;
  const [loanContract, invoices, kunns] = await Promise.all([
    loanContractRepo.getAllContractData(contractNumber),
    invoiceRepo.getInvoicesByKunn(kunnId),
    kunnRepo.getKunnByContractNumber(contractNumber)
  ]);
  if (!loanContract) {
    throw new Error(`Loan contract not found for KUNN ID: ${kunnId}`);
  }
  const listKunn = kunns?.rows || [];
  const docType = "BTTXNCN";
  const filePath = "./template/BM06_THONG_BAO_XAC_NHAN_CONG_NO_VA_CHI_DAN_THANH_TOAN.docx";
  const uniqueStr = uuid.v4().replace(/-/g, "").slice(0, 8);
  const fileName = `${moment().format("YYYYMMDD")}_${uniqueStr}_${kunnData.kunn_id}_${docType}.pdf`;
  const taskName = "GEN_BTT_CONTRACT_FILE";
  const now = moment();

  const currentDate = now.format(DATE_FORMAT.DDMMYYYY);
  const contractSignDate = formatDate(loanContract.approval_date, DATE_FORMAT.DDMMYYYY);
  const totalFactoredAmount = listKunn.filter(k => k.status === KUNN_STATUS.ACTIVATED && k.partner_code === PARTNER_CODE.BIZZ).reduce((sum, k) => sum + k.with_draw_amount, 0);

  return {
    task_name: taskName,
    body: {
      file_tem_path: filePath,
      contract_number: contractNumber,
      file_name: fileName,
      doc_type: docType,
      contract_data: {
        document_no: documentNos?.BTTXNCN || "",
        day: now.date(),
        month: now.month() + 1,
        year: now.year(),
        sme_name: loanContract.sme_name,
        anchor_name: loanContract.anchor_name,
        contract_number: contractNumber,
        kunn_contract_number: kunnId,
        contract_sign_date: contractSignDate,
        hop_dong_nguyen_tac: contractNumber,
        hop_dong_nguyen_tac_sign_date: contractSignDate,
        debt_confirm_date: currentDate,
        debt_confirm_amount: convertNumber(totalFactoredAmount),
        debt_confirm_amount_text: numberToString(totalFactoredAmount),
        evf_account_number: "*********",
        evf_bank_name: "Ngân hàng TMCP Quân Đội",
        ecf_branch_name: "Hoàn Kiếm - Hà Nội",
        invoices: invoices.map(invoice => ({
          invoice_order: invoice.invoice_order,
          invoice_date: invoice.invoice_date,
          invoice_number: invoice.invoice_number,
          total_invoice: invoice.invoice_amount,
          invoice_payment_date: invoice.payment_date,
          invoice_note: invoice.note,
        })),
        total_invoices: invoices.reduce((sum, invoice) => sum + invoice.invoice_amount, 0),
      }
    },
  };
}

async function getDataForTemplateBTTTTCN(kunnId, documentNos) {
  //Khoản phải thu BM04
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new Error(`KUNN data not found for ID: ${kunnId}`);
  }
  const contractNumber = kunnData.contract_number;
  const [loanContract, smeHeadquartersAddress, smeRepresentationIssuePlace] = await Promise.all([
    loanContractRepo.getAllContractData(contractNumber),
    getFullAddressNew(kunnData.sme_headquarters_province, kunnData.sme_headquarters_ward, kunnData.sme_headquarters_address),
    getValueCodeMasterdataV2(kunnData.sme_representation_issue_place, "ISSUE_PLACE_VN")
  ]);
  if (!loanContract) {
    throw new Error(`Loan contract not found for KUNN ID: ${kunnId}`);
  }

  const docType = "BTTTTCN";
  const filePath = "./template/BM04_THOA_THUAN_CHUYEN_NHUONG_KHOAN_PHAI_THU.docx";
  const uniqueStr = uuid.v4().replace(/-/g, "").slice(0, 8);
  const fileName = `${moment().format("YYYYMMDD")}_${uniqueStr}_${kunnData.kunn_id}_${docType}.pdf`;
  const taskName = "GEN_BTT_CONTRACT_FILE";
  const now = moment();

  return {
    task_name: taskName,
    body: {
      file_tem_path: filePath,
      contract_number: kunnData.contract_number,
      file_name: fileName,
      doc_type: docType,
      contract_data: {
        document_no: documentNos?.BTTTTCN || "", 
        day: now.date(),
        month: now.month() + 1,
        year: now.year(),
        sme_name: loanContract.sme_name,
        registration_cert_no: loanContract.registration_cert_no,
        registration_cert_issue_date: loanContract.registration_cert_issue_date,
        registration_cert_issue_place: loanContract.registration_cert_issue_place,
        sme_headquarters_address: smeHeadquartersAddress,
        sme_phone: loanContract.sme_phone,
        sme_fax: "",
        legal_representative_name: loanContract.sme_representation_name,
        legal_representative_positions: loanContract.sme_representation_position,
        legal_representative_identity_number: loanContract.sme_representation_id,
        legal_representative_identity_issued_by: smeRepresentationIssuePlace?.nameVn || "",
        legal_representative_identity_issue_date: loanContract.sme_representation_issue_date,
        authorization_number: "",
        authorization_date: "",
        khoan_phai_thu_duoc_chuyen_nhuong_amount: convertNumber(kunnData.with_draw_amount),
        khoan_phai_thu_duoc_chuyen_nhuong_amount_text: numberToString(kunnData.with_draw_amount),

        kunn_contract_number: kunnId,
        contract_number: contractNumber,
        contract_sign_date: formatDate(loanContract.approval_date, DATE_FORMAT.DDMMYYYY),
      }
    },
  };
}

async function getDataForTemplateBTTTTRV(kunnId, documentNos) {
  // Tờ trình rút vốn
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new Error(`KUNN data not found for ID: ${kunnId}`);
  }
  const contractNumber = kunnData.contract_number;
  const [loanContract, smeHeadquartersAddress, smeBankInfo, bttCNKPTFile, kunns, invoices, productInfo] = await Promise.all([
    loanContractRepo.getAllContractData(contractNumber),
    getFullAddressNew(kunnData.sme_headquarters_province, kunnData.sme_headquarters_ward, kunnData.sme_headquarters_address),
    getValueCodeMasterdataV2(kunnData.bank_code, "BANK"),
    documentRepo.getByDocType(contractNumber, "BTTCNKPT"),
    kunnRepo.getKunnByContractNumber(contractNumber),
    invoiceRepo.getInvoicesByKunn(kunnId),
    productService.getProductInfo(global.config, kunnData.kunn_code)
  ]);
  if (!loanContract) {
    throw new Error(`Loan contract not found for KUNN ID: ${kunnId}`);
  }

  const bttCNKPT = bttCNKPTFile?.[0] || {};
  const listKunn = kunns?.rows || [];
  const taskName = "GEN_BTT_CONTRACT_FILE";
  const docType = "BTTTTRV";
  const now = moment();
  const totalFactoredAmount = listKunn.filter(k => k.status === KUNN_STATUS.ACTIVATED && k.partner_code === PARTNER_CODE.BIZZ).reduce((sum, k) => sum + k.with_draw_amount, 0);
  const currentDate = now.format(DATE_FORMAT.DDMMYYYY);

  const extendInvoicePaymentDate = productInfo?.factoringTermDays;
  const extendMaxEndDate = productInfo?.maxDurationDay;
  const minPaymentDate = calculateInvoicePaymentDateBizz(invoices, extendInvoicePaymentDate, extendMaxEndDate);
  const intRate = productInfo?.productVar?.[0]?.intRate ? Number(productInfo.productVar[0].intRate).toFixed(2) : '';
  const factoringFee = productInfo?.fee?.[0]?.feeDetail?.[0]?.calculaDetail?.[0]?.value ? Number(productInfo.fee[0].feeDetail[0].calculaDetail[0].value).toFixed(2) : '';

  return {
    task_name: taskName,
    body: {
      contract_number: kunnData.contract_number,
      doc_type: docType,
      contract_data: {
        document_no: documentNos?.BTTTTRV || "",
        day: now.date(),
        month: now.month() + 1,
        year: now.year(),
        sme_name: loanContract.sme_name,
        contract_number: contractNumber,
        contract_sign_date: formatDate(loanContract.approval_date, DATE_FORMAT.DDMMYYYY),
        bttcnkpt_doc_id: bttCNKPT.document_no || "",
        bttcnkpt_doc_date: formatDate(bttCNKPT.creation_time, DATE_FORMAT.DDMMYYYY),
        kunn_contract_number: kunnId,
        legal_representative_name: loanContract.sme_representation_name,
        sme_headquarters_address: smeHeadquartersAddress,
        sme_phone: loanContract.sme_phone_number,
        registration_cert_no: loanContract.registration_cert_no,
        approved_factoring_limit: convertNumber(loanContract.approval_amt),
        approved_factoring_limit_text: numberToString(loanContract.approval_amt),
        current_factoring_debt: convertNumber(totalFactoredAmount),
        current_factoring_debt_text: numberToString(totalFactoredAmount),
        remaining_factoring_limit: convertNumber(kunnData.available_amount - kunnData.with_draw_amount),
        remaining_factoring_limit_text: numberToString(kunnData.available_amount - kunnData.with_draw_amount),
        request_amount: loanContract.request_amount,
        request_amount_text: numberToString(loanContract.request_amount),
        effective_date_from: currentDate,
        effective_date_to: formatDate(minPaymentDate, DATE_FORMAT.DDMMYYYY),
        receivable_value: convertNumber(kunnData.with_draw_amount),
        receivable_value_text: numberToString(kunnData.with_draw_amount),
        sme_account_number: kunnData.bank_account,
        sme_account_holder: kunnData.beneficiary_name,
        sme_bank_code: kunnData.bank_code,
        sme_bank_name: smeBankInfo.nameVn,
        term_from: currentDate,
        term_to: formatDate(minPaymentDate, DATE_FORMAT.DDMMYYYY),
        factoring_interest_rate: intRate,
        zero_interest_days: "45",
        factoring_fee: factoringFee,
      }
    },
  };
}

const checkAllKunnDocumentsExist = async (kunnId, docTypes) => {
  const documents = await documentRepo.getDocumentsByKunnAndDocTypes(kunnId, docTypes);
  return docTypes.every((docType) => documents.some((doc) => doc.doc_type === docType));
};

const cancelKunn = async ({ contractNumber, kunnId, comment, cancelledBy, kunn }) => {
  try {
    if (!kunn?.id) {
      kunn = await kunnRepo.getKunnData(kunnId);
    }
    const nonCancelableStatuses = [
      KUNN_STATUS.CANCELLED, 
      KUNN_STATUS.SIGNED_TO_BE_DISBURED, 
      KUNN_STATUS.ACTIVATED
    ];

    if (nonCancelableStatuses.includes(kunn.status)) {
      console.log(`kunn ${kunnId} cannot be cancelled, current status: ${kunn.status}`);
      return false;
    }

    const kunnUpdated = await sqlHelper.patchUpdate({
      table: "kunn",
      columns: ["status", "cancelled_reason", "cancelled_by", "cancelled_at"],
      values: sqlHelper.generateValues(
        {
          status: KUNN_STATUS.CANCELLED,
          cancelled_reason: comment,
          cancelled_by: cancelledBy,
          cancelled_at: new Date(),
        },
        ["status", "cancelled_reason", "cancelled_by", "cancelled_at"]
      ),
      conditions: {
        contract_number: contractNumber,
        kunn_id: kunnId
      },
    });

    if (kunnUpdated?.status !== KUNN_STATUS.CANCELLED) {
      console.log(`Failed to update contract ${kunnId} status to CANCELLED`);
      return false;
    }

    await loggingRepo.saveWorkflow('KUNN', KUNN_STATUS.CANCELLED, contractNumber, 'system', kunnId);
    //callback partner
    return true;
  } catch (e) {
    console.error(`Error cancelling kunn ${kunnId}:`, e);
    return false;
  }
};

const getDataForTemplateLCTKU = async (kunnId) => {
  const kunnData = await kunnRepo.getKunnData(kunnId);
  if (!kunnData) {
    throw new Error(`KUNN data not found for ID: ${kunnId}`);
  }
  const contractNumber = kunnData.contract_number;
  const [loanContractData, loanCustomerData] = await Promise.all([
    loanContractRepo.getAllContractData(contractNumber),
    loanCustomerRepo.getLoanCustomer(contractNumber)
  ]);
  const [wardCur, provinceCur, wardPer, provincePer, bankName, bankBranchName] = await Promise.all([
    getValueCode_v3(loanContractData.cur_new_ward_code, "NEW_WARD"),
    getValueCode_v3(loanContractData.cur_new_province_code, "NEW_PROVINCE"),
    getValueCode_v3(loanContractData.per_new_ward_code, "NEW_WARD"),
    getValueCode_v3(loanContractData.per_new_province_code, "NEW_PROVINCE"),
    getValueCode_v3(kunnData.bank_code, "BANK"),
    getValueCode_v3(kunnData.bank_branch_code, "BANK_BRANCH"),
    // getValueCode_v3(loanContractData.id_issue_place, "ISSUE_PLACE_VN")
  ]);
  const outstandingBalance = +loanContractData.approval_amt - +kunnData.available_amount + +kunnData.with_draw_amount;
  return {
    task_name: "GEN_FINV_DOC_FILE",
    body: {
      doc_type: DOC_TYPE.LCTKU,
      contract_number: contractNumber,
      contract_data: {
        cust_full_name: loanContractData.cust_full_name,
        id_number: loanContractData.id_number,
        id_issue_dt: formatDate(loanContractData.id_issue_dt, DATE_FORMAT.DDMMYYYY),
        id_issue_place: loanContractData.id_issue_place,
        address_per: `${loanContractData.address_per}, ${wardPer}, ${provincePer}`,
        address_cur: `${loanContractData.address_cur}, ${wardCur}, ${provinceCur}`,
        phone_number: loanContractData.phone_number1,
        registration_number: loanCustomerData.registration_number,
        address_on_license: loanCustomerData.address_on_license,
        contract_number: contractNumber,
        approval_amt: convertNumber(loanContractData.approval_amt || 0),
        with_draw_amount: convertNumber(kunnData.with_draw_amount),
        with_draw_amount_text: getText(+kunnData.with_draw_amount),
        tenor: kunnData.tenor,
        start_date: formatDate(kunnData.start_date, DATE_FORMAT.DDMMYYYY),
        end_date: formatDate(kunnData.end_date, DATE_FORMAT.DDMMYYYY),
        approval_int_rate: loanContractData.approval_int_rate,
        beneficiary_name: kunnData.beneficiary_name,
        bank_account: kunnData.bank_account,
        bank: bankName,
        bank_branch: bankBranchName,
        kunnId,
        bill_day: loanContractData.bill_day,
        outstanding_balance: convertNumber(outstandingBalance),
      },
    },
  }
};

const getDataFinVForTemplateTTRV = async (kunnId) => {
  const kunn = await kunnRepo.getKunnData(kunnId);
  if (!kunn) {
    throw new Error(`KUNN data not found for ID: ${kunnId}`);
  }
  const loan = await loanContractRepo.getAllContractData(kunn.contract_number);
  const contractNumber = kunn.contract_number;
  const [ wardPer, provincePer, loanPurpose, idIssuePlace] = await Promise.all([
    getValueCode_v3(loan.per_new_ward_code, "NEW_WARD"), 
    getValueCode_v3(loan.per_new_province_code, "NEW_PROVINCE"), 
    getValueCode_v3(loan.loan_purpose, "LOAN_PURPOSE"), 
    getValueCode_v3(loan.id_issue_place, "ISSUE_PLACE_VN")
  ]);
  return {
    task_name: "GEN_FINV_DOC_FILE",
    body: {
      contract_number: contractNumber,
      doc_type: DOC_TYPE.TTRV,
      contract_data: {
        contract_number: contractNumber,
        customer_name: loan.cust_full_name,
        per_address: `${loan.address_cur}, ${wardPer}, ${provincePer}`,
        new_per_address: `${loan.address_cur}, ${wardPer}, ${provincePer}`,
        phone_number: loan.phone_number1,
        email: loan.email,
        identity_card: loan.id_number,
        issue_date: toVnDateFormat(loan.id_issue_dt),
        issue_place: idIssuePlace,
        loan_amount: convertNumber(kunn.with_draw_amount),
        loan_amount_text: getText(Number(kunn.with_draw_amount)),
        loan_purpose: loanPurpose,
        loan_amount_approve: convertNumber(loan.approval_amt),
        total_debt: convertNumber(0),
        loan_amount_remain: convertNumber(0),
        loan_amount_remain_after_kunn: convertNumber(0),
        end_date: toVnDateFormat(kunn.end_date),
        ir_rate_year: parseInt(loan.approval_int_rate * 100),
        ngay: getDate(),
        thang: getMonth(),
        nam: getYear(),
        date_signature: VN_FORMAT_DATE(),
        kunnId,
      },
    }
  };
};

module.exports = {
  documentsSupplementation,
  supplementSignedFile,
  getDataForTemplateBTTXNCN,
  getDataForTemplateBTTTTCN,
  getDataForTemplateBTTTTRV,
  getDataForTemplateBTTKUNN,
  checkAllKunnDocumentsExist,
  cancelKunn,
  getDataForTemplateLCTKU,
  getDataFinVForTemplateTTRV,
};
