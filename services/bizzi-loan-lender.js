const BaseLenderModel = require("../base/base-lender-model");
const uuid = require("uuid");
const { getLenderChangeRequestDetail, updateLenderChangeRequest, createLenderChangeRequest, getLatestLenderChangeRequest, getLatestLenderChangeRequestList, updateStatusOfListChangeRequest, checkChangeRequestDetailsHasChange, checkChangeRequestDetailsHasResubmit } = require("../repositories/lender-change-request-repo");
const _ = require("lodash");
const loanContractRepo = require("../repositories/loan-contract-repo");
const { LENDER_CHANGE_REQUEST_STATUS } = require("../const/status-const");
const { REF_TABLE } = require("../const/variables-const");
const { LENDER_REQUEST_TYPE, LENDER_REFERENCE_TYPE } = require("../const/variables-const");
const { updateKUStatusV2 } = require("../repositories/kunn-repo");
const { updateLoanContract } = require("../repositories/loan-contract-repo");
const { STATUS } = require("../const/caseStatus");
const { BadRequestResponseV2 } = require("../base/response");
const loanContactDocumentRepo = require("../repositories/document");
const { arraysEqualObjectsUnordered } = require("../utils/helper");
const { find, updateData } = require("../utils/sqlHelper");
const { TASK_FLOW } = require("../const/definition");
const { cancelContract } = require("./contract-service");
const loggingRepo = require("../repositories/logging-repo");

class BizziLoanLender extends BaseLenderModel {
  constructor({
    referenceNumber,
    data,
    partnerCode,
    table,
    url,
    changeRequest,
    changeRequestDetails,
    groupType,
  }) {
    super({
      referenceNumber,
      data,
      partnerCode,
      table,
      url,
      changeRequest,
      changeRequestDetails,
      groupType,
    });
    this.table = table || "loan_contract";
  }

  async bindingData({isSubInfo} = {}) {
    const data = this.data;
    const lenderColumnConfig = this.columnConfig;
    let result = [];

    //find all key has type = file in lenderColumnConfig
    const docTypes = lenderColumnConfig
      .filter((col) => col.type === "file")
      .map((col) => col.key);

    //get change request details
    let request_type;

    let tempTable;
    switch (this.table) {
      case REF_TABLE.LOAN_CONTRACT.toLocaleLowerCase():
        request_type = LENDER_REQUEST_TYPE.LENDER_LOAN_PROCESS;
        this.docs = await loanContactDocumentRepo.findByContractAndTypes(
          this.referenceNumber,
          docTypes
        );
        break;
      case REF_TABLE.LOAN_CONTRACT_DOCUMENT.toLocaleLowerCase():
        request_type = LENDER_REQUEST_TYPE.LENDER_LOAN_AF3_PROCESS;
        this.docs = await loanContactDocumentRepo.findByContractAndTypes(
          this.referenceNumber,
          docTypes
        );
        break;
      case REF_TABLE.REPRESENTATION.toLocaleLowerCase():
      case REF_TABLE.PARTNER.toLocaleLowerCase():
      case REF_TABLE.SHAREHOLDER.toLocaleLowerCase():
      case REF_TABLE.OWNER.toLocaleLowerCase():
      case REF_TABLE.WAREHOUSE.toLocaleLowerCase():
      case REF_TABLE.BRANCH.toLocaleLowerCase():
        request_type = LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS;
        this.docs = await loanContactDocumentRepo.findByReferenceAndTypes(
          this.table,
          this.referenceNumber,
          docTypes
        );
        if (!isSubInfo) {
          tempTable = REF_TABLE.LOAN_CONTRACT;
        }
        break;
      // case REF_TABLE.MANAGER.toLocaleLowerCase():
      default:
        request_type = LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS;
        this.docs = await loanContactDocumentRepo.findByReferenceAndTypes(
          this.table,
          this.referenceNumber,
          docTypes
        );
        break;
    }
    if (!tempTable && isSubInfo) {
      this.changeRequest = null;
      this.changeRequestDetails = null;
    }
    this.changeRequest =
      this.changeRequest ||
      (await getLatestLenderChangeRequest({
        request_type: request_type,
        reference_code: isSubInfo ? data?.id : this.referenceNumber,
        reference_type: tempTable || this.table.toUpperCase(),
      }));
    this.changeRequestDetails =
      this.changeRequestDetails ||
      (this.changeRequest
        ? await getLenderChangeRequestDetail(this.changeRequest.id)
        : []);
    this.docs = this.docs || [];
    if (Array.isArray(lenderColumnConfig)) {
      for (const col of lenderColumnConfig) {
        const key = col.key;
        const extendData = this.getStatusAndLatestComment(key);
        if (col.type === "file") {
          const files = this.docs.filter((doc) => doc.doc_type === key);
          //neu la file thi old value can parse to array
          if (extendData?.old_value) {
            extendData.old_value = JSON.parse(extendData.old_value);
            extendData.new_value = JSON.parse(extendData.new_value);
            for (const element of extendData.new_value || []) {
              if (!element.file_key && element.doc_id) {
                let docItem = await loanContactDocumentRepo.findByDocID(
                  element.doc_id
                );
                element.file_key = docItem?.file_key;
                element.file_name = docItem?.file_name;
                element.url = docItem?.url;
              }
            }
          }
          result.push({
            ...col,
            ...this.defaultExtend(extendData),

            value:
              extendData?.new_value ||
              files.map((file) => ({
                id: file.id,
                file_key: file.file_key,
                file_name: file.file_name,
                url: file.url,
                doc_group: file.doc_group,
              })),
          });
        } else {
          const value =
            extendData?.new_value ||
            (data.hasOwnProperty(key) ? data[key] : null);
          result.push({
            ...col,
            ...this.defaultExtend(extendData),
            value,
          });
        }
      }
    }
    this.bindedData = result;
    return result;
  }

  async initialize() {
    await this.getLenderColumnConfig();
    await this.bindingData();
  }

  //override method to extract key values for allow files types
  async extractKeyValues(changeRequestId, info, refTable = null) {
    const data = this.data;
    const docTypeKeys =
      this.columnConfig
        .filter((col) => col.type === "file")
        .map((col) => col.key) || [];

    const docs = await loanContactDocumentRepo.findByContractAndTypes(
      this.referenceNumber,
      docTypeKeys
    );
    return await Promise.all(
      info.map(async (item) => {
        const keyConfig = this.findKeyColumnConfig(item.key);
        let oldValue = data.hasOwnProperty(item.key) ? data[item.key] : null;
        let newValue = item.value;
        if (keyConfig && keyConfig.type === "file") {
          oldValue = docs
            .filter((doc) => doc.doc_type === item.key)
            .map((doc) => ({
              id: doc.id,
              doc_id: doc.doc_id, // đồng bộ lại field
              file_key: doc.file_key,
              file_name: doc.file_name,
              url: doc.url,
              doc_type: doc.doc_type,
              doc_group: doc.doc_group,
            }));
          // get docs by doc ids in item.value
          let newDocs = [];
          if (item.value && Array.isArray(item.value)) {
            const docIds = item.value
              .map((doc) => doc.doc_id)
              .filter((id) => id);
            if (docIds.length > 0) {
              newDocs = await loanContactDocumentRepo.findByDocIds(docIds);
            }
          }

          // const merged = [
          //   ...newDocs.map((doc) => ({
          //     id: doc.id,
          //     doc_id: doc.doc_id, // đồng bộ với doc_id luôn
          //     file_key: doc.file_key,
          //     file_name: doc.file_name,
          //     url: doc.url,
          //     doc_type: doc.doc_type,
          //     doc_group: doc.doc_group,
          //   })),
          //   ...(oldValue || []),
          // ];

          const merged =[]
          for (const doc of (item.value || [])) {
            const oldDoc = oldValue.find((e) => doc.doc_id == e.doc_id);
            if(!oldDoc){
              //find in new docs
              const newDoc = newDocs.find((e) => doc.doc_id == e.doc_id);
              if(newDoc){
                merged.push({
                  id: newDoc.id,
                  doc_id: newDoc.doc_id, // đồng bộ với doc_id luôn
                  file_key: newDoc.file_key,
                  file_name: newDoc.file_name,
                  url: newDoc.url,
                  doc_type: newDoc.doc_type,
                  doc_group: newDoc.doc_group,
                })
              }
            }
            else {
              merged.push({...oldDoc,...doc})
            }
          }
          
          //tim nhung phan tu trong old doc ma merged chua co thi add vao
          for (const oldDoc of (oldValue || [])) {
            if (!merged.find((doc) => doc.doc_id == oldDoc.doc_id)) {
              merged.push(oldDoc);
            }
          }

          newValue = merged;
        }

        let isChange = false;
        if (keyConfig && keyConfig.type === "file") {
          if (!arraysEqualObjectsUnordered(newValue, oldValue)) {
            isChange = true;
          }
          oldValue = oldValue ? JSON.stringify(oldValue) : null;
          newValue = newValue ? JSON.stringify(newValue) : null;
        } else {
          isChange = newValue !== oldValue;
        }

        return {
          change_request_id: changeRequestId,
          key: item.key,
          old_value: oldValue,
          new_value: newValue,
          status: item.status,
          comment: item.comment,
          editable: keyConfig?.editable || false,
          is_change: isChange,
          ref_table:
            item.table?.toUpperCase() ||
            item.reference_type?.toUpperCase() ||
            refTable,
        };
      })
    );
  }

  async process(body) {
    const contractNumber = body.contractNumber || body.contract_number;
    let waitingStatus =
      body.waiting_status || STATUS.WAITING_APPROVE_CHANGE_REQUEST;

    body.requestType =
      body.requestType || LENDER_REQUEST_TYPE.LENDER_LOAN_PROCESS;
    body.referenceType =
      body.referenceType || LENDER_REFERENCE_TYPE.LOAN_CONTRACT;
    body.requestId = body.requestId || uuid.v4();

    let changeRequest = await super.process(body, this.table.toUpperCase());
    const changeRequestDetails = await getLenderChangeRequestDetail(
      changeRequest.id
    );

    let hasChange = _.some(changeRequestDetails, {
      editable: true,
      is_change: true,
    });
    let hasResubmit = _.some(changeRequestDetails, { status: false });

    let _changeRequests = [];
    if (body.requestType !== LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS) {
      const { changeRequests } = await this.findChangesNeedUpdate(contractNumber);
      _changeRequests = changeRequests;
      const subRequestIds = changeRequests.map((item) => item.id);
      const [subHasChange, subHasResubmit] = await Promise.all([
        checkChangeRequestDetailsHasChange(subRequestIds),
        checkChangeRequestDetailsHasResubmit(subRequestIds),
      ]);
      hasChange = hasChange || subHasChange;
      hasResubmit = hasResubmit || subHasResubmit;
    }

    if (!hasChange && !hasResubmit) {
      changeRequest = await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.APPROVED,
      });
      return changeRequest;
    }
    if (body.requestType === LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS) {
      changeRequest = await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
      return changeRequest;
    }
    // update  neu co resubmit roi thi ko dc tinh la edit
    if (hasResubmit) {
      waitingStatus = STATUS.RESUBMIT_A2;

      // update sub info status
      if (_changeRequests?.length > 0) {
        await updateStatusOfListChangeRequest(
          _changeRequests.map((item) => item.id),
          LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT
        );
      }
    }

    await updateLoanContract({
      contract_number: contractNumber,
      status: waitingStatus,
      current_task: TASK_FLOW.RESUBMIT_A2,
      resubmitted_at: hasResubmit ? new Date() : null,
    });

    if (!hasResubmit && hasChange) {
      changeRequest = await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
      // goi phe duyet va quay lai af2 luon

      return changeRequest;
    }

    if (hasResubmit) {
      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
      });

      return { ...changeRequest, resubmit: true };
    }

    return changeRequest;
  }

  async af3process(body) {
    const contractNumber = body.contractNumber || body.contract_number;
    body.requestId = body.requestId || uuid.v4();

    let changeRequest = await super.process(body, this.table.toUpperCase());
    const changeRequestDetails = await getLenderChangeRequestDetail(
      changeRequest.id
    );

    const hasChange = _.some(changeRequestDetails, {
      editable: true,
      is_change: true,
    });
    const hasResubmit = _.some(changeRequestDetails, { status: false });

    if (!hasChange && !hasResubmit) {
      changeRequest = await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.APPROVED,
      });
      return changeRequest;
    }
    // update  neu co resubmit roi thi ko dc tinh la edit
    if (hasResubmit) {
      await updateLoanContract({
        contract_number: contractNumber,
        status: STATUS.RESUBMIT_A3,
        current_task: TASK_FLOW.RESUBMIT_A3,
      });

      await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_RESUBMIT,
      });

      return { ...changeRequest, resubmit: true };
    }

    if (!hasResubmit && hasChange) {
      changeRequest = await updateLenderChangeRequest(changeRequest.id, {
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });

      // await this.commitAF3ChangeRequest({ comment: body?.comment, createdBy: `UI lender${body?.createdBy}` }, LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ);
      // await bizziService.approveAF3ChangeRequest(body);
      return changeRequest;
    }

    return changeRequest;
  }

  async callback(changeRequest) {
    const step = "REQ_DISBURSE";
    const changeRequestDetails = await getLenderChangeRequestDetail(
      changeRequest.id
    );
    const validatedParams = changeRequestDetails.maps((item) => {
      return {
        param: item.key,
        reason: item.comment,
        isValid: item.status,
      };
    });

    const body = {
      requestId: changeRequest.requestId,
      debtContractNumber: changeRequest.referenceNumber,
      step: step,
      validatedParams: validatedParams,
      validatedDocs: [],
      validatedInvoices: [],
    };

    return body;
  }

  async commitChangeRequest(commitData, requestType, reference_type) {
    try {
      const contractNumber =
        this.data.contractNumber || this.data.contract_number;
      const loan = this.data;
      const { comment, createdBy } = commitData;
      // get last change request type: LENDER_LOAN_PROCESS

      const commitChangeRequest = await createLenderChangeRequest({
        request_id: uuid.v4(),
        request_type: requestType,
        reference_code: contractNumber,
        reference_type: reference_type || LENDER_REFERENCE_TYPE.LOAN_CONTRACT,
        created_by: createdBy || "system",
        request_body: commitData,
        comment: comment,
      });

      const lastChangeRequestForCommit = await getLatestLenderChangeRequest({
        reference_code: contractNumber,
        reference_type: reference_type || LENDER_REFERENCE_TYPE.LOAN_CONTRACT,
        request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_PROCESS,
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
      if (!lastChangeRequestForCommit) {
        if (requestType == LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_CHANGE_REQ) {
          await cancelContract({
            contractNumber: contractNumber,
            comment: comment,
            cancelledBy: createdBy,
            stage: TASK_FLOW.MANUAL_REVIEW_A2,
          });
          return;
        }
        if (requestType == LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_CHANGE_REQ) {
          await loggingRepo.saveWorkflow(
            TASK_FLOW.MANUAL_REVIEW_A2,
            STATUS.APPROVED,
            contractNumber,
            createdBy
          );
          return;
        }
        return;
      }
      const { changeRequests, allowsKeys } = await this.findChangesNeedUpdate(
        contractNumber
      );

      //insert loan change to first
      changeRequests.unshift(lastChangeRequestForCommit);
      allowsKeys[LENDER_REFERENCE_TYPE.LOAN_CONTRACT] = Object.keys(loan);
      if (requestType == LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_CHANGE_REQ) {
        await cancelContract({
          contractNumber: contractNumber,
          comment: comment,
          cancelledBy: createdBy,
        });
        // get change request by contract number
        //update status of change request to REJECT_CHANGE_REQUEST
        await updateStatusOfListChangeRequest(
          changeRequests.map((item) => item.id),
          LENDER_CHANGE_REQUEST_STATUS.REJECTED
        );
        return true;
      }
      for (const changeRequest of changeRequests) {
        const dataToUpdate = await this.handleApplyChangeRequest(
          contractNumber,
          allowsKeys[changeRequest.reference_type],
          changeRequest,
          createdBy
        );
        if (!dataToUpdate) {
          continue;
        }
        switch (changeRequest.reference_type) {
          case LENDER_REFERENCE_TYPE.LOAN_CONTRACT: {
            dataToUpdate.status = STATUS.APPROVED_CHANGE_REQUEST;
            dataToUpdate.contract_number = contractNumber;
            await updateLoanContract(dataToUpdate);
            break;
          }
          case LENDER_REFERENCE_TYPE.REPRESENTATION: {
            await this.updateDataFromChange(
              "loan_customer_representations",
              changeRequest.reference_code,
              dataToUpdate
            );
            break;
          }
          case LENDER_REFERENCE_TYPE.MANAGER: {
            await this.updateDataFromChange(
              "loan_customer_managers",
              changeRequest.reference_code,
              dataToUpdate
            );
            break;
          }
          case LENDER_REFERENCE_TYPE.SHAREHOLDER: {
            await this.updateDataFromChange(
              "loan_customer_shareholders",
              changeRequest.reference_code,
              dataToUpdate
            );
            break;
          }
          case LENDER_REFERENCE_TYPE.PARTNER: {
            await this.updateDataFromChange(
              "loan_customer_partners",
              changeRequest.reference_code,
              dataToUpdate
            );
            break;
          }
          default:
            continue;
        }
      }
      const changesRequestIds = changeRequests.map((item) => item.id);
      changesRequestIds.push(commitChangeRequest.id);
      //insert change request details
      await updateStatusOfListChangeRequest(
        changesRequestIds,
        LENDER_CHANGE_REQUEST_STATUS.APPROVED
      );
      return true;
    } catch (error) {
      console.error(`[BizziLoanLender] [commitChangeRequest] Error:`, error);
      throw new BadRequestResponseV2([], error.message);
    }
  }

  async commitAF3ChangeRequest(commitData, requestType) {
    try {
      const contractNumber =
        this.data.contractNumber || this.data.contract_number;
      const loan = this.data;
      const { comment, createdBy } = commitData;

      const commitChangeRequest = await createLenderChangeRequest({
        request_id: uuid.v4(),
        request_type: requestType,
        reference_code: contractNumber,
        reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT,
        created_by: createdBy || "AF3 lender system",
        request_body: commitData,
        comment: comment,
      });

      const lastChangeRequestForCommit = await getLatestLenderChangeRequest({
        reference_code: contractNumber,
        reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT,
        request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_AF3_PROCESS,
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });

      if (!lastChangeRequestForCommit) {
        // throw new BadRequestResponseV2([], `No change request found for contract_number: ${contractNumber}`);
        // nếu duyệt luôn thì không cần tạo change request
        if (
          requestType == LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_AF3_CHANGE_REQ
        ) {
          await cancelContract({
            contractNumber: contractNumber,
            comment: comment,
            cancelledBy: createdBy,
            stage: TASK_FLOW.MANUAL_REVIEW_A3,
          });
          return;
        }
        if (
          requestType == LENDER_REQUEST_TYPE.LENDER_LOAN_APPROVE_AF3_CHANGE_REQ
        ) {
          await loggingRepo.saveWorkflow(
            TASK_FLOW.MANUAL_REVIEW_A3,
            STATUS.APPROVED,
            contractNumber,
            createdBy
          );
          await loanContractRepo.updateContractStatus(
            STATUS.PASSED_REVIEW_A3,
            contractNumber
          );
          return;
        }
        return;
      }

      const { changeRequests, allowsKeys } =
        await this.findAF3ChangesNeedUpdate(contractNumber);
      //insert loan change to first
      changeRequests.unshift(lastChangeRequestForCommit);
      allowsKeys[LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT] =
        Object.keys(loan);

      if (
        requestType == LENDER_REQUEST_TYPE.LENDER_LOAN_REJECT_AF3_CHANGE_REQ
      ) {
        await cancelContract({
          contractNumber: contractNumber,
          comment: comment,
          cancelledBy: createdBy,
        });
        // get change request by contract number
        //update status of change request to REJECT_CHANGE_REQUEST
        await updateStatusOfListChangeRequest(
          changeRequests.map((item) => item.id),
          LENDER_CHANGE_REQUEST_STATUS.REJECTED
        );
        return true;
      }
      for (const changeRequest of changeRequests) {
        const dataToUpdate = await this.handleApplyChangeRequest(
          contractNumber,
          allowsKeys[changeRequest.reference_type],
          changeRequest,
          createdBy
        );
        if (!dataToUpdate) {
          continue;
        }

        switch (changeRequest.reference_type) {
          case LENDER_REFERENCE_TYPE.LOAN_CONTRACT:
            dataToUpdate.status = STATUS.PASSED_REVIEW_A3;
            dataToUpdate.contract_number = contractNumber;
            await updateLoanContract(dataToUpdate);
            break;
          case LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT:
            await this.updateDataFromChange(
              "loan_contract_document",
              changeRequest.reference_code,
              dataToUpdate
            );
            break;
          default:
            continue;
        }
      }
      const changesRequestIds = changeRequests.map((item) => item.id);
      changesRequestIds.push(commitChangeRequest.id);
      //insert change request details
      await updateStatusOfListChangeRequest(
        changesRequestIds,
        LENDER_CHANGE_REQUEST_STATUS.APPROVED
      );
      return true;
    } catch (error) {
      console.error(`[BizziLoanLender] [commitChangeRequest] Error:`, error);
      throw new BadRequestResponseV2([], error.message);
    }
  }

  async updateDataFromChange(table, id, data) {
    try {
      const columns = Object.keys(data);
      const values = Object.values(data);
      await updateData({
        table: table,
        columns: columns,
        values: values,
        conditions: {
          id: Number(id),
        },
      });
    } catch (error) {
      console.error(`[BizziLoanLender] [updateDataFromChange] Error:`, error);
    }
  }

  async findChangesNeedUpdate(contractNumber) {
    const task = [];
    //dai dien phap luat
    task.push(
      find({
        table: "loan_customer_representations",
        whereCondition: {
          contract_number: contractNumber,
          is_deleted: 0,
        },
      })
    );
    // quan ly
    task.push(
      find({
        table: "loan_customer_managers",
        whereCondition: {
          contract_number: contractNumber,
          is_deleted: 0,
        },
      })
    );
    // co dong
    task.push(
      find({
        table: "loan_customer_shareholders",
        whereCondition: {
          contract_number: contractNumber,
          is_deleted: 0,
        },
      })
    );
    // doi tac
    task.push(
      find({
        table: "loan_customer_partners",
        whereCondition: {
          contract_number: contractNumber,
          is_deleted: 0,
        },
      })
    );
    //chu so huu
    task.push(
      find({
        table: "loan_business_owner",
        whereCondition: {
          contract_number: contractNumber,
          is_deleted: 0,
        },
      })
    );
    const [representations, managers, shareholders, partners, businessOwners] =
      await Promise.all(task);
    const conditions = [];
    const allowsKeys = {
      [LENDER_REFERENCE_TYPE.REPRESENTATION]: Object.keys(
        representations?.[0] || {}
      ),
      [LENDER_REFERENCE_TYPE.MANAGER]: Object.keys(managers?.[0] || {}),
      [LENDER_REFERENCE_TYPE.SHAREHOLDER]: Object.keys(shareholders?.[0] || {}),
      [LENDER_REFERENCE_TYPE.PARTNER]: Object.keys(partners?.[0] || {}),
      [LENDER_REFERENCE_TYPE.OWNER]: Object.keys(businessOwners?.[0] || {}),
    };
    for (const representation of representations) {
      conditions.push({
        reference_code: representation.id.toString(),
        reference_type: LENDER_REFERENCE_TYPE.REPRESENTATION,
        request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS,
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    }
    for (const manager of managers) {
      conditions.push({
        reference_code: manager.id.toString(),
        reference_type: LENDER_REFERENCE_TYPE.MANAGER,
        request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS,
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    }
    for (const shareholder of shareholders) {
      conditions.push({
        reference_code: shareholder.id.toString(),
        reference_type: LENDER_REFERENCE_TYPE.SHAREHOLDER,
        request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS,
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    }
    for (const partner of partners) {
      conditions.push({
        reference_code: partner.id.toString(),
        reference_type: LENDER_REFERENCE_TYPE.PARTNER,
        request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS,
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    }
    for (const businessOwner of businessOwners) {
      conditions.push({
        reference_code: businessOwner.id.toString(),
        reference_type: LENDER_REFERENCE_TYPE.OWNER,
        request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_SUB_INFO_PROCESS,
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    }
    const changeRequests =
      (await getLatestLenderChangeRequestList(conditions)) || [];
    return { changeRequests, allowsKeys };
  }

  async findAF3ChangesNeedUpdate(contractNumber) {
    const task = [];
    //dai dien phap luat
    task.push(
      find({
        table: "loan_contract_document",
        whereCondition: {
          contract_number: contractNumber,
          is_deleted: 0,
        },
      })
    );

    const [documents] = await Promise.all(task);
    const conditions = [];
    const allowsKeys = {
      [LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT]: Object.keys(
        documents?.[0] || {}
      ),
    };
    for (const doc of documents) {
      conditions.push({
        reference_code: doc.id.toString(),
        reference_type: LENDER_REFERENCE_TYPE.LOAN_CONTRACT_DOCUMENT,
        request_type: LENDER_REQUEST_TYPE.LENDER_LOAN_AF3_PROCESS,
        status: LENDER_CHANGE_REQUEST_STATUS.WAITING_APPROVE,
      });
    }

    const changeRequests =
      (await getLatestLenderChangeRequestList(conditions)) || [];
    return { changeRequests, allowsKeys };
  }

    async handleApplyChangeRequest(
    contractNumber,
    allowKey,
    changeRequest,
    createdBy
  ) {
    try {
      const changeDetails = await getLenderChangeRequestDetail(
        changeRequest.id
      );
      const keysFileData = (this.columnConfig || [])
        .filter((col) => col.type === "file")
        .map((col) => col.key);

      const dataToUpdate = {};
      let listDocIdsDelete = [];
      const listDocToInsert = [];
      const listDocToUpdate = [];
      for (const detail of changeDetails) {
        if (!detail.is_change) {
          continue;
        }
        if (keysFileData.includes(detail.key)) {
          const value = detail.new_value || [];
          // docTypesNeedToDelete.push(detail.key);
          console.log(
            `[BizziLoanLender] [commitChangeRequest] File data for key ${detail.key}:`,
            value
          );
          const newDocs = JSON.parse(value) || [];
          const oldDocs = JSON.parse(detail.old_value || "[]");
          //doc delete if in oldDocs but not in newDocs
          // listDocDelete = oldDocs.filter(
          //   (oldDoc) => newDocs.find((newDoc) => newDoc.doc_id === oldDoc.doc_id)
          // ) || [];

          // delete old files
          const docGroup =
            oldDocs.find((doc) => doc.doc_type === detail.key)?.doc_group ||
            null;
          for (const doc of newDocs) {
            const oldDoc = oldDocs.find((d) => d.doc_id === doc.doc_id);
            if (oldDoc) {
              // If the old document exists, we can compare and decide what to do
              if (oldDoc.file_key !== doc.file_key) {
                // If file_key is different, we consider it as a new file upload
                listDocIdsDelete.push(oldDoc.id);
                listDocToInsert.push({
                  contractNumber: contractNumber,
                  docType: detail.key,
                  fileKey: doc.file_key,
                  fileName: doc.file_name,
                  url: doc.url,
                  createdBy: createdBy || "system",
                  docId: doc.doc_id,
                  kunnContractNumber: null,
                  docGroup: oldDoc.doc_group || docGroup,
                  isDeleted: 0,
                });
              }
            } else {
              if (doc.doc_id) {
                listDocToUpdate.push({
                  contractNumber: contractNumber,
                  docId: doc.doc_id,
                  docType: doc.doc_type,
                });
              } else {
                listDocToInsert.push({
                  contractNumber: contractNumber,
                  docType: detail.key,
                  fileKey: doc.file_key,
                  fileName: doc.file_name,
                  url: doc.url,
                  createdBy: createdBy || "system",
                  docId: uuid.v4(),
                  kunnContractNumber: null,
                  docGroup: docGroup,
                  isDeleted: 0,
                });
              }
            }
          }
        } else if (allowKey.includes(detail.key)) {
          dataToUpdate[detail.key] = detail.new_value;
        }
      }
      await Promise.all([
        loanContactDocumentRepo.softDeleteByContractNumberAndIds(
          contractNumber,
          listDocIdsDelete
        ),
        ...listDocToInsert.map((doc) => loanContactDocumentRepo.insert(doc)),
        ...listDocToUpdate.map((doc) =>
          loanContactDocumentRepo.updateLoanContractNumberKunn(doc)
        ),
      ]);
      return dataToUpdate;
    } catch (error) {
      console.error(
        `[BizziLoanLender] [handleApplyChangeRequest] Error:`,
        error
      );
      throw error;
    }
  }
}

module.exports = BizziLoanLender;
