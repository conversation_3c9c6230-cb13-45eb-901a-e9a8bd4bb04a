const crmService = require("../utils/crmService");
const crmGw = require("./crm-service");
const loanContractRepo = require("../repositories/loan-contract-repo");
const loggingRepo = require("../repositories/logging-repo");
const { STATUS } = require("../const/caseStatus");
const { CRM } = require("../const/service-response-const");
const { MANUAL_TASK_CODE, SERVICE_NAME, CONTRACT_TYPE } = require("../const/definition");
const common = require("../utils/common");
const utils = require("../utils/helper");
const dateHelper = require("../utils/dateHelper");
const { body } = require("express-validator");
const { pushTaskMc } = require("../utils/aadService");
const uuid = require("uuid");
const { checkNfcCacheApi } = require("../apis/crm.api");
const actionAuditService = require("../services/action-audit");
const { CASE_STATUS } = require("../const/code-const");

async function checkDedupCash(contractNumber) {
  const crmCheckDedupRs = await crmService.checkDedup(contractNumber);
  // const loanData = await loanContractRepo.getLoanContract(contractNumber);
  if (crmCheckDedupRs.code == 0) {
    if (crmCheckDedupRs.data.statusDedup !== "MD") {
      const custId = crmCheckDedupRs.data.custId;
      await loanContractRepo.updateCustId(custId, contractNumber);
      await crmService.createCustomerService(contractNumber);
      return true;
    } else {
      // await pushTaskMc('CE',contractNumber,contractType)
      await loanContractRepo.updateContractStatus(STATUS.IN_MANUAL_PROCESS, contractNumber);
      common.log("[WORKFLOW] Stop by manual dedup", contractNumber, 1);
      return false;
    }
  } else {
    common.log("[WORKFLOW] Stop by check manual dedup failed", contractNumber, 1);
    return false;
  }
}

async function checkManualDedup(req, res) {
  try {
    const data = req.body;
    const contract_number = data.contract_number;
    const cust_id = data.cust_id;
    const act = parseInt(data.act);

    let contractData = await loanContractRepo.getPartnerCodeAndProductCode(contract_number);

    let workflowBody = {
      contract_number: contract_number,
      partner_code: contractData.partner_code,
      product_code: contractData.product_code,
    };

    if (act === CRM.MANUAL_DEDUP.MERGED || act === CRM.MANUAL_DEDUP.NEW) {
      await loanContractRepo.updateCustId(cust_id, contract_number);
      await crmService.createCustomerService(contract_number);
      workflowBody.currentTask = MANUAL_TASK_CODE.DEDUP_MANUAL.DEDUP_APPROVE;
      // routing(workflowBody)
      return res.status(200).json({
        code: 1,
        msg: "Create dedup successfully.",
      });
    } else if (act === CRM.MANUAL_DEDUP.CANCELLED) {
      loanContractRepo.updateContractStatus(STATUS.CANCELLED, contract_number);
      workflowBody.currentTask = MANUAL_TASK_CODE.DEDUP_MANUAL.DEDUP_CANCEL;
      //routing(workflowBody)
      return res.status(200).json({
        code: 1,
        msg: "Cancel dedup successfully.",
      });
    } else if (act === CRM.MANUAL_DEDUP.FRAUD) {
      loanContractRepo.updateContractStatus(STATUS.REFUSED, contract_number);
      workflowBody.currentTask = MANUAL_TASK_CODE.DEDUP_MANUAL.DEDUP_CANCEL;
      //routing(workflowBody)
      return res.status(200).json({
        code: 1,
        msg: "Fraud dedup successfully.",
      });
    }
  } catch (error) {
    common.log(`[${SERVICE_NAME.CRM_DEDUP}] Error while update dedup customer: ${error.message}`);
    return res.status(500).json({
      code: -1,
      msg: `Manual dedup error : ${error.message}`,
    });
  }
}

async function checkDedupMcV2(body) {
  try {
    const contractNumber = body.contract_number;
    let checkDedupBody = {
      contractNumber: body.contract_number,
      phoneNumber: body.phone_number1,
      fullName: body.cust_full_name,
      idNumber: body.id_number,
      idIssueDt: dateHelper.convertYMD2DMY(body.id_issue_dt),
      birthDate: dateHelper.convertYMD2DMY(body.birth_date),
      gender: body.gender,
    };
    const dedupUrl = global.config.basic.crmService[config.env] + global.config.data.crmService.dedup;
    const dedupRs = await common.postApiV2(dedupUrl, checkDedupBody);
    loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.CRM, SERVICE_NAME.CRM_DEDUP, checkDedupBody, dedupRs);
    if (dedupRs.status == 200) {
      const dedupData = dedupRs.data;
      const custId = dedupData.data[0].custId;
      await loanContractRepo.updateCustId(custId, contractNumber);
      return custId;
    }
    return true;
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function checkDedup(req, res) {
  let currentTaskCode = "MC_CRE_DED";
  const statusCode = "KH02";
  try {
    let data = req.body.data;
    const contractNumber = data.contractNumber;
    const poolWrite = global.poolWrite;
    const config = global.config;
    // let body = {
    //   contractNumber: contractNumber,
    //   phoneNumber: data.phoneNumber,
    //   fullName: data.customerName,
    //   idNumber: data.identityCardId,
    //   idIssueDt: dateHelper.convertYMD2DMY(data.issueDate),
    //   birthDate: dateHelper.convertYMD2DMY(data.dateOfBirth),
    //   gender: data.gender,
    //   contractType: CONTRACT_TYPE.CREDIT_LINE,
    // };

    utils.saveStatus(poolWrite, contractNumber, statusCode);

    // const envType = req.config.data.env.wf_uri;
    // let wf_lb;
    // if (envType == "local") {
    //   wf_lb = "http://localhost:1001";
    // }

    // let uri = req.config.data.crmService.dedup;
    // let url = config.basic.crmService[config.env] + uri;

    let workflowUri = req.config.data.workflow.uri;
    let workflowUrl = config.basic.wfMcCredit[config.env] + workflowUri;

    const dedupRs = await checkDedupCash(contractNumber);
    if (dedupRs == true) {
      let flowData = req.body;
      //flowData.data.dedupResult = dedupData
      flowData.current_task_code = currentTaskCode;
      await common.postApiV2(workflowUrl, flowData);
    } else {
      await loanContractRepo.getLoanContract(contractNumber);
      // const contractType = loanData?.contract_type;
      // await pushTaskMc('CE',contractNumber,contractType)
      await loanContractRepo.updateContractStatus(STATUS.IN_MANUAL_PROCESS, contractNumber);
      common.log("[WORKFLOW] Stop by manual dedup", contractNumber, 1);
    }

    return res.status(200).json({
      msg: "check dedup",
      code: 1,
    });
    // let data = req.body.data
    // const contractNumber = data.contractNumber
    // const poolWrite = global.poolWrite
    // const config = global.config
    // let body = {
    // 	"contractNumber" : contractNumber,
    //     "phoneNumber": data.phoneNumber,
    //     "fullName": data.customerName,
    //     "idNumber": data.identityCardId,
    //     "idIssueDt": dateHelper.convertYMD2DMY(data.issueDate),
    //     "birthDate": dateHelper.convertYMD2DMY(data.dateOfBirth),
    //     "gender" : data.gender,
    //     contractType : CONTRACT_TYPE.CREDIT_LINE
    // }

    // utils.saveStatus(poolWrite,contractNumber,statusCode)

    // const envType = req.config.data.env.wf_uri;
    // let wf_lb ;
    // if(envType=='local') {
    // 	wf_lb = "http://localhost:1001"
    // }

    // let uri = req.config.data.crmService.dedup;
    // let url = config.basic.crmService[config.env] + uri

    // let workflowUri = req.config.data.workflow.uri;
    // let workflowUrl = config.basic.wfMcCredit[config.env] + workflowUri
    // const dedupRs = await  common.postApiV2(url,body)
    // if(dedupRs.status == 200) {
    //     const dedupData = dedupRs.data
    //     const custId = dedupData.data[0].custId
    // 	let flowData = req.body
    // 	flowData.data.dedupResult = dedupData
    // 	flowData.current_task_code = currentTaskCode
    //     await loanContractRepo.updateCustId(custId,contractNumber)
    //     common.postApiV2(workflowUrl,flowData)
    // }
    // return res.status(200).json({
    // 	"msg" : "check dedup",
    // 	"code" : 1
    // })
  } catch (error) {
    console.log(error);
    return res.status(500).json({
      msg: "service error",
      code: -1,
    });
  }
}

async function checkDedupSme(body) {
  try {
    const contractNumber = body;
    const loanData = await loanContractRepo.getLoanContract(contractNumber);
    const contractType = loanData?.contract_type;
    let checkDedupBody = {
      isInsert: true,
      contractNumber: body,
      phoneNumber: loanData?.sme_phone_number,
      fullName: loanData?.sme_representation_name,
      idNumber: loanData?.sme_representation_id,
      chanel: "LOS",
      bussinessCode: loanData?.registration_number, //mã số doanh nghiệp
      tax: loanData?.sme_tax_id,
      companyType: "E", //E: SME, I: hộ kinh doanh
      companyName: loanData?.sme_name,
      productType: "CASH",
      custType: "E", //E: SME, I: hộ kinh doanh
    };

    const dedupUrl = global.config.basic.crmService[config.env] + global.config.data.crmService.dedupSme;
    const dedupRs = await common.postApiV2(dedupUrl, checkDedupBody);
    loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.CRM, SERVICE_NAME.CRM_DEDUP, checkDedupBody, dedupRs);

    if (dedupRs.data.code == 0) {
      const dedupData = dedupRs.data;
      if (dedupData.data.statusDedup !== "MD") {
        const custId = dedupData.data.custId;
        await loanContractRepo.updateCustId(custId, contractNumber);
        await crmService.createCustomerService(contractNumber);
        return true;
      } else {
        await pushTaskMc("CE", contractNumber, contractType);
        await loanContractRepo.updateContractStatus(STATUS.IN_MANUAL_PROCESS, contractNumber);
        common.log("[WORKFLOW] Stop by manual dedup", contractNumber, 1);
        return false;
      }
    } else {
      common.log("[WORKFLOW] Stop by check dedup sme failed", contractNumber, 1);
      return false;
    }
  } catch (err) {
    console.log(err);
    return false;
  }
}

async function saveUpdateCusLog(req, body) {
  const crmUri = "/crm/v1/customer/ref-individual";
  const crmUrl = global.config.basic.crmService[config.env] + crmUri;
  const { uiid, token } = req.headers;
  const headers = { uiid, token };
  const rs = await common.postApiV2(crmUrl, body, headers);
  return rs?.data;
}

const checkDedupEnterpriseV3 = async (contractNumber) => {
  try {
    const loan = await loanContractRepo.findOneMisaLoanContract(contractNumber);
    const customer = loan?.loan_customer;
    if (!customer || !customer.registration_number) {
      throw Error(`find loan error: ${contractNumber}`);
    }
    const representation = loan.loan_customer_representations[0];
    let checkDedupBody = {
      requestId: uuid.v4(),
      contractNumber: contractNumber,
      phoneNumber: representation.phone_number,
      fullName: representation?.full_name,
      idNumber: representation?.id_number,
      chanel: "LOS",
      bussinessCode: customer.registration_number, //mã số doanh nghiệp
      tax: customer.tax_id,
      companyType: "E", //E: SME, I: hộ kinh doanh
      companyName: customer.company_name,
    };

    const dedupUrl = global.config.basic.crmService[config.env] + global.config.data.crmService.dedupEnterpriseV3;
    const dedupRs = await common.postApiV2(dedupUrl, checkDedupBody);
    loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.CRM, SERVICE_NAME.CRM_DEDUP, checkDedupBody, dedupRs);

    if (dedupRs.data.code == 0) {
      const dedupData = dedupRs.data;
      if (dedupData.data.statusDedup !== "MD") {
        const custId = dedupData.data.custId;
        await loanContractRepo.updateCustId(custId, contractNumber);
        // await crmService.createCustomerService(contractNumber)
        return true;
      }
    } else {
      common.log("[checkDedupEnterpriseV3] failed", contractNumber, 1);
      return false;
    }
  } catch (err) {
    console.error(err);
    return false;
  }
};

const checkNfcCache = async ({ idNumber, issueDate }) => {
  try {
    const c06Rs = await checkNfcCacheApi({ idNumber, issueDate });
    return c06Rs;
  } catch (error) {
    console.log(`[CRM-SERVICE][checkC06Cache] idNumber ${idNumber}, issueDate ${issueDate} error ${error}`);
    throw error;
  }
};

const checkDedupEnterpriseFD = async (payload) => {
  const { contract_number } = payload;
  try {
    let resultWorkflow = false;
    const loan = await loanContractRepo.getLoanContract(contract_number);
    if (!loan || !loan.registration_number) {
      throw Error(`find loan error: ${contract_number}`);
    }

    
   let checkDedupBody = {
     requestId: loan.request_id,
     businessCode: loan.registration_number,
     tax: loan.sme_tax_id,
     taxCode: loan.sme_tax_id,
     contractNumber: contract_number,
     phoneNumber: loan.sme_representation_phone_number,
     fullName: loan.sme_representation_name,
     idNumber: loan.sme_representation_id,
     chanel: "LOS",
     bussinessCode: loan.registration_cert_no || loan.registration_number, //mã số doanh nghiệp
     companyType: "E",
     companyName: loan.sme_name,
   };
   

    const dedupUrl = global.config.basic.crmService[config.env] + global.config.data.crmService?.dedupAndCreateEnterprise;
    const dedupRs = await common.postApiV2(dedupUrl, checkDedupBody);
    loggingRepo.saveStepLog(contract_number, SERVICE_NAME.CRM, SERVICE_NAME.CRM_DEDUP, checkDedupBody, dedupRs);

    if (dedupRs.data.code == 0) {
      resultWorkflow = true;
      const dedupData = dedupRs.data;
      const statusDedup = dedupData.data.statusDedup;

      await loanContractRepo.updateCustId(dedupData.data.custId, contract_number);

      if (statusDedup == "FD" || statusDedup == "PD") {
        common.log("[WORKFLOW] continue by Business code or tax code already exists in the system -> go next step", contract_number);
        actionAuditService.saveCaseHistoryActionAudit(contract_number, CASE_STATUS.CHECKED_DEDUPLICATION.STEP_CODE, CASE_STATUS.CHECKED_DEDUPLICATION.ACTION.AUTO_MERG, contract_number);
      } else {
        actionAuditService.saveCaseHistoryActionAudit(contract_number, CASE_STATUS.CHECKED_DEDUPLICATION.STEP_CODE, CASE_STATUS.CHECKED_DEDUPLICATION.ACTION.NEW, contract_number);
      }
    } else {
      common.log("[checkDedupEnterprise] failed", contract_number, 1);
      actionAuditService.saveCaseHistoryActionAudit(contract_number, CASE_STATUS.CHECKED_DEDUPLICATION.STEP_CODE, CASE_STATUS.CHECKED_DEDUPLICATION.ACTION.FAILED, contract_number);
      return false;
    }

    return resultWorkflow;
  } catch (error) {
    console.log(`[CRM][checkDedupEnterpriseEFD] contract number :${contract_number}, error ${error}`);
    return false;
  }
};

const checkDedupEnterprise = async (contractNumber) => {
  try {
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan || !loan.registration_number) {
      throw Error(`find loan error: ${contractNumber}`);
    }
    let checkDedupBody = {
      requestId: uuid.v4(),
      contractNumber: contractNumber,
      phoneNumber: loan.sme_representation_phone_number,
      fullName: loan.sme_representation_name,
      idNumber: loan.sme_representation_id,
      chanel: "LOS",
      bussinessCode: loan.registration_number ?? loan.sme_tax_id, //mã số doanh nghiệp
      tax: loan.sme_tax_id,
      companyType: "E", //E: SME, I: hộ kinh doanh
      companyName: loan.sme_name,
    };

    const dedupUrl = global.config.basic.crmService[config.env] + global.config.data.crmService.dedupEnterpriseV3;
    const dedupRs = await common.postApiV2(dedupUrl, checkDedupBody);
    loggingRepo.saveStepLog(contractNumber, SERVICE_NAME.CRM, SERVICE_NAME.CRM_DEDUP, checkDedupBody, dedupRs);

    if (dedupRs.data.code == 0) {
      const dedupData = dedupRs.data;
      const statusDedup = dedupData.data.statusDedup;
      if (statusDedup !== "MD") {
        const custId = dedupData.data.custId;
        await loanContractRepo.updateCustId(custId, contractNumber);
        let actionCode = "";
        if (["FD", "PD"].includes(statusDedup)) {
          actionCode = CASE_STATUS.CHECKED_DEDUPLICATION.ACTION.AUTO_MERG;
        } else if (statusDedup == "OD") {
          actionCode = CASE_STATUS.CHECKED_DEDUPLICATION.ACTION.NEW;
        }
        actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECKED_DEDUPLICATION.STEP_CODE, actionCode, contractNumber);
        return true;
      }
    } else {
      common.log("[checkDedupEnterprise] failed", contractNumber, 1);
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.CHECKED_DEDUPLICATION.STEP_CODE, CASE_STATUS.CHECKED_DEDUPLICATION.ACTION.FAILED, contractNumber);
      return false;
    }
  } catch (err) {
    console.error(err);
    return false;
  }
};

const checkDedupEnterpriseGw = async (body) => {
  const contractNumber = body.contract_number;
  try {
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan || !loan.registration_number) {
      throw Error(`find loan error: ${contractNumber}`);
    }
    return await checkDedupEnterprise(contractNumber);
  } catch (error) {
    console.log(`[CRM][checkDedupEnterpriseGw] contract number :${contractNumber}, error ${error}`);
    return false;
  }
};

const createMerchantLimitGw = async (body) => {
  const contractNumber = body.contract_number;
  try {
    const loan = await loanContractRepo.getLoanContract(contractNumber);
    if (!loan || !loan.id) {
      console.log(`ERROR | ${contractNumber}| ${new Date()} | createMerchantLimitGw | loan not found`);
      return false;
    }
    const result = await crmService.createCustomerService(contractNumber);
    if (!result) {
      actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.MERCHANT_LIMIT_CREATE.STEP_CODE, CASE_STATUS.MERCHANT_LIMIT_CREATE.ACTION.FAILED, contractNumber);
      return false;
    }
    actionAuditService.saveCaseHistoryActionAudit(contractNumber, CASE_STATUS.MERCHANT_LIMIT_CREATE.STEP_CODE, CASE_STATUS.MERCHANT_LIMIT_CREATE.ACTION.SUCCESS, contractNumber);
    return true;
  } catch (error) {
    console.log(`[CRM][createMerchantLimitGw] contract number :${contractNumber}, error ${error}`);
    return false;
  }
};

const createKunnGw = async (body) => {
  const kunnId = body.kunnId;
  return await crmService.createCustomerServiceKunn(kunnId);
};

module.exports = {
  checkDedupCash,
  checkManualDedup,
  checkDedup,
  checkDedupMcV2,
  checkDedupSme,
  saveUpdateCusLog,
  checkDedupEnterpriseV3,
  checkNfcCache,
  checkDedupEnterpriseGw,
  createMerchantLimitGw,
  checkDedupEnterpriseFD,
  createKunnGw,
};
